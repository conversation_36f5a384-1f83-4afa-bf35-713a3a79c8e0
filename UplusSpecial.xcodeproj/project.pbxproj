// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		099BFE0D2A60E38800FC412B /* UpUplusCommonSchemeModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 099BFE0B2A60E38800FC412B /* UpUplusCommonSchemeModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		099BFE0E2A60E38800FC412B /* UpUplusCommonSchemeModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 099BFE0C2A60E38800FC412B /* UpUplusCommonSchemeModule.m */; };
		14FE343F2988A3BF00DAE099 /* UPMockCrashViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 14FE343D2988A3BF00DAE099 /* UPMockCrashViewController.h */; };
		14FE34402988A3BF00DAE099 /* UPMockCrashViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 14FE343E2988A3BF00DAE099 /* UPMockCrashViewController.m */; };
		2C0D2414295BE087004B67E3 /* UPEventTraceConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C0D2412295BE087004B67E3 /* UPEventTraceConfig.m */; };
		2C0D2415295BE087004B67E3 /* UPEventTraceConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C0D2413295BE087004B67E3 /* UPEventTraceConfig.h */; };
		2CDA8EDB291B831E00844E1E /* InitBaseKit.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CDA8ED9291B831D00844E1E /* InitBaseKit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2CDA8EDC291B831E00844E1E /* InitBaseKit.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CDA8EDA291B831D00844E1E /* InitBaseKit.m */; };
		2CDA8EE8291B837400844E1E /* ResourceTrackerIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CDA8EE3291B837400844E1E /* ResourceTrackerIMP.h */; };
		2CDA8EE9291B837400844E1E /* ResourceTrackerIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CDA8EE4291B837400844E1E /* ResourceTrackerIMP.m */; };
		2CDA8EEA291B837400844E1E /* PushMessageHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CDA8EE6291B837400844E1E /* PushMessageHandler.m */; };
		2CDA8EEB291B837400844E1E /* PushMessageHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CDA8EE7291B837400844E1E /* PushMessageHandler.h */; };
		2CFECE7329751B8800C337CA /* UPUpgradeConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CFECE7129751B8700C337CA /* UPUpgradeConfig.m */; };
		2CFECE7429751B8800C337CA /* UPUpgradeConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CFECE7229751B8700C337CA /* UPUpgradeConfig.h */; };
		4FB14B662E49D36500B534D3 /* WashProgramStartIntercepter.h in Headers */ = {isa = PBXBuildFile; fileRef = 4FB14B642E49D36500B534D3 /* WashProgramStartIntercepter.h */; };
		4FB14B672E49D36500B534D3 /* WashProgramStartIntercepter.m in Sources */ = {isa = PBXBuildFile; fileRef = 4FB14B652E49D36500B534D3 /* WashProgramStartIntercepter.m */; };
		6768F91E2DF061C000D960AE /* GyOneKeyLoginLauncher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6768F91D2DF061C000D960AE /* GyOneKeyLoginLauncher.swift */; };
		6768F9202DF061DF00D960AE /* GyOneKeyLoginPatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6768F91F2DF061DF00D960AE /* GyOneKeyLoginPatcher.swift */; };
		6768F9222DF061F400D960AE /* GyOneKeyLoginModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6768F9212DF061F400D960AE /* GyOneKeyLoginModule.swift */; };
		67FE6EBA2D939CE200B3BA87 /* UP3DTouchModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 67FE6EB82D939CE200B3BA87 /* UP3DTouchModule.m */; };
		67FE6EBB2D939CE200B3BA87 /* UP3DTouchModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 67FE6EB72D939CE200B3BA87 /* UP3DTouchModule.h */; };
		848339352A8B90AD0039E79B /* UplusSpecialModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 848339322A8B90AD0039E79B /* UplusSpecialModule.swift */; };
		848F2CC02B04AC00004C65B8 /* UpLaunchTimeReportModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 848F2CBF2B04AC00004C65B8 /* UpLaunchTimeReportModule.swift */; };
		94BE895A291E39CE002B5167 /* OpenInstallConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE8958291E39CE002B5167 /* OpenInstallConfig.h */; };
		94BE895B291E39CE002B5167 /* OpenInstallConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE8959291E39CE002B5167 /* OpenInstallConfig.m */; };
		94BE899A29239A8A002B5167 /* InviteCodeViewModel_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE898A29239A8A002B5167 /* InviteCodeViewModel_special.m */; };
		94BE899B29239A8A002B5167 /* InviteCodePatcher_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE898B29239A8A002B5167 /* InviteCodePatcher_special.m */; };
		94BE899C29239A8A002B5167 /* UPMutableRequest_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE898D29239A8A002B5167 /* UPMutableRequest_special.m */; };
		94BE899D29239A8A002B5167 /* HTTPRequestManager_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE898E29239A8A002B5167 /* HTTPRequestManager_special.m */; };
		94BE899E29239A8A002B5167 /* AFHTTPRequestOperation_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE898F29239A8A002B5167 /* AFHTTPRequestOperation_special.m */; };
		94BE899F29239A8A002B5167 /* UPNetWorkHeader_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE899029239A8A002B5167 /* UPNetWorkHeader_special.m */; };
		94BE89A029239A8A002B5167 /* OHServerDefine_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899129239A8A002B5167 /* OHServerDefine_special.h */; };
		94BE89A129239A8A002B5167 /* HTTPRequestManager_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899229239A8A002B5167 /* HTTPRequestManager_special.h */; };
		94BE89A229239A8A002B5167 /* UPMutableRequest_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899329239A8A002B5167 /* UPMutableRequest_special.h */; };
		94BE89A329239A8A002B5167 /* UPNetWorkHeader_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899429239A8A002B5167 /* UPNetWorkHeader_special.h */; };
		94BE89A429239A8A002B5167 /* AFHTTPRequestOperation_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899529239A8A002B5167 /* AFHTTPRequestOperation_special.h */; };
		94BE89A529239A8A002B5167 /* InviteCodeService_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899629239A8A002B5167 /* InviteCodeService_special.h */; };
		94BE89A629239A8A002B5167 /* InviteCodeViewModel_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899729239A8A002B5167 /* InviteCodeViewModel_special.h */; };
		94BE89A729239A8A002B5167 /* InviteCodePatcher_special.h in Headers */ = {isa = PBXBuildFile; fileRef = 94BE899829239A8A002B5167 /* InviteCodePatcher_special.h */; };
		94BE89A829239A8A002B5167 /* InviteCodeService_special.m in Sources */ = {isa = PBXBuildFile; fileRef = 94BE899929239A8A002B5167 /* InviteCodeService_special.m */; };
		952630082A380ECC00864668 /* UpAuthFamilyPickerCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 952630062A380ECC00864668 /* UpAuthFamilyPickerCell.h */; };
		952630092A380ECC00864668 /* UpAuthFamilyPickerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 952630072A380ECC00864668 /* UpAuthFamilyPickerCell.m */; };
		9548DF712CD9EDBE007632DA /* UPServiceFloatViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF592CD9EDBE007632DA /* UPServiceFloatViewManager.m */; };
		9548DF722CD9EDBE007632DA /* UPServiceGrayUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF5A2CD9EDBE007632DA /* UPServiceGrayUtil.h */; };
		9548DF732CD9EDBF007632DA /* UPServiceGrayUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF5B2CD9EDBE007632DA /* UPServiceGrayUtil.m */; };
		9548DF742CD9EDBF007632DA /* UPServicePageUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF5C2CD9EDBE007632DA /* UPServicePageUtil.swift */; };
		9548DF752CD9EDBF007632DA /* UIViewController+XiaoU.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF5E2CD9EDBE007632DA /* UIViewController+XiaoU.h */; };
		9548DF762CD9EDBF007632DA /* UIViewController+XiaoU.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF5F2CD9EDBE007632DA /* UIViewController+XiaoU.m */; };
		9548DF772CD9EDBF007632DA /* UPServiceBadgeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF602CD9EDBE007632DA /* UPServiceBadgeView.swift */; };
		9548DF782CD9EDBF007632DA /* UPServiceFloatBubbleView.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF612CD9EDBE007632DA /* UPServiceFloatBubbleView.h */; };
		9548DF792CD9EDBF007632DA /* UPServiceFloatBubbleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF622CD9EDBE007632DA /* UPServiceFloatBubbleView.m */; };
		9548DF7A2CD9EDBF007632DA /* UPServiceFloatView.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF632CD9EDBE007632DA /* UPServiceFloatView.h */; };
		9548DF7B2CD9EDBF007632DA /* UPServiceFloatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF642CD9EDBE007632DA /* UPServiceFloatView.m */; };
		9548DF7C2CD9EDBF007632DA /* UPServiceFloatViewManager+Badge.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF652CD9EDBE007632DA /* UPServiceFloatViewManager+Badge.h */; };
		9548DF7D2CD9EDBF007632DA /* UPServiceFloatViewManager+Badge.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF662CD9EDBE007632DA /* UPServiceFloatViewManager+Badge.m */; };
		9548DF7E2CD9EDBF007632DA /* UPServiceFloatViewManager+Bubble.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF672CD9EDBE007632DA /* UPServiceFloatViewManager+Bubble.h */; };
		9548DF7F2CD9EDBF007632DA /* UPServiceFloatViewManager+Bubble.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF682CD9EDBE007632DA /* UPServiceFloatViewManager+Bubble.m */; };
		9548DF802CD9EDBF007632DA /* UPServiceFloatViewManager+Coordinate.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF692CD9EDBE007632DA /* UPServiceFloatViewManager+Coordinate.h */; };
		9548DF812CD9EDBF007632DA /* UPServiceFloatViewManager+Coordinate.m in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF6A2CD9EDBE007632DA /* UPServiceFloatViewManager+Coordinate.m */; };
		9548DF822CD9EDBF007632DA /* UPServiceEnableRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF6C2CD9EDBE007632DA /* UPServiceEnableRequest.swift */; };
		9548DF832CD9EDBF007632DA /* UPServiceEnableManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9548DF6E2CD9EDBE007632DA /* UPServiceEnableManager.swift */; };
		9548DF842CD9EDBF007632DA /* UPServiceFloatViewManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF6F2CD9EDBE007632DA /* UPServiceFloatViewManager.h */; };
		9548DF852CD9EDBF007632DA /* UPServiceConstant.h in Headers */ = {isa = PBXBuildFile; fileRef = 9548DF702CD9EDBE007632DA /* UPServiceConstant.h */; settings = {ATTRIBUTES = (Public, ); }; };
		954ABCC42CDC9682004E2767 /* UplusSpecial.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 954ABCC32CDC9682004E2767 /* UplusSpecial.bundle */; };
		955A88752A36F9E10066F538 /* UpAuthManager+Reporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 955A88732A36F9E10066F538 /* UpAuthManager+Reporter.h */; };
		955A88762A36F9E10066F538 /* UpAuthManager+Reporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 955A88742A36F9E10066F538 /* UpAuthManager+Reporter.m */; };
		955A88792A370CE50066F538 /* UpAuthViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 955A88772A370CE50066F538 /* UpAuthViewController.h */; };
		955A887A2A370CE50066F538 /* UpAuthViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 955A88782A370CE50066F538 /* UpAuthViewController.m */; };
		955A88862A37219C0066F538 /* UpAuthPageCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 955A88842A37219C0066F538 /* UpAuthPageCell.h */; };
		955A88872A37219C0066F538 /* UpAuthPageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 955A88852A37219C0066F538 /* UpAuthPageCell.m */; };
		955A888E2A3736360066F538 /* UpAuthFamilyPickerView.h in Headers */ = {isa = PBXBuildFile; fileRef = 955A888C2A3736360066F538 /* UpAuthFamilyPickerView.h */; };
		955A888F2A3736360066F538 /* UpAuthFamilyPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 955A888D2A3736360066F538 /* UpAuthFamilyPickerView.m */; };
		95686A6B2A208227001B0FD5 /* UpAuthModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 95686A692A208227001B0FD5 /* UpAuthModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95686A6C2A208227001B0FD5 /* UpAuthModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 95686A6A2A208227001B0FD5 /* UpAuthModule.m */; };
		95686A6F2A20852E001B0FD5 /* UpAuthManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 95686A6D2A20852E001B0FD5 /* UpAuthManager.h */; };
		95686A702A20852E001B0FD5 /* UpAuthManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 95686A6E2A20852E001B0FD5 /* UpAuthManager.m */; };
		95693CDE2DB8D65E00333759 /* FamilyInviteView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95693CDD2DB8D65E00333759 /* FamilyInviteView.swift */; };
		95A552EF2C8848EE00D0790D /* UIViewController+PageStay.h in Headers */ = {isa = PBXBuildFile; fileRef = 95A552ED2C8848EE00D0790D /* UIViewController+PageStay.h */; };
		95A552F02C8848EE00D0790D /* UIViewController+PageStay.m in Sources */ = {isa = PBXBuildFile; fileRef = 95A552EE2C8848EE00D0790D /* UIViewController+PageStay.m */; };
		95E858CD2A386DE200ECDFBE /* UpAuthCodeRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 95E858CB2A386DE200ECDFBE /* UpAuthCodeRequest.h */; };
		95E858CE2A386DE200ECDFBE /* UpAuthCodeRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 95E858CC2A386DE200ECDFBE /* UpAuthCodeRequest.m */; };
		95E95C202D804E74006A53D4 /* ModuleConfigure.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E95C1F2D804E74006A53D4 /* ModuleConfigure.swift */; };
		95E95C222D804E87006A53D4 /* ModuleInitializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E95C212D804E87006A53D4 /* ModuleInitializer.swift */; };
		9B6C5D452D8288A200BD3505 /* DeviceBindPatcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B6C5D442D8288A200BD3505 /* DeviceBindPatcher.swift */; };
		9BE7382F2DAF85BA00DC439C /* DeviceBindLauncher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BE7382E2DAF85BA00DC439C /* DeviceBindLauncher.swift */; };
		A5565BAA2E015346002665C8 /* AlertControllerTool.h in Headers */ = {isa = PBXBuildFile; fileRef = A5565BA82E015346002665C8 /* AlertControllerTool.h */; };
		A5565BAB2E015346002665C8 /* AlertControllerTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A5565BA92E015346002665C8 /* AlertControllerTool.m */; };
		D34A4CA228F1247500FEE94F /* UplusSpecial.h in Headers */ = {isa = PBXBuildFile; fileRef = D34A4CA128F1247500FEE94F /* UplusSpecial.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D362E0FF29791402006F1EEC /* UPFamilyFloatView.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E0FB29791402006F1EEC /* UPFamilyFloatView.m */; };
		D362E10029791402006F1EEC /* UPFamilyFloatLauncher.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E0FC29791402006F1EEC /* UPFamilyFloatLauncher.h */; };
		D362E10129791402006F1EEC /* UPFamilyFloatLauncher.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E0FD29791402006F1EEC /* UPFamilyFloatLauncher.m */; };
		D362E10229791402006F1EEC /* UPFamilyFloatView.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E0FE29791402006F1EEC /* UPFamilyFloatView.h */; };
		D362E1062979149A006F1EEC /* JoinFamilyAlertPatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E1042979149A006F1EEC /* JoinFamilyAlertPatcher.m */; };
		D362E1072979149A006F1EEC /* JoinFamilyAlertPatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E1052979149A006F1EEC /* JoinFamilyAlertPatcher.h */; };
		D362E1142979160F006F1EEC /* AFHTTPRequestOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E10E2979160F006F1EEC /* AFHTTPRequestOperation.h */; };
		D362E1152979160F006F1EEC /* UPMutableRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E10F2979160F006F1EEC /* UPMutableRequest.m */; };
		D362E1162979160F006F1EEC /* HTTPRequestManager.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E1102979160F006F1EEC /* HTTPRequestManager.h */; };
		D362E1172979160F006F1EEC /* AFHTTPRequestOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E1112979160F006F1EEC /* AFHTTPRequestOperation.m */; };
		D362E1182979160F006F1EEC /* UPMutableRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E1122979160F006F1EEC /* UPMutableRequest.h */; };
		D362E1192979160F006F1EEC /* HTTPRequestManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E1132979160F006F1EEC /* HTTPRequestManager.m */; };
		D362E12B29791674006F1EEC /* LaunchTimeLogsController.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E12529791674006F1EEC /* LaunchTimeLogsController.h */; };
		D362E12C29791674006F1EEC /* DebuggerConfigViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E12629791674006F1EEC /* DebuggerConfigViewController.m */; };
		D362E12D29791674006F1EEC /* PageTraceCacheViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E12729791674006F1EEC /* PageTraceCacheViewController.m */; };
		D362E12E29791674006F1EEC /* LaunchTimeLogsController.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E12829791674006F1EEC /* LaunchTimeLogsController.m */; };
		D362E12F29791674006F1EEC /* DebuggerConfigViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E12929791674006F1EEC /* DebuggerConfigViewController.h */; };
		D362E13029791674006F1EEC /* PageTraceCacheViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E12A29791674006F1EEC /* PageTraceCacheViewController.h */; };
		D362E13629791689006F1EEC /* SNSMhManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E13229791689006F1EEC /* SNSMhManager.m */; };
		D362E13729791689006F1EEC /* SNSModule.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E13329791689006F1EEC /* SNSModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D362E13829791689006F1EEC /* SNSMhManager.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E13429791689006F1EEC /* SNSMhManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D362E13929791689006F1EEC /* SNSModule.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E13529791689006F1EEC /* SNSModule.m */; };
		D362E13D29791710006F1EEC /* UserCenterAddStatusPatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E13B29791710006F1EEC /* UserCenterAddStatusPatcher.m */; };
		D362E13E29791710006F1EEC /* UserCenterAddStatusPatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E13C29791710006F1EEC /* UserCenterAddStatusPatcher.h */; };
		D362E14129791724006F1EEC /* UIViewController+SetStatusBarStyle.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E13F29791724006F1EEC /* UIViewController+SetStatusBarStyle.h */; };
		D362E14229791724006F1EEC /* UIViewController+SetStatusBarStyle.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E14029791724006F1EEC /* UIViewController+SetStatusBarStyle.m */; };
		D362E14629791749006F1EEC /* AnalyticsModule.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E14429791749006F1EEC /* AnalyticsModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D362E14729791749006F1EEC /* AnalyticsModule.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E14529791749006F1EEC /* AnalyticsModule.m */; };
		D362E15B297918A1006F1EEC /* UPLaunchTimeRecorder.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E159297918A1006F1EEC /* UPLaunchTimeRecorder.m */; };
		D362E15C297918A1006F1EEC /* UPLaunchTimeRecorder.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E15A297918A1006F1EEC /* UPLaunchTimeRecorder.h */; };
		D362E164297918AE006F1EEC /* ASADataModel.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E15E297918AE006F1EEC /* ASADataModel.h */; };
		D362E165297918AE006F1EEC /* MBMainBoxModule.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E15F297918AE006F1EEC /* MBMainBoxModule.m */; };
		D362E166297918AE006F1EEC /* MainboxServerProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E160297918AE006F1EEC /* MainboxServerProtocol.m */; };
		D362E167297918AE006F1EEC /* MBMainBoxModule.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E161297918AE006F1EEC /* MBMainBoxModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D362E168297918AE006F1EEC /* ASADataModel.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E162297918AE006F1EEC /* ASADataModel.m */; };
		D362E169297918AE006F1EEC /* MainboxServerProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E163297918AE006F1EEC /* MainboxServerProtocol.h */; };
		D362E16C297918E6006F1EEC /* UPNetWorkHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E16A297918E6006F1EEC /* UPNetWorkHeader.m */; };
		D362E16D297918E6006F1EEC /* UPNetWorkHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E16B297918E6006F1EEC /* UPNetWorkHeader.h */; };
		D362E1AB29791EF8006F1EEC /* LGModule.h in Headers */ = {isa = PBXBuildFile; fileRef = D362E1A929791EF8006F1EEC /* LGModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D362E1AC29791EF8006F1EEC /* LGModule.m in Sources */ = {isa = PBXBuildFile; fileRef = D362E1AA29791EF8006F1EEC /* LGModule.m */; };
		D3F55D0B28F143F4006B32B9 /* state in Resources */ = {isa = PBXBuildFile; fileRef = D3F55D0328F143F3006B32B9 /* state */; };
		D3F55D0C28F143F4006B32B9 /* MainLibModule.h in Headers */ = {isa = PBXBuildFile; fileRef = D3F55D0528F143F3006B32B9 /* MainLibModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D3F55D0E28F143F4006B32B9 /* MainLibModule.m in Sources */ = {isa = PBXBuildFile; fileRef = D3F55D0728F143F3006B32B9 /* MainLibModule.m */; };
		D3F55D2C28F14702006B32B9 /* UplusSpecial.podspec in Resources */ = {isa = PBXBuildFile; fileRef = D3F55D2B28F14702006B32B9 /* UplusSpecial.podspec */; };
		E9C235671F988F1EE23BE7F1 /* libPods-UplusSpecial.a in Frameworks */ = {isa = PBXBuildFile; fileRef = CD913E81A9319EB1F839EDC7 /* libPods-UplusSpecial.a */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		08AB9A6825A7E3867C09AF2E /* Pods-UplusSpecial.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UplusSpecial.debug.xcconfig"; path = "Target Support Files/Pods-UplusSpecial/Pods-UplusSpecial.debug.xcconfig"; sourceTree = "<group>"; };
		099BFE0B2A60E38800FC412B /* UpUplusCommonSchemeModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUplusCommonSchemeModule.h; sourceTree = "<group>"; };
		099BFE0C2A60E38800FC412B /* UpUplusCommonSchemeModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUplusCommonSchemeModule.m; sourceTree = "<group>"; };
		14FE343D2988A3BF00DAE099 /* UPMockCrashViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPMockCrashViewController.h; sourceTree = "<group>"; };
		14FE343E2988A3BF00DAE099 /* UPMockCrashViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPMockCrashViewController.m; sourceTree = "<group>"; };
		2C0D2412295BE087004B67E3 /* UPEventTraceConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPEventTraceConfig.m; sourceTree = "<group>"; };
		2C0D2413295BE087004B67E3 /* UPEventTraceConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPEventTraceConfig.h; sourceTree = "<group>"; };
		2CDA8ED9291B831D00844E1E /* InitBaseKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InitBaseKit.h; sourceTree = "<group>"; };
		2CDA8EDA291B831D00844E1E /* InitBaseKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InitBaseKit.m; sourceTree = "<group>"; };
		2CDA8EE3291B837400844E1E /* ResourceTrackerIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ResourceTrackerIMP.h; sourceTree = "<group>"; };
		2CDA8EE4291B837400844E1E /* ResourceTrackerIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ResourceTrackerIMP.m; sourceTree = "<group>"; };
		2CDA8EE6291B837400844E1E /* PushMessageHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PushMessageHandler.m; sourceTree = "<group>"; };
		2CDA8EE7291B837400844E1E /* PushMessageHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PushMessageHandler.h; sourceTree = "<group>"; };
		2CFECE7129751B8700C337CA /* UPUpgradeConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUpgradeConfig.m; sourceTree = "<group>"; };
		2CFECE7229751B8700C337CA /* UPUpgradeConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUpgradeConfig.h; sourceTree = "<group>"; };
		4FB14B642E49D36500B534D3 /* WashProgramStartIntercepter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WashProgramStartIntercepter.h; sourceTree = "<group>"; };
		4FB14B652E49D36500B534D3 /* WashProgramStartIntercepter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WashProgramStartIntercepter.m; sourceTree = "<group>"; };
		6768F91D2DF061C000D960AE /* GyOneKeyLoginLauncher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GyOneKeyLoginLauncher.swift; sourceTree = "<group>"; };
		6768F91F2DF061DF00D960AE /* GyOneKeyLoginPatcher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GyOneKeyLoginPatcher.swift; sourceTree = "<group>"; };
		6768F9212DF061F400D960AE /* GyOneKeyLoginModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GyOneKeyLoginModule.swift; sourceTree = "<group>"; };
		67FE6EB72D939CE200B3BA87 /* UP3DTouchModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UP3DTouchModule.h; sourceTree = "<group>"; };
		67FE6EB82D939CE200B3BA87 /* UP3DTouchModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UP3DTouchModule.m; sourceTree = "<group>"; };
		848339322A8B90AD0039E79B /* UplusSpecialModule.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UplusSpecialModule.swift; sourceTree = "<group>"; };
		848F2CBF2B04AC00004C65B8 /* UpLaunchTimeReportModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpLaunchTimeReportModule.swift; sourceTree = "<group>"; };
		93BCE691EB990EEB6B65ED54 /* Pods-UplusSpecial.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UplusSpecial.release.xcconfig"; path = "Target Support Files/Pods-UplusSpecial/Pods-UplusSpecial.release.xcconfig"; sourceTree = "<group>"; };
		94BE8958291E39CE002B5167 /* OpenInstallConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OpenInstallConfig.h; sourceTree = "<group>"; };
		94BE8959291E39CE002B5167 /* OpenInstallConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OpenInstallConfig.m; sourceTree = "<group>"; };
		94BE898A29239A8A002B5167 /* InviteCodeViewModel_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InviteCodeViewModel_special.m; sourceTree = "<group>"; };
		94BE898B29239A8A002B5167 /* InviteCodePatcher_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InviteCodePatcher_special.m; sourceTree = "<group>"; };
		94BE898D29239A8A002B5167 /* UPMutableRequest_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPMutableRequest_special.m; sourceTree = "<group>"; };
		94BE898E29239A8A002B5167 /* HTTPRequestManager_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HTTPRequestManager_special.m; sourceTree = "<group>"; };
		94BE898F29239A8A002B5167 /* AFHTTPRequestOperation_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFHTTPRequestOperation_special.m; sourceTree = "<group>"; };
		94BE899029239A8A002B5167 /* UPNetWorkHeader_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPNetWorkHeader_special.m; sourceTree = "<group>"; };
		94BE899129239A8A002B5167 /* OHServerDefine_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OHServerDefine_special.h; sourceTree = "<group>"; };
		94BE899229239A8A002B5167 /* HTTPRequestManager_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HTTPRequestManager_special.h; sourceTree = "<group>"; };
		94BE899329239A8A002B5167 /* UPMutableRequest_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPMutableRequest_special.h; sourceTree = "<group>"; };
		94BE899429239A8A002B5167 /* UPNetWorkHeader_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPNetWorkHeader_special.h; sourceTree = "<group>"; };
		94BE899529239A8A002B5167 /* AFHTTPRequestOperation_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFHTTPRequestOperation_special.h; sourceTree = "<group>"; };
		94BE899629239A8A002B5167 /* InviteCodeService_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InviteCodeService_special.h; sourceTree = "<group>"; };
		94BE899729239A8A002B5167 /* InviteCodeViewModel_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InviteCodeViewModel_special.h; sourceTree = "<group>"; };
		94BE899829239A8A002B5167 /* InviteCodePatcher_special.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InviteCodePatcher_special.h; sourceTree = "<group>"; };
		94BE899929239A8A002B5167 /* InviteCodeService_special.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InviteCodeService_special.m; sourceTree = "<group>"; };
		952630062A380ECC00864668 /* UpAuthFamilyPickerCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthFamilyPickerCell.h; sourceTree = "<group>"; };
		952630072A380ECC00864668 /* UpAuthFamilyPickerCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthFamilyPickerCell.m; sourceTree = "<group>"; };
		9548DF592CD9EDBE007632DA /* UPServiceFloatViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPServiceFloatViewManager.m; sourceTree = "<group>"; };
		9548DF5A2CD9EDBE007632DA /* UPServiceGrayUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPServiceGrayUtil.h; sourceTree = "<group>"; };
		9548DF5B2CD9EDBE007632DA /* UPServiceGrayUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPServiceGrayUtil.m; sourceTree = "<group>"; };
		9548DF5C2CD9EDBE007632DA /* UPServicePageUtil.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UPServicePageUtil.swift; sourceTree = "<group>"; };
		9548DF5E2CD9EDBE007632DA /* UIViewController+XiaoU.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+XiaoU.h"; sourceTree = "<group>"; };
		9548DF5F2CD9EDBE007632DA /* UIViewController+XiaoU.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+XiaoU.m"; sourceTree = "<group>"; };
		9548DF602CD9EDBE007632DA /* UPServiceBadgeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UPServiceBadgeView.swift; sourceTree = "<group>"; };
		9548DF612CD9EDBE007632DA /* UPServiceFloatBubbleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPServiceFloatBubbleView.h; sourceTree = "<group>"; };
		9548DF622CD9EDBE007632DA /* UPServiceFloatBubbleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPServiceFloatBubbleView.m; sourceTree = "<group>"; };
		9548DF632CD9EDBE007632DA /* UPServiceFloatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPServiceFloatView.h; sourceTree = "<group>"; };
		9548DF642CD9EDBE007632DA /* UPServiceFloatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPServiceFloatView.m; sourceTree = "<group>"; };
		9548DF652CD9EDBE007632DA /* UPServiceFloatViewManager+Badge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UPServiceFloatViewManager+Badge.h"; sourceTree = "<group>"; };
		9548DF662CD9EDBE007632DA /* UPServiceFloatViewManager+Badge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UPServiceFloatViewManager+Badge.m"; sourceTree = "<group>"; };
		9548DF672CD9EDBE007632DA /* UPServiceFloatViewManager+Bubble.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UPServiceFloatViewManager+Bubble.h"; sourceTree = "<group>"; };
		9548DF682CD9EDBE007632DA /* UPServiceFloatViewManager+Bubble.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UPServiceFloatViewManager+Bubble.m"; sourceTree = "<group>"; };
		9548DF692CD9EDBE007632DA /* UPServiceFloatViewManager+Coordinate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UPServiceFloatViewManager+Coordinate.h"; sourceTree = "<group>"; };
		9548DF6A2CD9EDBE007632DA /* UPServiceFloatViewManager+Coordinate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UPServiceFloatViewManager+Coordinate.m"; sourceTree = "<group>"; };
		9548DF6C2CD9EDBE007632DA /* UPServiceEnableRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UPServiceEnableRequest.swift; sourceTree = "<group>"; };
		9548DF6E2CD9EDBE007632DA /* UPServiceEnableManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UPServiceEnableManager.swift; sourceTree = "<group>"; };
		9548DF6F2CD9EDBE007632DA /* UPServiceFloatViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPServiceFloatViewManager.h; sourceTree = "<group>"; };
		9548DF702CD9EDBE007632DA /* UPServiceConstant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPServiceConstant.h; sourceTree = "<group>"; };
		954ABCC32CDC9682004E2767 /* UplusSpecial.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = UplusSpecial.bundle; sourceTree = "<group>"; };
		955A88732A36F9E10066F538 /* UpAuthManager+Reporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UpAuthManager+Reporter.h"; sourceTree = "<group>"; };
		955A88742A36F9E10066F538 /* UpAuthManager+Reporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UpAuthManager+Reporter.m"; sourceTree = "<group>"; };
		955A88772A370CE50066F538 /* UpAuthViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthViewController.h; sourceTree = "<group>"; };
		955A88782A370CE50066F538 /* UpAuthViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthViewController.m; sourceTree = "<group>"; };
		955A88842A37219C0066F538 /* UpAuthPageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthPageCell.h; sourceTree = "<group>"; };
		955A88852A37219C0066F538 /* UpAuthPageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthPageCell.m; sourceTree = "<group>"; };
		955A888C2A3736360066F538 /* UpAuthFamilyPickerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthFamilyPickerView.h; sourceTree = "<group>"; };
		955A888D2A3736360066F538 /* UpAuthFamilyPickerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthFamilyPickerView.m; sourceTree = "<group>"; };
		95686A692A208227001B0FD5 /* UpAuthModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthModule.h; sourceTree = "<group>"; };
		95686A6A2A208227001B0FD5 /* UpAuthModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthModule.m; sourceTree = "<group>"; };
		95686A6D2A20852E001B0FD5 /* UpAuthManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthManager.h; sourceTree = "<group>"; };
		95686A6E2A20852E001B0FD5 /* UpAuthManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthManager.m; sourceTree = "<group>"; };
		95693CDD2DB8D65E00333759 /* FamilyInviteView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FamilyInviteView.swift; sourceTree = "<group>"; };
		95A552ED2C8848EE00D0790D /* UIViewController+PageStay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+PageStay.h"; sourceTree = "<group>"; };
		95A552EE2C8848EE00D0790D /* UIViewController+PageStay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+PageStay.m"; sourceTree = "<group>"; };
		95E858CB2A386DE200ECDFBE /* UpAuthCodeRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAuthCodeRequest.h; sourceTree = "<group>"; };
		95E858CC2A386DE200ECDFBE /* UpAuthCodeRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAuthCodeRequest.m; sourceTree = "<group>"; };
		95E95C1F2D804E74006A53D4 /* ModuleConfigure.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModuleConfigure.swift; sourceTree = "<group>"; };
		95E95C212D804E87006A53D4 /* ModuleInitializer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModuleInitializer.swift; sourceTree = "<group>"; };
		9B6C5D442D8288A200BD3505 /* DeviceBindPatcher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceBindPatcher.swift; sourceTree = "<group>"; };
		9BE7382E2DAF85BA00DC439C /* DeviceBindLauncher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceBindLauncher.swift; sourceTree = "<group>"; };
		A5565BA82E015346002665C8 /* AlertControllerTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AlertControllerTool.h; sourceTree = "<group>"; };
		A5565BA92E015346002665C8 /* AlertControllerTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AlertControllerTool.m; sourceTree = "<group>"; };
		CD913E81A9319EB1F839EDC7 /* libPods-UplusSpecial.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UplusSpecial.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		D34A4C9E28F1247500FEE94F /* UplusSpecial.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UplusSpecial.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D34A4CA128F1247500FEE94F /* UplusSpecial.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UplusSpecial.h; sourceTree = "<group>"; };
		D362E0FB29791402006F1EEC /* UPFamilyFloatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyFloatView.m; sourceTree = "<group>"; };
		D362E0FC29791402006F1EEC /* UPFamilyFloatLauncher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyFloatLauncher.h; sourceTree = "<group>"; };
		D362E0FD29791402006F1EEC /* UPFamilyFloatLauncher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyFloatLauncher.m; sourceTree = "<group>"; };
		D362E0FE29791402006F1EEC /* UPFamilyFloatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyFloatView.h; sourceTree = "<group>"; };
		D362E1042979149A006F1EEC /* JoinFamilyAlertPatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JoinFamilyAlertPatcher.m; sourceTree = "<group>"; };
		D362E1052979149A006F1EEC /* JoinFamilyAlertPatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JoinFamilyAlertPatcher.h; sourceTree = "<group>"; };
		D362E10E2979160F006F1EEC /* AFHTTPRequestOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFHTTPRequestOperation.h; sourceTree = "<group>"; };
		D362E10F2979160F006F1EEC /* UPMutableRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPMutableRequest.m; sourceTree = "<group>"; };
		D362E1102979160F006F1EEC /* HTTPRequestManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HTTPRequestManager.h; sourceTree = "<group>"; };
		D362E1112979160F006F1EEC /* AFHTTPRequestOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFHTTPRequestOperation.m; sourceTree = "<group>"; };
		D362E1122979160F006F1EEC /* UPMutableRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPMutableRequest.h; sourceTree = "<group>"; };
		D362E1132979160F006F1EEC /* HTTPRequestManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HTTPRequestManager.m; sourceTree = "<group>"; };
		D362E12529791674006F1EEC /* LaunchTimeLogsController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LaunchTimeLogsController.h; sourceTree = "<group>"; };
		D362E12629791674006F1EEC /* DebuggerConfigViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DebuggerConfigViewController.m; sourceTree = "<group>"; };
		D362E12729791674006F1EEC /* PageTraceCacheViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PageTraceCacheViewController.m; sourceTree = "<group>"; };
		D362E12829791674006F1EEC /* LaunchTimeLogsController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LaunchTimeLogsController.m; sourceTree = "<group>"; };
		D362E12929791674006F1EEC /* DebuggerConfigViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DebuggerConfigViewController.h; sourceTree = "<group>"; };
		D362E12A29791674006F1EEC /* PageTraceCacheViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PageTraceCacheViewController.h; sourceTree = "<group>"; };
		D362E13229791689006F1EEC /* SNSMhManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SNSMhManager.m; sourceTree = "<group>"; };
		D362E13329791689006F1EEC /* SNSModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SNSModule.h; sourceTree = "<group>"; };
		D362E13429791689006F1EEC /* SNSMhManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SNSMhManager.h; sourceTree = "<group>"; };
		D362E13529791689006F1EEC /* SNSModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SNSModule.m; sourceTree = "<group>"; };
		D362E13B29791710006F1EEC /* UserCenterAddStatusPatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterAddStatusPatcher.m; sourceTree = "<group>"; };
		D362E13C29791710006F1EEC /* UserCenterAddStatusPatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterAddStatusPatcher.h; sourceTree = "<group>"; };
		D362E13F29791724006F1EEC /* UIViewController+SetStatusBarStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+SetStatusBarStyle.h"; sourceTree = "<group>"; };
		D362E14029791724006F1EEC /* UIViewController+SetStatusBarStyle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+SetStatusBarStyle.m"; sourceTree = "<group>"; };
		D362E14429791749006F1EEC /* AnalyticsModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnalyticsModule.h; sourceTree = "<group>"; };
		D362E14529791749006F1EEC /* AnalyticsModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AnalyticsModule.m; sourceTree = "<group>"; };
		D362E159297918A1006F1EEC /* UPLaunchTimeRecorder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPLaunchTimeRecorder.m; sourceTree = "<group>"; };
		D362E15A297918A1006F1EEC /* UPLaunchTimeRecorder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPLaunchTimeRecorder.h; sourceTree = "<group>"; };
		D362E15E297918AE006F1EEC /* ASADataModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ASADataModel.h; sourceTree = "<group>"; };
		D362E15F297918AE006F1EEC /* MBMainBoxModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MBMainBoxModule.m; sourceTree = "<group>"; };
		D362E160297918AE006F1EEC /* MainboxServerProtocol.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MainboxServerProtocol.m; sourceTree = "<group>"; };
		D362E161297918AE006F1EEC /* MBMainBoxModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MBMainBoxModule.h; sourceTree = "<group>"; };
		D362E162297918AE006F1EEC /* ASADataModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ASADataModel.m; sourceTree = "<group>"; };
		D362E163297918AE006F1EEC /* MainboxServerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MainboxServerProtocol.h; sourceTree = "<group>"; };
		D362E16A297918E6006F1EEC /* UPNetWorkHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPNetWorkHeader.m; sourceTree = "<group>"; };
		D362E16B297918E6006F1EEC /* UPNetWorkHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPNetWorkHeader.h; sourceTree = "<group>"; };
		D362E1A929791EF8006F1EEC /* LGModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LGModule.h; sourceTree = "<group>"; };
		D362E1AA29791EF8006F1EEC /* LGModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LGModule.m; sourceTree = "<group>"; };
		D3F55CFE28F143F3006B32B9 /* state */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = state; sourceTree = "<group>"; };
		D3F55D0328F143F3006B32B9 /* state */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = state; sourceTree = "<group>"; };
		D3F55D0528F143F3006B32B9 /* MainLibModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MainLibModule.h; sourceTree = "<group>"; };
		D3F55D0628F143F3006B32B9 /* state */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = state; sourceTree = "<group>"; };
		D3F55D0728F143F3006B32B9 /* MainLibModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MainLibModule.m; sourceTree = "<group>"; };
		D3F55D2B28F14702006B32B9 /* UplusSpecial.podspec */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = UplusSpecial.podspec; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		D34A4C9B28F1247500FEE94F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E9C235671F988F1EE23BE7F1 /* libPods-UplusSpecial.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		099BFE0A2A60E1BD00FC412B /* CustomScheme */ = {
			isa = PBXGroup;
			children = (
				099BFE0B2A60E38800FC412B /* UpUplusCommonSchemeModule.h */,
				099BFE0C2A60E38800FC412B /* UpUplusCommonSchemeModule.m */,
			);
			path = CustomScheme;
			sourceTree = "<group>";
		};
		0B73AA845703F7F438CAD972 /* Pods */ = {
			isa = PBXGroup;
			children = (
				08AB9A6825A7E3867C09AF2E /* Pods-UplusSpecial.debug.xcconfig */,
				93BCE691EB990EEB6B65ED54 /* Pods-UplusSpecial.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		2C0D2411295BE087004B67E3 /* EventTrace */ = {
			isa = PBXGroup;
			children = (
				2C0D2413295BE087004B67E3 /* UPEventTraceConfig.h */,
				2C0D2412295BE087004B67E3 /* UPEventTraceConfig.m */,
			);
			path = EventTrace;
			sourceTree = "<group>";
		};
		2CDA8EE2291B837400844E1E /* ResourceTracker */ = {
			isa = PBXGroup;
			children = (
				2CDA8EE3291B837400844E1E /* ResourceTrackerIMP.h */,
				2CDA8EE4291B837400844E1E /* ResourceTrackerIMP.m */,
			);
			path = ResourceTracker;
			sourceTree = "<group>";
		};
		2CDA8EE5291B837400844E1E /* PushMessage */ = {
			isa = PBXGroup;
			children = (
				2CDA8EE7291B837400844E1E /* PushMessageHandler.h */,
				2CDA8EE6291B837400844E1E /* PushMessageHandler.m */,
			);
			path = PushMessage;
			sourceTree = "<group>";
		};
		2CFECE7029751B8700C337CA /* Upgrade */ = {
			isa = PBXGroup;
			children = (
				2CFECE7129751B8700C337CA /* UPUpgradeConfig.m */,
				2CFECE7229751B8700C337CA /* UPUpgradeConfig.h */,
			);
			path = Upgrade;
			sourceTree = "<group>";
		};
		4FD2A5552E45EF8700EB844F /* Device */ = {
			isa = PBXGroup;
			children = (
				4FB14B642E49D36500B534D3 /* WashProgramStartIntercepter.h */,
				4FB14B652E49D36500B534D3 /* WashProgramStartIntercepter.m */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		6768F9162DF057A500D960AE /* GyVerification */ = {
			isa = PBXGroup;
			children = (
				6768F91D2DF061C000D960AE /* GyOneKeyLoginLauncher.swift */,
				6768F91F2DF061DF00D960AE /* GyOneKeyLoginPatcher.swift */,
				6768F9212DF061F400D960AE /* GyOneKeyLoginModule.swift */,
			);
			path = GyVerification;
			sourceTree = "<group>";
		};
		67FE6EB92D939CE200B3BA87 /* 3DTouch */ = {
			isa = PBXGroup;
			children = (
				67FE6EB72D939CE200B3BA87 /* UP3DTouchModule.h */,
				67FE6EB82D939CE200B3BA87 /* UP3DTouchModule.m */,
			);
			path = 3DTouch;
			sourceTree = "<group>";
		};
		848F2CBE2B04ABAD004C65B8 /* LaunchTime */ = {
			isa = PBXGroup;
			children = (
				848F2CBF2B04AC00004C65B8 /* UpLaunchTimeReportModule.swift */,
			);
			path = LaunchTime;
			sourceTree = "<group>";
		};
		94BE8957291E37AA002B5167 /* OpenInstall */ = {
			isa = PBXGroup;
			children = (
				94BE8958291E39CE002B5167 /* OpenInstallConfig.h */,
				94BE8959291E39CE002B5167 /* OpenInstallConfig.m */,
			);
			path = OpenInstall;
			sourceTree = "<group>";
		};
		94BE898929239A89002B5167 /* InviteCode */ = {
			isa = PBXGroup;
			children = (
				94BE899829239A8A002B5167 /* InviteCodePatcher_special.h */,
				94BE898B29239A8A002B5167 /* InviteCodePatcher_special.m */,
				94BE899629239A8A002B5167 /* InviteCodeService_special.h */,
				94BE899929239A8A002B5167 /* InviteCodeService_special.m */,
				94BE899729239A8A002B5167 /* InviteCodeViewModel_special.h */,
				94BE898A29239A8A002B5167 /* InviteCodeViewModel_special.m */,
				94BE898C29239A8A002B5167 /* network */,
			);
			path = InviteCode;
			sourceTree = "<group>";
		};
		94BE898C29239A8A002B5167 /* network */ = {
			isa = PBXGroup;
			children = (
				94BE898D29239A8A002B5167 /* UPMutableRequest_special.m */,
				94BE898E29239A8A002B5167 /* HTTPRequestManager_special.m */,
				94BE898F29239A8A002B5167 /* AFHTTPRequestOperation_special.m */,
				94BE899029239A8A002B5167 /* UPNetWorkHeader_special.m */,
				94BE899129239A8A002B5167 /* OHServerDefine_special.h */,
				94BE899229239A8A002B5167 /* HTTPRequestManager_special.h */,
				94BE899329239A8A002B5167 /* UPMutableRequest_special.h */,
				94BE899429239A8A002B5167 /* UPNetWorkHeader_special.h */,
				94BE899529239A8A002B5167 /* AFHTTPRequestOperation_special.h */,
			);
			path = network;
			sourceTree = "<group>";
		};
		9548DF582CD9ED7E007632DA /* XiaoU */ = {
			isa = PBXGroup;
			children = (
				9548DF702CD9EDBE007632DA /* UPServiceConstant.h */,
				9548DF6F2CD9EDBE007632DA /* UPServiceFloatViewManager.h */,
				9548DF592CD9EDBE007632DA /* UPServiceFloatViewManager.m */,
				9548DF6E2CD9EDBE007632DA /* UPServiceEnableManager.swift */,
				9548DF6B2CD9EDBE007632DA /* UI */,
				9548DF6D2CD9EDBE007632DA /* ServerApi */,
				9548DF5D2CD9EDBE007632DA /* Utils */,
			);
			path = XiaoU;
			sourceTree = "<group>";
		};
		9548DF5D2CD9EDBE007632DA /* Utils */ = {
			isa = PBXGroup;
			children = (
				9548DF5A2CD9EDBE007632DA /* UPServiceGrayUtil.h */,
				9548DF5B2CD9EDBE007632DA /* UPServiceGrayUtil.m */,
				9548DF5C2CD9EDBE007632DA /* UPServicePageUtil.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		9548DF6B2CD9EDBE007632DA /* UI */ = {
			isa = PBXGroup;
			children = (
				9548DF5E2CD9EDBE007632DA /* UIViewController+XiaoU.h */,
				9548DF5F2CD9EDBE007632DA /* UIViewController+XiaoU.m */,
				9548DF602CD9EDBE007632DA /* UPServiceBadgeView.swift */,
				9548DF612CD9EDBE007632DA /* UPServiceFloatBubbleView.h */,
				9548DF622CD9EDBE007632DA /* UPServiceFloatBubbleView.m */,
				9548DF632CD9EDBE007632DA /* UPServiceFloatView.h */,
				9548DF642CD9EDBE007632DA /* UPServiceFloatView.m */,
				9548DF652CD9EDBE007632DA /* UPServiceFloatViewManager+Badge.h */,
				9548DF662CD9EDBE007632DA /* UPServiceFloatViewManager+Badge.m */,
				9548DF672CD9EDBE007632DA /* UPServiceFloatViewManager+Bubble.h */,
				9548DF682CD9EDBE007632DA /* UPServiceFloatViewManager+Bubble.m */,
				9548DF692CD9EDBE007632DA /* UPServiceFloatViewManager+Coordinate.h */,
				9548DF6A2CD9EDBE007632DA /* UPServiceFloatViewManager+Coordinate.m */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		9548DF6D2CD9EDBE007632DA /* ServerApi */ = {
			isa = PBXGroup;
			children = (
				9548DF6C2CD9EDBE007632DA /* UPServiceEnableRequest.swift */,
			);
			path = ServerApi;
			sourceTree = "<group>";
		};
		955A88832A3715260066F538 /* UI */ = {
			isa = PBXGroup;
			children = (
				955A88772A370CE50066F538 /* UpAuthViewController.h */,
				955A88782A370CE50066F538 /* UpAuthViewController.m */,
				955A88842A37219C0066F538 /* UpAuthPageCell.h */,
				955A88852A37219C0066F538 /* UpAuthPageCell.m */,
				955A888C2A3736360066F538 /* UpAuthFamilyPickerView.h */,
				955A888D2A3736360066F538 /* UpAuthFamilyPickerView.m */,
				952630062A380ECC00864668 /* UpAuthFamilyPickerCell.h */,
				952630072A380ECC00864668 /* UpAuthFamilyPickerCell.m */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		95686A682A208163001B0FD5 /* Auth */ = {
			isa = PBXGroup;
			children = (
				955A88832A3715260066F538 /* UI */,
				95686A692A208227001B0FD5 /* UpAuthModule.h */,
				95686A6A2A208227001B0FD5 /* UpAuthModule.m */,
				95686A6D2A20852E001B0FD5 /* UpAuthManager.h */,
				95686A6E2A20852E001B0FD5 /* UpAuthManager.m */,
				955A88732A36F9E10066F538 /* UpAuthManager+Reporter.h */,
				955A88742A36F9E10066F538 /* UpAuthManager+Reporter.m */,
				95E858CB2A386DE200ECDFBE /* UpAuthCodeRequest.h */,
				95E858CC2A386DE200ECDFBE /* UpAuthCodeRequest.m */,
			);
			path = Auth;
			sourceTree = "<group>";
		};
		95E95C1E2D804E59006A53D4 /* LaunchKit */ = {
			isa = PBXGroup;
			children = (
				95E95C1F2D804E74006A53D4 /* ModuleConfigure.swift */,
				95E95C212D804E87006A53D4 /* ModuleInitializer.swift */,
			);
			path = LaunchKit;
			sourceTree = "<group>";
		};
		9BEDDD552D82C88100D85041 /* Devicebind */ = {
			isa = PBXGroup;
			children = (
				9B6C5D442D8288A200BD3505 /* DeviceBindPatcher.swift */,
				9BE7382E2DAF85BA00DC439C /* DeviceBindLauncher.swift */,
			);
			path = Devicebind;
			sourceTree = "<group>";
		};
		A5565BA72E01530B002665C8 /* CommonAlertTool */ = {
			isa = PBXGroup;
			children = (
				A5565BA82E015346002665C8 /* AlertControllerTool.h */,
				A5565BA92E015346002665C8 /* AlertControllerTool.m */,
			);
			path = CommonAlertTool;
			sourceTree = "<group>";
		};
		D34A4C9428F1247500FEE94F = {
			isa = PBXGroup;
			children = (
				D3F55D2B28F14702006B32B9 /* UplusSpecial.podspec */,
				D34A4CA028F1247500FEE94F /* UplusSpecial */,
				D34A4C9F28F1247500FEE94F /* Products */,
				0B73AA845703F7F438CAD972 /* Pods */,
				F0AC4597123E0AB066EE3D46 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		D34A4C9F28F1247500FEE94F /* Products */ = {
			isa = PBXGroup;
			children = (
				D34A4C9E28F1247500FEE94F /* UplusSpecial.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		D34A4CA028F1247500FEE94F /* UplusSpecial */ = {
			isa = PBXGroup;
			children = (
				D34A4CA128F1247500FEE94F /* UplusSpecial.h */,
				95E95C1E2D804E59006A53D4 /* LaunchKit */,
				D3F55CFD28F143F3006B32B9 /* Business */,
				A5565BA72E01530B002665C8 /* CommonAlertTool */,
				D3F55CFF28F143F3006B32B9 /* Features */,
				D3F55D0428F143F3006B32B9 /* Init */,
				D3F55CFC28F143F3006B32B9 /* Resource */,
			);
			path = UplusSpecial;
			sourceTree = "<group>";
		};
		D362E0FA29791402006F1EEC /* FamilyFloat */ = {
			isa = PBXGroup;
			children = (
				D362E0FE29791402006F1EEC /* UPFamilyFloatView.h */,
				D362E0FB29791402006F1EEC /* UPFamilyFloatView.m */,
				D362E0FC29791402006F1EEC /* UPFamilyFloatLauncher.h */,
				D362E0FD29791402006F1EEC /* UPFamilyFloatLauncher.m */,
			);
			path = FamilyFloat;
			sourceTree = "<group>";
		};
		D362E1032979149A006F1EEC /* JoinFamily */ = {
			isa = PBXGroup;
			children = (
				D362E1052979149A006F1EEC /* JoinFamilyAlertPatcher.h */,
				D362E1042979149A006F1EEC /* JoinFamilyAlertPatcher.m */,
			);
			path = JoinFamily;
			sourceTree = "<group>";
		};
		D362E10D2979160E006F1EEC /* NetWorking */ = {
			isa = PBXGroup;
			children = (
				D362E16B297918E6006F1EEC /* UPNetWorkHeader.h */,
				D362E16A297918E6006F1EEC /* UPNetWorkHeader.m */,
				D362E10E2979160F006F1EEC /* AFHTTPRequestOperation.h */,
				D362E1112979160F006F1EEC /* AFHTTPRequestOperation.m */,
				D362E1122979160F006F1EEC /* UPMutableRequest.h */,
				D362E10F2979160F006F1EEC /* UPMutableRequest.m */,
				D362E1102979160F006F1EEC /* HTTPRequestManager.h */,
				D362E1132979160F006F1EEC /* HTTPRequestManager.m */,
			);
			path = NetWorking;
			sourceTree = "<group>";
		};
		D362E12429791674006F1EEC /* DebugFunction */ = {
			isa = PBXGroup;
			children = (
				D362E15A297918A1006F1EEC /* UPLaunchTimeRecorder.h */,
				D362E159297918A1006F1EEC /* UPLaunchTimeRecorder.m */,
				D362E12529791674006F1EEC /* LaunchTimeLogsController.h */,
				D362E12829791674006F1EEC /* LaunchTimeLogsController.m */,
				D362E12929791674006F1EEC /* DebuggerConfigViewController.h */,
				D362E12629791674006F1EEC /* DebuggerConfigViewController.m */,
				D362E12A29791674006F1EEC /* PageTraceCacheViewController.h */,
				D362E12729791674006F1EEC /* PageTraceCacheViewController.m */,
				14FE343D2988A3BF00DAE099 /* UPMockCrashViewController.h */,
				14FE343E2988A3BF00DAE099 /* UPMockCrashViewController.m */,
			);
			path = DebugFunction;
			sourceTree = "<group>";
		};
		D362E13129791689006F1EEC /* SNS */ = {
			isa = PBXGroup;
			children = (
				D362E13429791689006F1EEC /* SNSMhManager.h */,
				D362E13229791689006F1EEC /* SNSMhManager.m */,
				D362E13329791689006F1EEC /* SNSModule.h */,
				D362E13529791689006F1EEC /* SNSModule.m */,
				95693CDD2DB8D65E00333759 /* FamilyInviteView.swift */,
			);
			path = SNS;
			sourceTree = "<group>";
		};
		D362E13A29791710006F1EEC /* StatusBar */ = {
			isa = PBXGroup;
			children = (
				D362E13C29791710006F1EEC /* UserCenterAddStatusPatcher.h */,
				D362E13B29791710006F1EEC /* UserCenterAddStatusPatcher.m */,
				D362E13F29791724006F1EEC /* UIViewController+SetStatusBarStyle.h */,
				D362E14029791724006F1EEC /* UIViewController+SetStatusBarStyle.m */,
			);
			path = StatusBar;
			sourceTree = "<group>";
		};
		D362E14329791749006F1EEC /* Analytics */ = {
			isa = PBXGroup;
			children = (
				D362E14429791749006F1EEC /* AnalyticsModule.h */,
				D362E14529791749006F1EEC /* AnalyticsModule.m */,
				95A552ED2C8848EE00D0790D /* UIViewController+PageStay.h */,
				95A552EE2C8848EE00D0790D /* UIViewController+PageStay.m */,
			);
			path = Analytics;
			sourceTree = "<group>";
		};
		D362E15D297918AE006F1EEC /* AppleDataReport */ = {
			isa = PBXGroup;
			children = (
				D362E15E297918AE006F1EEC /* ASADataModel.h */,
				D362E162297918AE006F1EEC /* ASADataModel.m */,
				D362E161297918AE006F1EEC /* MBMainBoxModule.h */,
				D362E15F297918AE006F1EEC /* MBMainBoxModule.m */,
				D362E163297918AE006F1EEC /* MainboxServerProtocol.h */,
				D362E160297918AE006F1EEC /* MainboxServerProtocol.m */,
			);
			path = AppleDataReport;
			sourceTree = "<group>";
		};
		D3F55CFC28F143F3006B32B9 /* Resource */ = {
			isa = PBXGroup;
			children = (
				954ABCC32CDC9682004E2767 /* UplusSpecial.bundle */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		D3F55CFD28F143F3006B32B9 /* Business */ = {
			isa = PBXGroup;
			children = (
				4FD2A5552E45EF8700EB844F /* Device */,
				6768F9162DF057A500D960AE /* GyVerification */,
				9BEDDD552D82C88100D85041 /* Devicebind */,
				67FE6EB92D939CE200B3BA87 /* 3DTouch */,
				9548DF582CD9ED7E007632DA /* XiaoU */,
				848F2CBE2B04ABAD004C65B8 /* LaunchTime */,
				099BFE0A2A60E1BD00FC412B /* CustomScheme */,
				95686A682A208163001B0FD5 /* Auth */,
				D362E15D297918AE006F1EEC /* AppleDataReport */,
				D362E14329791749006F1EEC /* Analytics */,
				D362E13A29791710006F1EEC /* StatusBar */,
				D362E13129791689006F1EEC /* SNS */,
				D362E12429791674006F1EEC /* DebugFunction */,
				D362E10D2979160E006F1EEC /* NetWorking */,
				D362E1032979149A006F1EEC /* JoinFamily */,
				D362E0FA29791402006F1EEC /* FamilyFloat */,
				94BE898929239A89002B5167 /* InviteCode */,
				2CDA8EE5291B837400844E1E /* PushMessage */,
				2CDA8EE2291B837400844E1E /* ResourceTracker */,
				D3F55CFE28F143F3006B32B9 /* state */,
			);
			path = Business;
			sourceTree = "<group>";
		};
		D3F55CFF28F143F3006B32B9 /* Features */ = {
			isa = PBXGroup;
			children = (
				2CFECE7029751B8700C337CA /* Upgrade */,
				2C0D2411295BE087004B67E3 /* EventTrace */,
				94BE8957291E37AA002B5167 /* OpenInstall */,
				D3F55D0328F143F3006B32B9 /* state */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		D3F55D0428F143F3006B32B9 /* Init */ = {
			isa = PBXGroup;
			children = (
				D3F55D0628F143F3006B32B9 /* state */,
				D3F55D0528F143F3006B32B9 /* MainLibModule.h */,
				D3F55D0728F143F3006B32B9 /* MainLibModule.m */,
				D362E1A929791EF8006F1EEC /* LGModule.h */,
				D362E1AA29791EF8006F1EEC /* LGModule.m */,
				2CDA8ED9291B831D00844E1E /* InitBaseKit.h */,
				2CDA8EDA291B831D00844E1E /* InitBaseKit.m */,
				848339322A8B90AD0039E79B /* UplusSpecialModule.swift */,
			);
			path = Init;
			sourceTree = "<group>";
		};
		F0AC4597123E0AB066EE3D46 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				CD913E81A9319EB1F839EDC7 /* libPods-UplusSpecial.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		D34A4C9928F1247500FEE94F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D34A4CA228F1247500FEE94F /* UplusSpecial.h in Headers */,
				2CFECE7429751B8800C337CA /* UPUpgradeConfig.h in Headers */,
				95686A6B2A208227001B0FD5 /* UpAuthModule.h in Headers */,
				9548DF852CD9EDBF007632DA /* UPServiceConstant.h in Headers */,
				D362E13829791689006F1EEC /* SNSMhManager.h in Headers */,
				D362E13729791689006F1EEC /* SNSModule.h in Headers */,
				2CDA8EDB291B831E00844E1E /* InitBaseKit.h in Headers */,
				A5565BAA2E015346002665C8 /* AlertControllerTool.h in Headers */,
				D3F55D0C28F143F4006B32B9 /* MainLibModule.h in Headers */,
				D362E14629791749006F1EEC /* AnalyticsModule.h in Headers */,
				D362E1AB29791EF8006F1EEC /* LGModule.h in Headers */,
				099BFE0D2A60E38800FC412B /* UpUplusCommonSchemeModule.h in Headers */,
				D362E167297918AE006F1EEC /* MBMainBoxModule.h in Headers */,
				94BE895A291E39CE002B5167 /* OpenInstallConfig.h in Headers */,
				95A552EF2C8848EE00D0790D /* UIViewController+PageStay.h in Headers */,
				D362E12B29791674006F1EEC /* LaunchTimeLogsController.h in Headers */,
				955A88792A370CE50066F538 /* UpAuthViewController.h in Headers */,
				952630082A380ECC00864668 /* UpAuthFamilyPickerCell.h in Headers */,
				94BE89A729239A8A002B5167 /* InviteCodePatcher_special.h in Headers */,
				D362E15C297918A1006F1EEC /* UPLaunchTimeRecorder.h in Headers */,
				94BE89A129239A8A002B5167 /* HTTPRequestManager_special.h in Headers */,
				94BE89A429239A8A002B5167 /* AFHTTPRequestOperation_special.h in Headers */,
				4FB14B662E49D36500B534D3 /* WashProgramStartIntercepter.h in Headers */,
				D362E14129791724006F1EEC /* UIViewController+SetStatusBarStyle.h in Headers */,
				D362E164297918AE006F1EEC /* ASADataModel.h in Headers */,
				D362E10229791402006F1EEC /* UPFamilyFloatView.h in Headers */,
				D362E169297918AE006F1EEC /* MainboxServerProtocol.h in Headers */,
				D362E13029791674006F1EEC /* PageTraceCacheViewController.h in Headers */,
				9548DF7E2CD9EDBF007632DA /* UPServiceFloatViewManager+Bubble.h in Headers */,
				94BE89A329239A8A002B5167 /* UPNetWorkHeader_special.h in Headers */,
				2C0D2415295BE087004B67E3 /* UPEventTraceConfig.h in Headers */,
				D362E1162979160F006F1EEC /* HTTPRequestManager.h in Headers */,
				D362E12F29791674006F1EEC /* DebuggerConfigViewController.h in Headers */,
				2CDA8EE8291B837400844E1E /* ResourceTrackerIMP.h in Headers */,
				9548DF7A2CD9EDBF007632DA /* UPServiceFloatView.h in Headers */,
				95686A6F2A20852E001B0FD5 /* UpAuthManager.h in Headers */,
				94BE89A029239A8A002B5167 /* OHServerDefine_special.h in Headers */,
				D362E10029791402006F1EEC /* UPFamilyFloatLauncher.h in Headers */,
				D362E1072979149A006F1EEC /* JoinFamilyAlertPatcher.h in Headers */,
				14FE343F2988A3BF00DAE099 /* UPMockCrashViewController.h in Headers */,
				9548DF842CD9EDBF007632DA /* UPServiceFloatViewManager.h in Headers */,
				95E858CD2A386DE200ECDFBE /* UpAuthCodeRequest.h in Headers */,
				955A88862A37219C0066F538 /* UpAuthPageCell.h in Headers */,
				9548DF802CD9EDBF007632DA /* UPServiceFloatViewManager+Coordinate.h in Headers */,
				9548DF722CD9EDBE007632DA /* UPServiceGrayUtil.h in Headers */,
				955A888E2A3736360066F538 /* UpAuthFamilyPickerView.h in Headers */,
				D362E13E29791710006F1EEC /* UserCenterAddStatusPatcher.h in Headers */,
				2CDA8EEB291B837400844E1E /* PushMessageHandler.h in Headers */,
				9548DF782CD9EDBF007632DA /* UPServiceFloatBubbleView.h in Headers */,
				67FE6EBB2D939CE200B3BA87 /* UP3DTouchModule.h in Headers */,
				9548DF752CD9EDBF007632DA /* UIViewController+XiaoU.h in Headers */,
				955A88752A36F9E10066F538 /* UpAuthManager+Reporter.h in Headers */,
				D362E16D297918E6006F1EEC /* UPNetWorkHeader.h in Headers */,
				D362E1142979160F006F1EEC /* AFHTTPRequestOperation.h in Headers */,
				9548DF7C2CD9EDBF007632DA /* UPServiceFloatViewManager+Badge.h in Headers */,
				94BE89A629239A8A002B5167 /* InviteCodeViewModel_special.h in Headers */,
				94BE89A529239A8A002B5167 /* InviteCodeService_special.h in Headers */,
				94BE89A229239A8A002B5167 /* UPMutableRequest_special.h in Headers */,
				D362E1182979160F006F1EEC /* UPMutableRequest.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		D34A4C9D28F1247500FEE94F /* UplusSpecial */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D34A4CA528F1247500FEE94F /* Build configuration list for PBXNativeTarget "UplusSpecial" */;
			buildPhases = (
				F6D547218E6C658DEF1E416D /* [CP] Check Pods Manifest.lock */,
				D34A4C9928F1247500FEE94F /* Headers */,
				D34A4C9A28F1247500FEE94F /* Sources */,
				D34A4C9B28F1247500FEE94F /* Frameworks */,
				D34A4C9C28F1247500FEE94F /* Resources */,
				5D9C39EA9805A69D294E00C0 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UplusSpecial;
			productName = UplusSpecial;
			productReference = D34A4C9E28F1247500FEE94F /* UplusSpecial.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		D34A4C9528F1247500FEE94F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1340;
				TargetAttributes = {
					D34A4C9D28F1247500FEE94F = {
						CreatedOnToolsVersion = 13.4;
						LastSwiftMigration = 1430;
					};
				};
			};
			buildConfigurationList = D34A4C9828F1247500FEE94F /* Build configuration list for PBXProject "UplusSpecial" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = D34A4C9428F1247500FEE94F;
			productRefGroup = D34A4C9F28F1247500FEE94F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D34A4C9D28F1247500FEE94F /* UplusSpecial */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D34A4C9C28F1247500FEE94F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D3F55D2C28F14702006B32B9 /* UplusSpecial.podspec in Resources */,
				D3F55D0B28F143F4006B32B9 /* state in Resources */,
				954ABCC42CDC9682004E2767 /* UplusSpecial.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		5D9C39EA9805A69D294E00C0 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UplusSpecial/Pods-UplusSpecial-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UplusSpecial/Pods-UplusSpecial-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UplusSpecial/Pods-UplusSpecial-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F6D547218E6C658DEF1E416D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UplusSpecial-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		D34A4C9A28F1247500FEE94F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9548DF732CD9EDBF007632DA /* UPServiceGrayUtil.m in Sources */,
				D362E16C297918E6006F1EEC /* UPNetWorkHeader.m in Sources */,
				D362E15B297918A1006F1EEC /* UPLaunchTimeRecorder.m in Sources */,
				14FE34402988A3BF00DAE099 /* UPMockCrashViewController.m in Sources */,
				D362E14729791749006F1EEC /* AnalyticsModule.m in Sources */,
				955A88872A37219C0066F538 /* UpAuthPageCell.m in Sources */,
				D362E13929791689006F1EEC /* SNSModule.m in Sources */,
				D362E12E29791674006F1EEC /* LaunchTimeLogsController.m in Sources */,
				9B6C5D452D8288A200BD3505 /* DeviceBindPatcher.swift in Sources */,
				6768F9202DF061DF00D960AE /* GyOneKeyLoginPatcher.swift in Sources */,
				099BFE0E2A60E38800FC412B /* UpUplusCommonSchemeModule.m in Sources */,
				6768F91E2DF061C000D960AE /* GyOneKeyLoginLauncher.swift in Sources */,
				95A552F02C8848EE00D0790D /* UIViewController+PageStay.m in Sources */,
				95E95C222D804E87006A53D4 /* ModuleInitializer.swift in Sources */,
				94BE89A829239A8A002B5167 /* InviteCodeService_special.m in Sources */,
				952630092A380ECC00864668 /* UpAuthFamilyPickerCell.m in Sources */,
				848F2CC02B04AC00004C65B8 /* UpLaunchTimeReportModule.swift in Sources */,
				D362E10129791402006F1EEC /* UPFamilyFloatLauncher.m in Sources */,
				D362E1AC29791EF8006F1EEC /* LGModule.m in Sources */,
				94BE895B291E39CE002B5167 /* OpenInstallConfig.m in Sources */,
				D362E12C29791674006F1EEC /* DebuggerConfigViewController.m in Sources */,
				9548DF812CD9EDBF007632DA /* UPServiceFloatViewManager+Coordinate.m in Sources */,
				D362E166297918AE006F1EEC /* MainboxServerProtocol.m in Sources */,
				9548DF7F2CD9EDBF007632DA /* UPServiceFloatViewManager+Bubble.m in Sources */,
				9548DF832CD9EDBF007632DA /* UPServiceEnableManager.swift in Sources */,
				2CDA8EE9291B837400844E1E /* ResourceTrackerIMP.m in Sources */,
				94BE899B29239A8A002B5167 /* InviteCodePatcher_special.m in Sources */,
				D362E168297918AE006F1EEC /* ASADataModel.m in Sources */,
				94BE899E29239A8A002B5167 /* AFHTTPRequestOperation_special.m in Sources */,
				2C0D2414295BE087004B67E3 /* UPEventTraceConfig.m in Sources */,
				9548DF822CD9EDBF007632DA /* UPServiceEnableRequest.swift in Sources */,
				D3F55D0E28F143F4006B32B9 /* MainLibModule.m in Sources */,
				2CDA8EEA291B837400844E1E /* PushMessageHandler.m in Sources */,
				955A88762A36F9E10066F538 /* UpAuthManager+Reporter.m in Sources */,
				6768F9222DF061F400D960AE /* GyOneKeyLoginModule.swift in Sources */,
				848339352A8B90AD0039E79B /* UplusSpecialModule.swift in Sources */,
				2CFECE7329751B8800C337CA /* UPUpgradeConfig.m in Sources */,
				95E95C202D804E74006A53D4 /* ModuleConfigure.swift in Sources */,
				94BE899F29239A8A002B5167 /* UPNetWorkHeader_special.m in Sources */,
				95693CDE2DB8D65E00333759 /* FamilyInviteView.swift in Sources */,
				D362E1172979160F006F1EEC /* AFHTTPRequestOperation.m in Sources */,
				D362E13629791689006F1EEC /* SNSMhManager.m in Sources */,
				94BE899A29239A8A002B5167 /* InviteCodeViewModel_special.m in Sources */,
				95686A6C2A208227001B0FD5 /* UpAuthModule.m in Sources */,
				95E858CE2A386DE200ECDFBE /* UpAuthCodeRequest.m in Sources */,
				9548DF742CD9EDBF007632DA /* UPServicePageUtil.swift in Sources */,
				D362E14229791724006F1EEC /* UIViewController+SetStatusBarStyle.m in Sources */,
				9548DF7D2CD9EDBF007632DA /* UPServiceFloatViewManager+Badge.m in Sources */,
				D362E0FF29791402006F1EEC /* UPFamilyFloatView.m in Sources */,
				9BE7382F2DAF85BA00DC439C /* DeviceBindLauncher.swift in Sources */,
				67FE6EBA2D939CE200B3BA87 /* UP3DTouchModule.m in Sources */,
				94BE899D29239A8A002B5167 /* HTTPRequestManager_special.m in Sources */,
				955A887A2A370CE50066F538 /* UpAuthViewController.m in Sources */,
				9548DF712CD9EDBE007632DA /* UPServiceFloatViewManager.m in Sources */,
				955A888F2A3736360066F538 /* UpAuthFamilyPickerView.m in Sources */,
				9548DF792CD9EDBF007632DA /* UPServiceFloatBubbleView.m in Sources */,
				2CDA8EDC291B831E00844E1E /* InitBaseKit.m in Sources */,
				9548DF772CD9EDBF007632DA /* UPServiceBadgeView.swift in Sources */,
				D362E165297918AE006F1EEC /* MBMainBoxModule.m in Sources */,
				9548DF762CD9EDBF007632DA /* UIViewController+XiaoU.m in Sources */,
				94BE899C29239A8A002B5167 /* UPMutableRequest_special.m in Sources */,
				A5565BAB2E015346002665C8 /* AlertControllerTool.m in Sources */,
				95686A702A20852E001B0FD5 /* UpAuthManager.m in Sources */,
				D362E12D29791674006F1EEC /* PageTraceCacheViewController.m in Sources */,
				D362E1192979160F006F1EEC /* HTTPRequestManager.m in Sources */,
				D362E1062979149A006F1EEC /* JoinFamilyAlertPatcher.m in Sources */,
				4FB14B672E49D36500B534D3 /* WashProgramStartIntercepter.m in Sources */,
				D362E1152979160F006F1EEC /* UPMutableRequest.m in Sources */,
				D362E13D29791710006F1EEC /* UserCenterAddStatusPatcher.m in Sources */,
				9548DF7B2CD9EDBF007632DA /* UPServiceFloatView.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		D34A4CA328F1247500FEE94F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D34A4CA428F1247500FEE94F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		D34A4CA628F1247500FEE94F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 08AB9A6825A7E3867C09AF2E /* Pods-UplusSpecial.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AFNetworking\"",
					"\"${PODS_ROOT}/Headers/Public/AWFileHash\"",
					"\"${PODS_ROOT}/Headers/Public/AliyunOSSiOS\"",
					"\"${PODS_ROOT}/Headers/Public/Aspects\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaHTTPServer\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaLumberjack\"",
					"\"${PODS_ROOT}/Headers/Public/FMDB\"",
					"\"${PODS_ROOT}/Headers/Public/Godzip\"",
					"\"${PODS_ROOT}/Headers/Public/Growing\"",
					"\"${PODS_ROOT}/Headers/Public/JCore\"",
					"\"${PODS_ROOT}/Headers/Public/JPush\"",
					"\"${PODS_ROOT}/Headers/Public/LBXScan\"",
					"\"${PODS_ROOT}/Headers/Public/LogicEngine\"",
					"\"${PODS_ROOT}/Headers/Public/MJExtension\"",
					"\"${PODS_ROOT}/Headers/Public/Protobuf\"",
					"\"${PODS_ROOT}/Headers/Public/Realm\"",
					"\"${PODS_ROOT}/Headers/Public/SQLCipher\"",
					"\"${PODS_ROOT}/Headers/Public/UHMasonry\"",
					"\"${PODS_ROOT}/Headers/Public/UHWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/UPCore\"",
					"\"${PODS_ROOT}/Headers/Public/UPCrashDefend\"",
					"\"${PODS_ROOT}/Headers/Public/UPDevice\"",
					"\"${PODS_ROOT}/Headers/Public/UPDeviceInitKit\"",
					"\"${PODS_ROOT}/Headers/Public/UPHttpDNS\"",
					"\"${PODS_ROOT}/Headers/Public/UPJPushChannel\"",
					"\"${PODS_ROOT}/Headers/Public/UPPageTrace\"",
					"\"${PODS_ROOT}/Headers/Public/UPPluginBaseAPI\"",
					"\"${PODS_ROOT}/Headers/Public/UPPrivacyPolicy\"",
					"\"${PODS_ROOT}/Headers/Public/UPPush\"",
					"\"${PODS_ROOT}/Headers/Public/UPResource\"",
					"\"${PODS_ROOT}/Headers/Public/UPShortCut\"",
					"\"${PODS_ROOT}/Headers/Public/UPStorage\"",
					"\"${PODS_ROOT}/Headers/Public/UPTools\"",
					"\"${PODS_ROOT}/Headers/Public/UPUpgrade\"",
					"\"${PODS_ROOT}/Headers/Public/UPVDN\"",
					"\"${PODS_ROOT}/Headers/Public/UpCrash\"",
					"\"${PODS_ROOT}/Headers/Public/UpJVerification\"",
					"\"${PODS_ROOT}/Headers/Public/UpKSCrash\"",
					"\"${PODS_ROOT}/Headers/Public/UpNebula\"",
					"\"${PODS_ROOT}/Headers/Public/UpOssPlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpPermissionManager\"",
					"\"${PODS_ROOT}/Headers/Public/UpPluginFoundation\"",
					"\"${PODS_ROOT}/Headers/Public/UpPlugins\"",
					"\"${PODS_ROOT}/Headers/Public/UpQQMusic\"",
					"\"${PODS_ROOT}/Headers/Public/UpScan\"",
					"\"${PODS_ROOT}/Headers/Public/UpTrace\"",
					"\"${PODS_ROOT}/Headers/Public/UpTracePlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpUMShare\"",
					"\"${PODS_ROOT}/Headers/Public/UpUmengPlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpUnionPay\"",
					"\"${PODS_ROOT}/Headers/Public/UpUnionPayPlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpVdnModule\"",
					"\"${PODS_ROOT}/Headers/Public/UplusBase\"",
					"\"${PODS_ROOT}/Headers/Public/UplusKit\"",
					"\"${PODS_ROOT}/Headers/Public/YYCategories\"",
					"\"${PODS_ROOT}/Headers/Public/YYModel\"",
					"\"${PODS_ROOT}/Headers/Public/YYText\"",
					"\"${PODS_ROOT}/Headers/Public/ZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/libOpenInstallSDK\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-ios\"",
					"\"${PODS_ROOT}/Headers/Public/uplog\"",
					"\"${PODS_ROOT}/Headers/Public/upnetwork\"",
					"\"${PODS_ROOT}/Headers/Public/upsocial\"",
					"\"${PODS_ROOT}/Headers/Public/upuserdomain\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Realm/Headers\"",
					"\"$(SDKROOT)/usr/include/libxml2\"",
					SQLCipher,
					"$(SDKROOT)/usr/include/libxml2",
					"$(PODS_ROOT)/SQLCipher",
					"${OBJECT_FILE_DIR_normal}/x86_64",
					"${OBJECT_FILE_DIR_normal}/arm64",
				);
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.UplusSpecial;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				USER_HEADER_SEARCH_PATHS = "${SRCROOT}";
			};
			name = Debug;
		};
		D34A4CA728F1247500FEE94F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93BCE691EB990EEB6B65ED54 /* Pods-UplusSpecial.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AFNetworking\"",
					"\"${PODS_ROOT}/Headers/Public/AWFileHash\"",
					"\"${PODS_ROOT}/Headers/Public/AliyunOSSiOS\"",
					"\"${PODS_ROOT}/Headers/Public/Aspects\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaHTTPServer\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaLumberjack\"",
					"\"${PODS_ROOT}/Headers/Public/FMDB\"",
					"\"${PODS_ROOT}/Headers/Public/Godzip\"",
					"\"${PODS_ROOT}/Headers/Public/Growing\"",
					"\"${PODS_ROOT}/Headers/Public/JCore\"",
					"\"${PODS_ROOT}/Headers/Public/JPush\"",
					"\"${PODS_ROOT}/Headers/Public/LBXScan\"",
					"\"${PODS_ROOT}/Headers/Public/LogicEngine\"",
					"\"${PODS_ROOT}/Headers/Public/MJExtension\"",
					"\"${PODS_ROOT}/Headers/Public/Protobuf\"",
					"\"${PODS_ROOT}/Headers/Public/Realm\"",
					"\"${PODS_ROOT}/Headers/Public/SQLCipher\"",
					"\"${PODS_ROOT}/Headers/Public/UHMasonry\"",
					"\"${PODS_ROOT}/Headers/Public/UHWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/UPCore\"",
					"\"${PODS_ROOT}/Headers/Public/UPCrashDefend\"",
					"\"${PODS_ROOT}/Headers/Public/UPDevice\"",
					"\"${PODS_ROOT}/Headers/Public/UPDeviceInitKit\"",
					"\"${PODS_ROOT}/Headers/Public/UPHttpDNS\"",
					"\"${PODS_ROOT}/Headers/Public/UPJPushChannel\"",
					"\"${PODS_ROOT}/Headers/Public/UPPageTrace\"",
					"\"${PODS_ROOT}/Headers/Public/UPPluginBaseAPI\"",
					"\"${PODS_ROOT}/Headers/Public/UPPrivacyPolicy\"",
					"\"${PODS_ROOT}/Headers/Public/UPPush\"",
					"\"${PODS_ROOT}/Headers/Public/UPResource\"",
					"\"${PODS_ROOT}/Headers/Public/UPShortCut\"",
					"\"${PODS_ROOT}/Headers/Public/UPStorage\"",
					"\"${PODS_ROOT}/Headers/Public/UPTools\"",
					"\"${PODS_ROOT}/Headers/Public/UPUpgrade\"",
					"\"${PODS_ROOT}/Headers/Public/UPVDN\"",
					"\"${PODS_ROOT}/Headers/Public/UpCrash\"",
					"\"${PODS_ROOT}/Headers/Public/UpJVerification\"",
					"\"${PODS_ROOT}/Headers/Public/UpKSCrash\"",
					"\"${PODS_ROOT}/Headers/Public/UpNebula\"",
					"\"${PODS_ROOT}/Headers/Public/UpOssPlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpPermissionManager\"",
					"\"${PODS_ROOT}/Headers/Public/UpPluginFoundation\"",
					"\"${PODS_ROOT}/Headers/Public/UpPlugins\"",
					"\"${PODS_ROOT}/Headers/Public/UpQQMusic\"",
					"\"${PODS_ROOT}/Headers/Public/UpScan\"",
					"\"${PODS_ROOT}/Headers/Public/UpTrace\"",
					"\"${PODS_ROOT}/Headers/Public/UpTracePlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpUMShare\"",
					"\"${PODS_ROOT}/Headers/Public/UpUmengPlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpUnionPay\"",
					"\"${PODS_ROOT}/Headers/Public/UpUnionPayPlugin\"",
					"\"${PODS_ROOT}/Headers/Public/UpVdnModule\"",
					"\"${PODS_ROOT}/Headers/Public/UplusBase\"",
					"\"${PODS_ROOT}/Headers/Public/UplusKit\"",
					"\"${PODS_ROOT}/Headers/Public/YYCategories\"",
					"\"${PODS_ROOT}/Headers/Public/YYModel\"",
					"\"${PODS_ROOT}/Headers/Public/YYText\"",
					"\"${PODS_ROOT}/Headers/Public/ZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/libOpenInstallSDK\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-ios\"",
					"\"${PODS_ROOT}/Headers/Public/uplog\"",
					"\"${PODS_ROOT}/Headers/Public/upnetwork\"",
					"\"${PODS_ROOT}/Headers/Public/upsocial\"",
					"\"${PODS_ROOT}/Headers/Public/upuserdomain\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Realm/Headers\"",
					"\"$(SDKROOT)/usr/include/libxml2\"",
					SQLCipher,
					"$(SDKROOT)/usr/include/libxml2",
					"$(PODS_ROOT)/SQLCipher",
					"\"${PODS_ROOT}/Headers/Public/UPPrivacyPolicy\"",
					"${SRCROOT}",
					"${OBJECT_FILE_DIR_normal}/x86_64",
					"${OBJECT_FILE_DIR_normal}/arm64",
				);
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.UplusSpecial;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				USER_HEADER_SEARCH_PATHS = "${SRCROOT}";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		D34A4C9828F1247500FEE94F /* Build configuration list for PBXProject "UplusSpecial" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D34A4CA328F1247500FEE94F /* Debug */,
				D34A4CA428F1247500FEE94F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D34A4CA528F1247500FEE94F /* Build configuration list for PBXNativeTarget "UplusSpecial" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D34A4CA628F1247500FEE94F /* Debug */,
				D34A4CA728F1247500FEE94F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = D34A4C9528F1247500FEE94F /* Project object */;
}
