#
# Be sure to run `pod lib lint SYBirdSpecial.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'UplusSpecial'
  s.version          = '0.1.0'
  s.summary          = 'Uplus App 特有差异代码库'

# This description is used to generate tags and improve search results.
#   * Think: What does it do? Why did you write it? What is the focus?
#   * Try to keep it short, snappy and to the point.
#   * Write the description between the DESC delimiters below.
#   * Finally, don't worry about the indent, CocoaPods strips it!

  s.description      = <<-DESC
  智家 App 国内版特有差异代码库。
                       DESC

  s.homepage         = 'https://git.haier.net/uplus/ios/UplusSpecial.git'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = '海尔优家智能科技（北京）有限公司'
  s.source           = { :git => 'https://git.haier.net/uplus/ios/UplusSpecial.git', :tag => s.version.to_s }
  s.module_name      = 'UplusSpecial'
  s.ios.deployment_target = '12.0'

  s.swift_version = '5.0'
  s.source_files = 'UplusSpecial/*.{h,m,mm,c,cc,cpp,swift}','UplusSpecial/**/*.{h,m,mm,c,cc,cpp,swift}'

  s.resources   = 'UplusSpecial/Resource/UplusSpecial.bundle'

  s.exclude_files = '**/state'

    s.dependency 'SnapKit'
  	s.dependency 'AFNetworking', '>= 4.0.1'
    s.dependency 'MJExtension'
    s.dependency 'AMapFoundation'
  	s.dependency 'GYSDK', '*******'
  	s.dependency 'UPCore','>= 4.0.0'
  	s.dependency 'uplog'
  	s.dependency 'UPTools/Others'
  	s.dependency 'UPCrashDefend'
  	s.dependency 'upuserdomain', '>= 3.0.4'
  	s.dependency 'UpPermissionManager', '>= 0.1.15'
  	s.dependency 'UplusKit','>=1.7.0'
  	s.dependency 'AMapSearch','7.4.0'
  	s.dependency "UPDeviceInitKit", ">=1.0.0"
  	s.dependency 'UpCrash','>=1.0.1'
  	s.dependency 'UpTrace','>=1.0.0'
  	s.dependency "UPUpgrade", ">=1.0.0"
  	s.dependency "UPPush", ">=1.0.0"
  	s.dependency "UPVDN", ">=2.5.17"
  	s.dependency "UpVdnModule", '>= 2.4.0'
  	s.dependency 'UPHttpDNS', '>=1.0.5'
    s.dependency 'uSDK'
    s.dependency 'uAnalytics'
    s.dependency 'UPResource'
    s.dependency 'UPPageTrace'
    s.dependency 'LaunchKitCommon', '>= 1.1.0'
    s.dependency 'UsdkPlugin'
    s.dependency 'BuglyPro/CrashMonitor', '*******'
    s.dependency 'BuglyPro/LooperMonitor', '*******'
    
    s.dependency 'Hainer'
    s.dependency 'HaierCamera'
    s.dependency 'UPPrivacyPolicy'
    s.dependency 'UPGrowingIOTrace'
    s.dependency 'UpLinkage'
    s.dependency 'UPCache'
    s.dependency 'UPCommon'
    s.dependency 'scene'
    s.dependency 'upnfc'
    s.dependency 'HCameraUI'
    s.dependency 'UPDeviceVideo'
    s.dependency 'UPShortCut'
    s.dependency 'UpCustomURLScheme'
    s.dependency 'UpAlipayPlugin'
    s.dependency 'UpQQMusic'
    s.dependency 'UpUMSUnifyPayPlugin'
    s.dependency 'UpUnionPayPlugin'
    s.dependency 'UpUmengPlugin'
    s.dependency 'UpUMShare'
    s.dependency 'upsocial'
    s.dependency 'UpPlugins'
    s.dependency 'UPJPushChannel'
    
    s.dependency 'UpNebula/Core','>= 5.2.0'
    s.dependency 'UpNebula/Media','>= 5.2.0'
    s.dependency 'UpNebula/AudioRecorder','>= 5.2.0'
    s.dependency 'UpNebula/BLE','>= 5.2.0'
    s.dependency 'UpNebula/PhoneCapacities','>= 5.2.0'
    s.dependency 'UpNebula/Device','>= 5.2.0'
    s.dependency 'UpNebula/Health','>= 5.2.0'
    s.dependency 'UpNebula/SpecialInterface','>= 5.2.0'
    s.dependency 'UpNebula/Location','>= 5.2.0'
    s.dependency 'UpNebula/Log','>= 5.2.0'
    s.dependency 'UpNebula/Network','>= 5.2.0'
    s.dependency 'UpNebula/OtherCapacities','>= 5.2.0'
    s.dependency 'UpNebula/Resource','>= 5.2.0'
    s.dependency 'UpNebula/Storage','>= 5.2.0'
    s.dependency 'UpNebula/Statistic','>= 5.2.0'
    s.dependency 'UpNebula/User','>= 5.2.0'
    s.dependency 'UpNebula/Tools','>= 5.2.0'
    s.dependency 'UpNebula/uaiSDK','>= 5.2.0'
    s.dependency 'UpNebula/VDN','>= 5.2.0'
    s.dependency 'UpNebula/DesktopShortCut','>= 5.2.0'
    s.dependency 'UpNebula/NFC','>= 5.2.0'


    s.dependency 'UpMpaaSPlugin/AlipayDepends','0.2.3.**********'
    s.dependency 'UpMpaaSPlugin/Alipay','0.2.3.**********'
    s.dependency 'UpMpaaSPlugin/CameraUI','0.2.3.**********'
    s.dependency 'UpMpaaSPlugin/HttpDNS','0.2.3.**********'
    s.dependency 'UpMpaaSPlugin/Scancode','0.2.3.**********'
    s.dependency 'UpMpaaSPlugin/Base','0.2.3.**********'
    s.dependency 'ABTestConfig', '>= 0.0.1'
    s.dependency 'Toast','4.0.0'
    s.dependency 'YYImage', '1.0.4'
    s.dependency 'YYCategories', '1.0.4'
    s.dependency 'IQKeyboardManager', '6.5.18'
    s.dependency 'YYText', '1.0.7'
    s.dependency 'libOpenInstallSDK', '2.5.0'
    s.dependency 'UplusBase/TabBar', '>= 2.7.0'
    s.dependency 'AMapLocation','2.6.5'
    s.dependency 'YYModel', '>= 1.0.4'
    s.dependency 'UHMasonry', '>= 1.1.1'
    s.dependency 'UHWebImage', '>= 3.8.5'
    s.dependency 'UHProgressHUD', '>= 0.8.1'
    s.dependency 'UPStorage', '>= 1.6.0'
    s.dependency 'lottie-ios','4.4.0'
    s.dependency 'upnetwork', '>= 4.0.6'
    s.dependency 'UpScan', '>= 2.0.0'
    s.dependency 'BindScan', '>=1.0.0'
    s.dependency 'UpPedometerManager', '>=0.1.0'
    s.pod_target_xcconfig = {
      "DEFINES_MODULE" => "YES"
    }

end
