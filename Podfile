source "https://code.aliyun.com/mpaas-public/podspecs.git"
source "https://git.haier.net/uplus/shell/cocoapods/Specs.git"
#source 'https://github.com/CocoaPods/Specs.git'
#source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
source 'https://cdn.cocoapods.org'

platform :ios, '12.0'

use_modular_headers!

target 'UplusSpecial' do

  pod 'LaunchKitCommon', '1.1.0.2025032202'
   
  pod 'GYSDK', '3.1.0.0'
  
  pod 'UPResource', '2.26.5.2025072201'
  pod 'UPVDN','2.7.5.2025061201'
  pod 'UpVdnModule', '2.4.0.2025032201'
  pod 'uplog', '1.7.6.2024091201'
  pod 'upnetwork','4.0.8.2025062301'
  pod 'BuglyPro/CrashMonitor', '*******'
  pod 'BuglyPro/LooperMonitor', '*******'
  
  pod 'UpNetworkPlugin','0.1.6.2025031801'
  
  pod 'upuserdomain','3.33.0.2025071801'
  pod 'AFNetworking','4.0.1'
  pod 'YYModel', '1.0.4'
  pod 'MJExtension', '3.2.1'
  pod 'UHWebImage', '3.8.7.2024012901'
  pod 'Aspects', '1.4.1'
  pod 'ZipArchive', '1.4.0'
  pod 'Protobuf', '3.27.3'
  pod 'UPPageTrace','3.1.4.2025062702'
  pod 'FMDB', '2.7.10'
  pod 'SnapKit', '5.7.1'
  pod 'UPStorage','1.8.0.2024091001'
  pod 'UPTools/Others','1.0.1.2025040101'
  pod 'AMapLocation','2.6.5'
  pod 'AMapSearch','7.4.0'
  pod 'AMapFoundation'
  pod 'UPUpgrade', '1.8.0.2025052101'
  pod 'UpCrash', '1.4.2.2024091101'
  pod 'UPDeviceInitKit','1.22.1.2025072801'
  pod 'UPDeviceWidgetExtensionEngine', '2.4.0.2025041701'
  pod 'UPDeviceGroupData', '1.4.0.2025072801'
  pod 'LogicEngine','3.11.0.2025051901'
  pod 'UplusKit', '1.8.3.2025070801'
  pod 'UPPush','1.7.0.2025062501'
  pod 'libOpenInstallSDK','2.5.0'
  pod 'UpTracePlugin','1.0.0'
  
  pod 'UPCore', '4.2.0.2025062401'
  
  pod 'UPDevice', '7.41.0.2025081101'
  pod 'uSDK','10.10.0.2025072101'
  pod 'UPJPushChannel','1.1.0.2025032201'
  pod 'UPHttpDNS','*******.2023013001'
  pod 'UHProgressHUD', '0.8.4.2024012901'
  pod 'UplusBase', '2.7.2.2025060501'
  pod 'UpPlugins','3.4.3.2025071801'
  pod 'upsocial','1.5.0.2025032201'
  pod 'UpUMShare','1.6.0.2025032501'
  pod 'UpUmengPlugin','1.0.0.2025041002'
  pod 'UpPermissionManager', '1.2.0.2025070301'
  pod 'UpOssPlugin','0.1.7'
  pod 'UpUnionPayPlugin','1.0.0.2025032201'
  pod 'UPCrashDefend', '0.1.0.1.2023020901'
  pod 'UpMpaaSPlugin/Base', '0.1.3.2024032001'
  pod 'lottie-ios', '4.4.0'
  pod 'UPSafeColletion', '1.0.0.2023112301'
  pod 'UPNetworkConfigPlugin','1.4.0.2025031201'
  pod 'UPBindConfigPlugin', '1.9.6.2025072801'
  pod 'UPDiscoverDevice', '1.2.0.2024101703'
  pod 'UpScan', '2.0.2.2025072501'
  pod 'UsdkPlugin','1.4.0.2025072802'
  pod 'ABTestConfig','0.1.1.2025071001'
  pod 'UPPrivacyPolicy', '1.1.0.2025032201'
  pod 'UPGrowingIOTrace', '1.5.0.2025032901'
  pod 'UpTrace','1.3.5.2025032901'
  pod 'UpLinkage', '1.5.0.2025032201'
  pod 'UPCache', '1.4.0.2025032201'
  pod 'UPCommon', '1.0.0.2025032201'
  
  pod 'BindScan','1.2.0.2025072501'
  
  pod 'scene', '1.27.1.2025072901'
  pod 'upnfc', '1.1.0.2025041801'
  pod 'Hainer', '1.22.1.2025061201'
  pod 'HCameraUI', '3.10.0.2025041801'
  pod 'HaierCamera', '2.14.3'
  pod 'UPDeviceVideo', '4.2.0.2025032201'
  pod 'UPShortCut', '1.0.1.2025072301'
  pod 'UpCustomURLScheme', '1.0.0.2025032201'
  pod 'UpAlipayPlugin', '1.0.0.2025032201'
  pod 'UpQQMusic', '1.0.0.2025032201'
  pod 'UpUMSUnifyPayPlugin', '1.0.0.2025032201'
  pod 'YYCategories', '1.0.4'
  pod 'UHMasonry', '1.1.2.**********'
  pod 'YYText', '1.0.7'
  pod 'UpPedometerManager','0.2.0.**********'


  # 必须指定每个subspec的版本,没指定版本的subspec会拉下来最大的版本
  pod 'UpNebula/Core', '5.3.0.**********'
  pod 'UpNebula/Media', '5.3.0.**********'
  pod 'UpNebula/AudioRecorder', '5.3.0.**********'
  pod 'UpNebula/BLE', '5.3.0.**********'
  pod 'UpNebula/PhoneCapacities', '5.3.0.**********'
  pod 'UpNebula/Device', '5.3.0.**********'
  pod 'UpNebula/Health', '5.3.0.**********'
  pod 'UpNebula/SpecialInterface', '5.3.0.**********'
  pod 'UpNebula/Location', '5.3.0.**********'
  pod 'UpNebula/Log', '5.3.0.**********'
  pod 'UpNebula/Network', '5.3.0.**********'
  pod 'UpNebula/OtherCapacities', '5.3.0.**********'
  pod 'UpNebula/Resource', '5.3.0.**********'
  pod 'UpNebula/Storage', '5.3.0.**********'
  pod 'UpNebula/Statistic', '5.3.0.**********'
  pod 'UpNebula/User', '5.3.0.**********'
  pod 'UpNebula/Tools', '5.3.0.**********'
  pod 'UpNebula/uaiSDK', '5.3.0.**********'
  pod 'UpNebula/VDN', '5.3.0.**********'
  pod 'UpNebula/DesktopShortCut', '5.3.0.**********'
  pod 'UpNebula/NFC', '5.3.0.**********'
end
