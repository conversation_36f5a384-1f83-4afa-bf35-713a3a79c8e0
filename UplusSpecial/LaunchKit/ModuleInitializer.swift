//
//  ModuleInitializer.swift
//  UplusSpecial
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/11.
//

import Foundation
import LaunchKitCommon
import uplog

public class ModuleInitializer {
  public static let shared: ModuleInitializer = ModuleInitializer()
  private init() {}
}

extension ModuleInitializer: ModuleInitialization {
  public func initializeSystemModules() {
    let modules: [ModuleName] = [
      .Base, .Guest, .Growing, .Linkage, .Nebula,
      .InitKit, .LaunchTime, .VDN, .Cache, .Special
    ]

    let workflow = Workflow(name: "system")
    let launchKit = LaunchKit.shared

    for mName in modules {
      UPPrintDebug(moduleName: "UplusSpecial", message: "[LaunchKit]|Add launch task to system stage: \(mName)")
      launchKit.addTask(to: workflow, for: mName, at: .system)
    }

    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageSystem(BeforeDidFinishLaunch) start...")
    workflow.start()
    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageSystem(BeforeDidFinishLaunch) finish.")
  }
  
  public func initializeModulesBeforePrivacy() {
    let modules: [ModuleName] = [
      .Cache, .Nebula, .Special, .GeneralTask,
      .VDN, .UplusKit, .Analytics, .InitKit, .MainBox,
      .BSBussinessClient, .Scene, .NFC,
      .Social, .Hainer, .CameraAccess, .HCDeviceVideoAccess,
      .DeviceVideoAccess, .Guest, .Shortcut, .Linkage, .MainPage,
      .Login, .MainLib, .SNS, .OneKeyLogin, .Scan
    ]

    let workflow = Workflow(name: "beforePrivacy(didFinishLaunch-setup-init)")
    let launchKit = LaunchKit.shared
    for mName in modules {
      UPPrintDebug(moduleName: "UplusSpecial", message: "[LaunchKit]|Add launch task to beforePrivacy stage: \(mName)")
      launchKit.addTask(to: workflow, for: mName, at: .beforePrivacy)
    }

    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageBeforePrivacy(DidFinishLaunch-setup-init) start...")
    workflow.start()
    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageBeforePrivacy(DidFinishLaunch-setup-init) finish.")
  }
  
  public func initializeModulesAfterPrivacy() {
    let modules: [ModuleName] = [
      .PushChannel, .Guest, .UMengCommunity, .UplusKit, .Analytics,
      .InitKit, .MainLib, .MainBox, .Auth, .BSBussinessClient,
      .UP3DTouch, .Login, .UsdkDiscover, .OneKeyLogin, .Health,
      .Scan
    ]

    let workflow = Workflow(name: "afterPrivacy")
    let launchKit = LaunchKit.shared
    for mName in modules {
      UPPrintDebug(moduleName: "UplusSpecial", message: "[LaunchKit]|Add launch task to afterPrivacy stage: \(mName)")
      launchKit.addTask(to: workflow, for: mName, at: .afterPrivacy)
    }

    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageAfterPrivacy(LazyInit) start...")
    workflow.start()
    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageAfterPrivacy(LazyInit) finish.")
  }
  
  public func initializeModulesBeforeMainPage() {
    let workflow = Workflow(name: "beforeMainPage")

    UPPrintDebug(moduleName: "UplusSpecial", message: "[LaunchKit]|Add launch task to beforeMainPage stage: \(ModuleName.Base)")

    LaunchKit.shared.addTask(to: workflow, for: .Base, at: .beforeMainPage)

    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageBeforeMainPage start...")
    workflow.start()
    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageBeforeMainPage finish.")
  }

  public func initializeModulesAfterMainPage() {
    let modules: [ModuleName] = [
      .Nebula, .UplusKit, .Login
    ]

    let workflow = Workflow(name: "afterMainPage")
    let launchKit = LaunchKit.shared
    for mName in modules {
      UPPrintDebug(moduleName: "UplusSpecial", message: "[LaunchKit]|Add launch task to afterMainPage stage: \(mName)")
      launchKit.addTask(to: workflow, for: mName, at: .afterMainPage)
    }

    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageBeforeMainPage(AfterDidFinishLaunch) start...")
    workflow.start()
    UPPrintInfo(moduleName: "UplusSpecial", message: "[LaunchKit]|LaunchStageBeforeMainPage(AfterDidFinishLaunch) finish.")
  }
}
