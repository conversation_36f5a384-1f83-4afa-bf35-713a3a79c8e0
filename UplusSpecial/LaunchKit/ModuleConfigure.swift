//
//  ModuleConfigure.swift
//  UplusSpecial
//
//  Created by lubiao on 2025/3/11.
//

import Foundation
import LaunchKitCommon
import Hainer
import HCameraUI
import HaierCamera
import UPCache
import UPCommon
import UPDeviceVideo
import UPDeviceWidgetExtensionEngine
import UPGrowingIOTrace
import UPJPushChannel
import UPPrivacyPolicy
import UPShortCut
import UpAlipayPlugin
import UpCustomURLScheme
//import UpJVerification
import UpLinkage
import UpNebula
import UpPlugins
import UpQQMusic
import UpUMSUnifyPayPlugin
import UpUMShare
import UpUmengPlugin
import UpUnionPayPlugin
import UpVdnModule
import UplusBase
import UplusKit
import BindScan
import scene
import upnfc
import upsocial
import UplusBase
import UsdkPlugin
import UpScan
import UpPedometerManager

public extension ModuleName {
  /// 差异库中的模块
  static let LaunchTime: ModuleName = "LaunchTime"
  static let CommonScheme: ModuleName = "CommonScheme"
  static let MainBox: ModuleName = "MainBox"
  static let Analytics: ModuleName = "Analytics"
  static let SNS: ModuleName = "SNS"
  static let Auth: ModuleName = "Auth"
  static let MainLib: ModuleName = "MainLib"
  static let Login: ModuleName = "Login"
  static let InitKit: ModuleName = "InitKit"
  static let Special: ModuleName = "Special"
  static let UP3DTouch: ModuleName = "3DTouch"
  static let OneKeyLogin: ModuleName = "GyVerification"
  
  /// 非本库中的模块
  static let Base: ModuleName = "UplusBaseModule"
  static let Guest: ModuleName = "UPGuestModule"
  static let Growing: ModuleName = "UPGrowingIOTrace"
  static let Linkage: ModuleName = "UpLinkageMoudle"
  static let Nebula: ModuleName = "UpNebulaModule"
  static let VDN: ModuleName = "UpVdnModule"
  static let GeneralTask: ModuleName = "UPGeneralTaskModule"
  static let Health: ModuleName = "UpHealthModule"
  static let UplusKit: ModuleName = "UplusKit"
  static let BSBussinessClient: ModuleName = "BSBussinessClientModule"
  static let Scene: ModuleName = "SSModule"
  static let NFC: ModuleName = "NFCModule"
  static let Social: ModuleName = "UpSocialModule"
  static let Hainer: ModuleName = "HNModule"
  static let CameraAccess: ModuleName = "HCameraAccessModule"
  static let HCDeviceVideoAccess: ModuleName = "HCUPDeviceVideoAccessModule"
  static let Cache: ModuleName = "UPCacheModule"
  static let DeviceVideoAccess: ModuleName = "UPDeviceVideoAccessModule"
  static let Shortcut: ModuleName = "UPShortCutModule"
  static let MainPage: ModuleName = "UpMainPageModule"
  static let PushChannel: ModuleName = "UPPushChannel"
  static let UMengCommunity: ModuleName = "UPUMengCommunityModule"
  static let URLScheme: ModuleName = "UpURLSchemeModule"
  static let DeviceWidget: ModuleName = "UPDeviceWidgetModule"
  static let QQMusic: ModuleName = "UpQQMusicAuthModule"
  static let Alipay: ModuleName = "AliPayModule"
  static let OldWeChatPay: ModuleName = "OldWeChatPayModule"
  static let WeChatPay: ModuleName = "NewWeChatPayModule"
  static let UninPay: ModuleName = "UpUnionPayModule"
  static let UnifyPay: ModuleName = "UpUMSUnifyPayModule"
  static let UsdkDiscover: ModuleName = "UsdkDiscoverModule"
  static let Scan: ModuleName = "UpScanModule"
}

public class ModuleConfigure: ModuleConfiguration {
  public static let shared: ModuleConfigure = ModuleConfigure()
  private init() {}
  
  public var allModules: [ModuleName : ModuleProtocol] {
    ModuleConfigure._allModules
  }
  
  public func dependencies(
    for module: ModuleName,
    at stage: LaunchStage
  ) -> [ModuleName]? {
    dependencyTable[stage]?[module]
  }
  
  static let _allModules: [ModuleName: ModuleProtocol] = [
    .LaunchTime: UpLaunchTimeReportModule(),
    .CommonScheme: UpUplusCommonSchemeModule(),
    .MainBox: MBMainBoxModule(),
    .Analytics: AnalyticsModule(),
    .SNS: SNSModule(),
    .Auth: UpAuthModule(),
    .MainLib: MainLibModule(),
    .Login: LGModule(),
    .InitKit: InitBaseKit(),
    .Special: UplusSpecialModule(),
    .UP3DTouch: UP3DTouchModule(),
    .OneKeyLogin: GyOneKeyLoginModule(),
    .Health: UpHealthModule(),
    .Hainer: HNModule(),
    .CameraAccess: HCameraAccessModule(),
    .HCDeviceVideoAccess: HCUPDeviceVideoAccessModule(),
    .Cache: UPCacheModule(),
    .GeneralTask: UPGeneralTaskModule(),
    .DeviceVideoAccess: UPDeviceVideoAccessModule(),
    .DeviceWidget: UPDeviceWidgetModule(),
    .Growing: UPGrowingIOTrace(),
    .PushChannel: UPPushChannel(),
    .Guest: UPGuestModule(),
    .Shortcut: UPShortCutModule(),
    .Alipay: AliPayModule(),
    .URLScheme: UpURLSchemeModule(),
    .Linkage: UpLinkageMoudle(),
    .Nebula: UpNebulaModule(),
    .OldWeChatPay: OldWeChatPayModule(),
    .QQMusic: UpQQMusicAuthModule(),
    .UnifyPay: UpUMSUnifyPayModule(),
    .UMengCommunity: UPUMengCommunityModule(),
    .WeChatPay: NewWeChatPayModule(),
    .UninPay: UpUnionPayModule(),
    .VDN: UpVdnModule(),
    .MainPage: UpMainPageModule(),
    .UplusKit: UplusKit(),
    .BSBussinessClient: BSBussinessClientModule(),
    .Scene: SSModule(),
    .NFC: NFCModule(),
    .Social: UpSocialModule(),
    .Base: UplusBaseModule(),
    .UsdkDiscover: UsdkDiscoverModule(),
    .Scan: UpScanModule()
  ]
  
  private let dependencyTable: [LaunchStage: [ModuleName: [ModuleName]]] = [
    .system: [:],
    .beforePrivacy: [:],
    .afterPrivacy: [:],
    .beforeMainPage : [:],
    .afterMainPage: [:]
  ]
}
