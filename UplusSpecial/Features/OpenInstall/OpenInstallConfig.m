//
//  OpenInstallConfig.m
//  UplusSpecial
//
//  Created by 吴鹏举 on 2022/11/11.
//

#import "OpenInstallConfig.h"
#import <UPVDN/Page.h>
#import <libOpenInstallSDK/OpenInstallSDK.h>
#import <UPPageTrace/UPPageTraceInjection.h>
#import "InviteCodeViewModel_special.h"
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>

@interface OpenInstallConfig () <OpenInstallDelegate>

@end


@implementation OpenInstallConfig

// 获取单例
+ (instancetype)shareConfig
{
    static dispatch_once_t onceDispatch;
    static OpenInstallConfig *instance;
    dispatch_once(&onceDispatch, ^{
      instance = [[OpenInstallConfig alloc] init];
      // 开始配置
      [instance loadConfig];
    });
    return instance;
}

// 开始配置openinstall
- (void)loadConfig
{
    [OpenInstallSDK initWithDelegate:self];

    [self openInstallFirstUse];
}

- (void)openInstallFirstUse
{
    BOOL isFirstInstall =
        [[NSUserDefaults standardUserDefaults] boolForKey:@"openInstallFirst"];
    if (!isFirstInstall) {
        [[OpenInstallSDK defaultManager]
            getInstallParmsCompleted:^(OpeninstallData *_Nullable appData) {
              [[NSUserDefaults standardUserDefaults] setBool:YES
                                                      forKey:@"openInstallFirst"];
              [self getWakeUpParams:appData];
            }];
    }
}

//通过OpenInstall获取已经安装App被唤醒时的参数（如果是通过渠道页面唤醒App时，会返回渠道编号）
- (void)getWakeUpParams:(OpeninstallData *)appData
{
    if (appData.data) { //(动态唤醒参数)
        NSString *type = appData.data[@"type"];
        if (![type isKindOfClass:[NSString class]]) {
            return;
        }
        if ([type isEqualToString:@"launchToPage"]) {
            NSString *url = appData.data[@"targetUrl"];
            [UpVdn goToPage:url
                       flag:VdnPageFlagPush
                 parameters:nil
                   complete:nil
                      error:nil];
        }
        else if ([type isEqualToString:@"inviteSource"]) {
            [self getCodeData:appData.data];
        }
        // e.g.如免填邀请码建立邀请关系、自动加好友、自动进入某个群组或房间等
    }
    if (appData.channelCode) { //(通过渠道链接或二维码唤醒会返回渠道编号)
        // e.g.可自己统计渠道相关数据等
    }
    NSLog(@"OpenInstallSDK:\n动态参数：%@;\n渠道编号：%@", appData.data,
          appData.channelCode);
}

- (void)getCodeData:(NSDictionary *)dic
{
    //请求openinstall的逻辑只有在未登录情况下存在

    [[UPPageTraceInjection getInstance]
            .pageTraceManager
        savePageClickEvent:@"https://zj.haier.net/omsappapi/inviteUser"
               action_Code:@"0003"
               extend_info:dic];

    [InviteCodeViewModel_special addInviteCodeDic:dic];
}

@end
