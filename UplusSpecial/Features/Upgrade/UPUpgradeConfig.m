//
//  UPUpgradeConfig.m
//  UplusKit
//
//  Created by 景彦铭 on 2022/8/30.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPUpgradeConfig.h"

@implementation UPUpgradeConfig

- (instancetype)init
{
    if (self = [super init]) {
        self.maxAlertCount = NSIntegerMax;
        self.maxBetaAlertCount = 1;
        self.maxBetaExpirationAlertCount = 3;
        self.alertInterval = 2 * 24 * 60 * 60;
        self.alertIntervalBeforeBetaExpiration = 7 * 24 * 60 * 60;
    }
    return self;
}

@end
