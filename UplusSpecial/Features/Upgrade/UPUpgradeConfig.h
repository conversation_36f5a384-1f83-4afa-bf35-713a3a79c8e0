//
//  UPUpgradeConfig.h
//  UplusKit
//
//  Created by 景彦铭 on 2022/8/30.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPUpgradeConfig : NSObject

/// 全量邀请最大弹窗次数 NSIntegerMax 不做限制
@property (nonatomic, assign) NSInteger maxAlertCount;

/// 灰度邀请最大弹窗次数 NSIntegerMax 不做限制
@property (nonatomic, assign) NSInteger maxBetaAlertCount;

/// 灰度版本到期最大弹窗次数 NSIntegerMax 不做限制
@property (nonatomic, assign) NSInteger maxBetaExpirationAlertCount;

/// 弹窗间隔时间
@property (nonatomic, assign) NSTimeInterval alertInterval;

/// 内测到期提醒策略，到期前多少秒开始提醒
@property (nonatomic, assign) NSTimeInterval alertIntervalBeforeBetaExpiration;

@end

NS_ASSUME_NONNULL_END
