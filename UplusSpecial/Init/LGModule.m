//
//  LGModule.m
//  Uplus
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/5/10.
//  Copyright © 2017年 北京海尔广科数字技术有限公司. All rights reserved.
//

#import "LGModule.h"
#import <UPLog/UPLog.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <UPPageTrace/UPPageTraceInjection.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import "UPNetWorkHeader.h"
#import "JoinFamilyAlertPatcher.h"
#import "UPFamilyFloatLauncher.h"
#import "UserCenterAddStatusPatcher.h"
#import <UPTools/KVNProgressShow.h>
#import <UPCore/UPFunctionToggle.h>
#import <UPResource/UPResourceInjection.h>
#import <UPResource/UPResourceInfo.h>
#import "UplusSpecial-Swift.h"
#import <UPResource/UPResourceInjection.h>
#import <UPStorage/UPStorage.h>
#import <UpTrace/UPEventTrace.h>
#import <BuglyProCore/Bugly.h>

/// Bugly SDK 日志输出
void buglyLogCallback(RMLoggerLevel level, const char *log)
{
    UPLogInfo(@"[UpCrash]", @"[CrashPlatform]|Bugly log: %s", log);
}

@import ABTestConfig;
static NSString *const KgoBackFlag = @"goBack";
static NSString *const Kdevicebind = @"devicebind";

@interface LGModule () <UpWorkflowTask, UpUserDomainObserver,
                        UPResourceCallback>

@property (nonatomic) UpLaunchStage currentStage;
@end

@implementation LGModule {
}

#pragma mark - Public Methods
- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    self.currentStage = stage;
    switch (stage) {
        case UpLaunchStageBeforePrivacy:
        case UpLaunchStageAfterMainPage:
        case UpLaunchStageAfterPrivacy:
            return self;
        default:
            return nil;
    }
}

- (void)run
{
    switch (self.currentStage) {
        case UpLaunchStageBeforePrivacy:
            [self initBeforePrivacy];
            break;
        case UpLaunchStageAfterPrivacy:
            [self initAfterPrivacy];
            break;
        case UpLaunchStageAfterMainPage:
            [self initAfterMainPage];
        default:
            break;
    }
}

- (void)initBeforePrivacy
{
    [[UpUserDomainHolder instance].userDomain addObserver:self];

    [UpVdn addLauncher:[UPFamilyFloatLauncher new]];

    [UpVdn addLauncher:[DeviceBindLauncher new]];

    [UpVdn registerLogicPatch:[[JoinFamilyAlertPatcher alloc] init]];

    [UpVdn registerLogicPatch:UserCenterAddStatusPatcher.new];

    [UpVdn registerLogicPatch:DeviceBindPatcher.new];
}

- (void)initAfterPrivacy
{
    [self initCrashPlatform];
    [[ABTestConfigManager shared] initABTest];
}

- (void)initAfterMainPage
{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)),
                   dispatch_get_global_queue(0, 0), ^{
                     [self preloadABResList];
                   });
}

- (void)onAppActive
{
    [[NSNotificationCenter defaultCenter]
        postNotificationName:@"MainBoxModuleDidBecomeActive"
                      object:nil];
}

- (void)initCrashPlatform
{
    // "Uplus", "Bugly", "APM", "ThirdPart"
    NSString *crashPlatform = [UPStorage getMemoryString:@"CrashReportPlatform" defaultValue:@"Uplus"];
    UPLogInfo(@"[UpCrash]", @"[CrashPlatform]|FunctionToggle: Crash platform is %@", crashPlatform);
    if (![crashPlatform isEqualToString:@"Uplus"]) {
        [self initBuglySDK];
    }
}

- (void)initBuglySDK
{
    UPLogInfo(@"[UpCrash]", @"[CrashPlatform]|Init Bugly SDK");
    // UPStorage中存的可能是"ThirdPart",修改为"Bugly"
    [UPStorage putMemoryString:@"CrashReportPlatform" value:@"Bugly"];
    [UPEventTrace.getInstance trace:@"MB37134" withVariable:@{ @"data_type" : @"Bugly" }];

    BuglyConfig *config = [[BuglyConfig alloc] initWithAppId:@"cb097318f7" appKey:@"a594ef26-671f-4ef3-9f03-5ae917acc57c"];
#if Debug
    config.buildConfig = BuglyBuildConfigDebug;
#else
    config.buildConfig = BuglyBuildConfigRelease;
#endif
    config.deviceIdentifier = UPNetworkSettings.sharedSettings.clientID;
    NSString *userId = UpUserDomainHolder.instance.userDomain.user.user_center_userId;
    if (userId.length) {
        config.userIdentifier = userId;
    }

    [Bugly registerLogCallback:buglyLogCallback];

    [Bugly start:@[ BUGLY_MODULE_CRASH, RM_MODULE_LOOPER ]
                 config:config
        completeHandler:^{
          UPLogInfo(@"[UpCrash]", @"[CrashPlatform]| Init Bugly SDK finished.")
        }];
}

- (void)preloadABResList
{

    NSArray *resNameList =
        [[UPFunctionToggle shareInstance] toggleValueForKey:@"preLoadList"];
    if (resNameList.count > 0) {

        NSMutableArray *resInfoList = [NSMutableArray array];
        [resNameList
            enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj, NSUInteger idx,
                                         BOOL *_Nonnull stop) {
              UpPreloadResourceInfo *info = UpPreloadResourceInfo.new;
              info.name = obj[@"name"];
              info.type = [UPResourceInfo typeValueOfString:obj[@"type"]];
              [resInfoList addObject:info];
            }];

        [[UPResourceInjection getInstance]
                .resourceManager preloadNormalResList:resInfoList
                                             callback:self];
    }
}
- (void)onResult:(BOOL)success
         message:(NSString *)message
    resourceInfo:(UPResourceInfo *)info
{

    UPLogInfo(@"UplusSpecial", @"%s[%d] 预加载资源%@安装结果为%@",
              __PRETTY_FUNCTION__, __LINE__, info.name, message);
}

//基础信息模块代理
//获取缓存鉴权信息（鉴权信息未过期）oauthdata
- (void)onGetCacheTokenSuccess:(ApplicationOauthData *)oauthData
{
    [UPNetWorkHeader defaultHeader].accessToken =
        [UpUserDomainHolder instance].userDomain.oauthData.uhome_access_token;
    [UPNetWorkHeader defaultHeader].userID =
        [UpUserDomainHolder instance].userDomain.oauthData.uhome_user_id;
    [UpVdn setUserId:[UpUserDomainHolder instance]
                         .userDomain.oauthData.uhome_user_id];
}
//刷新Token成功 oauthdata
- (void)onRefreshTokenSuccess:(ApplicationOauthData *)oauthData
{
    [UPNetWorkHeader defaultHeader].accessToken =
        [UpUserDomainHolder instance].userDomain.oauthData.uhome_access_token;
    [UPNetWorkHeader defaultHeader].userID =
        [UpUserDomainHolder instance].userDomain.oauthData.uhome_user_id;

    NSString *userId = [UpUserDomainHolder instance].userDomain.oauthData.uc_user_id;
    [[ABTestConfigManager shared] onProfileSignIn:userId];

    NSString *platform = [UPStorage getMemoryString:@"CrashReportPlatform" defaultValue:@"Uplus"];
    if ([platform isEqualToString:@"Bugly"]) {
        [Bugly updateUserIdentifier:userId];
    }
}

- (void)onTokenMismatchDevice
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      [KVNProgressShow showText:@"终端变化，登录已过期"];
    });
}

- (void)onRefreshDeviceListSuccess:(User *)user
{
    //老逻辑，搜索没有找到其他地方使用这个匹配的字符串，暂时保留
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSDictionary *devices = user.devices;
    if (devices != nil && devices.count > 0) {
        [userDefault setBool:YES forKey:@"deviceUserKey"];
        [userDefault synchronize];
    }
    else {
        [userDefault setBool:NO forKey:@"deviceUserKey"];
        [userDefault synchronize];
    }
}

- (void)onLogOut:(UpUserDomain *)userDomain
{

    //停止UID的统计 ABTest使用
    [[ABTestConfigManager shared] onProfileSignOff];
    [Bugly updateUserIdentifier:@""];

    //取消单点登录的观察者
    [UPNetWorkHeader defaultHeader].accessToken = nil;
    [UPNetWorkHeader defaultHeader].userID = nil;

    dispatch_async(dispatch_get_main_queue(), ^{
      UIViewController *currentViewController =
          [UpVdnUtils getCurrentViewController];
      if ([currentViewController
              isKindOfClass:NSClassFromString(
                                @"VHLiveSaleWatchViewController")]) {
          return;
      }
      [UpVdn goToPage:@"https://uplus.haier.com/uplusapp/smart/index.html"
                 flag:VdnPageFlagPush
           parameters:nil
             complete:nil
                error:nil];
    });
}

@end
