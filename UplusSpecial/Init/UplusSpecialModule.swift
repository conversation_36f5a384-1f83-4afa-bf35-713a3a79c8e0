//
//  UplusSpecialModule.swift
//  UplusSpecial
//
//  Created by whenwe on 2023/8/14.
//

import Foundation
import uplog
import UPCore
import UPResource
import LaunchKitCommon
import UPStorage

let FunctionToggleResName = "FunctionToggle"
let PhoneModelResName = "DeviceModels"

@objc open class UplusSpecialModule: NSObject, ModuleProtocol, WorkflowTask {
    
    var toggleResInfo: UPResourceInfo?
    var phoneModelResInfo: UPResourceInfo?
    let UPStoragePhoneModelKey = "PhoneModels/PhoneModel"
  
  public func initializeTask(for stage: LaunchStage) -> (any WorkflowTask)? {
    return stage == .beforePrivacy ? self : nil
  }
  
  public func run() {
    let rejection = UPResourceInjection.getInstance()
    rejection.syncPresetResPkg(FunctionToggleResName, type: .configFile)
    let togglePreResInfo = rejection.resourceManager.getCommonResource(FunctionToggleResName, type: .configFile, selector: self, callback: self, listener: self);
    if let togglePreResInfo = togglePreResInfo, togglePreResInfo.active {
      toggleResInfo = togglePreResInfo
      self.updateToggleData(info: togglePreResInfo)
    }
      
    let storagePhoneModelValue = UPStorage.getStringValue(UPStoragePhoneModelKey, defaultValue: "")!
    guard storagePhoneModelValue.isEmpty else { return }
    rejection.syncPresetResPkg(PhoneModelResName, type: .configFile)
    let phoneModelPreResInfo = rejection.resourceManager.getCommonResource(PhoneModelResName, type: .configFile, selector: self, callback: self, listener: self);
    if let phoneModelPreResInfo = phoneModelPreResInfo, phoneModelPreResInfo.active {
      phoneModelResInfo = phoneModelPreResInfo
      self.updatePhoneModelData(info: phoneModelPreResInfo)
    }
  }
}

extension UplusSpecialModule: UPResourceCallback, UPResourceListener, UpResourceSelector {
    /// <UpResourceSelector>
    public func select(from infoList: [UPResourceInfo]) -> UPResourceInfo {
        var info: UPResourceInfo =  UPResourceInfo()
        for (_, obj) in infoList.enumerated() {
            if (obj.type == UPResourceType.configFile && obj.name == FunctionToggleResName) || (obj.type == UPResourceType.configFile && obj.name == PhoneModelResName) {
                info = obj
                break
            }
        }
        return info
    }
    
    /// <UPResourceCallback>
    public func onResult(_ success: Bool, message: String?, resourceInfo info: UPResourceInfo?) {
        UPPrintInfo(moduleName: "MainLibModule", message: "onResult:message:resourceInfo:====info.path:\(info?.path ?? "")")
        guard let info = info else { return }
        //如果远端有更新，下载成功 则解析远端下载配置
        if (success == true) {
            if (info.name == FunctionToggleResName && toggleResInfo?.hashStr != info.hashStr) {
                toggleResInfo = info
                self.updateToggleData(info: info)
            }else if (info.name == PhoneModelResName && phoneModelResInfo?.hashStr != info.hashStr) {
                phoneModelResInfo = info
                self.updatePhoneModelData(info: info)
            }
        }
    }
}

extension UplusSpecialModule {
     func updateToggleData(info: UPResourceInfo?) {
        guard let info = info else { return }
        
        let json = try? JSONSerialization.jsonObject(with: Data(contentsOf: URL(fileURLWithPath: info.path)), options: .mutableContainers)
        
        guard let toggleJson = json as? [AnyHashable: Any] else { return }
        
         UPFunctionToggle.shareInstance().updateData(toggleJson)
    }
    
    func updatePhoneModelData(info: UPResourceInfo?) {
       guard let info = info else { return }
       
       let json = try? JSONSerialization.jsonObject(with: Data(contentsOf: URL(fileURLWithPath: info.path)), options: .mutableContainers)
       
       guard let phoneModelJson = json as? [String: String] else { return }
        
       guard let phoneModel = phoneModelJson[getDeviceIdentifier()] else { return }
               
       UPStorage.putStringValue(phoneModel, name: UPStoragePhoneModelKey)
   }
    
    func getDeviceIdentifier() -> String {
        var systemInfo = utsname()
        uname(&systemInfo)
        
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let deviceIdentifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        
        return deviceIdentifier.isEmpty ? "unknown" : deviceIdentifier
    }
}
