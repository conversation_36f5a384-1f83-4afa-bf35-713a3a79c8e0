//
//  UPInitBaseKit.h
//  UPInitBaseKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/10/16.
//

#import <Foundation/Foundation.h>
#import <UplusKit/UplusKit.h>

@import LaunchKitCommon;

@interface InitBaseKit : NSObject <UpModuleProtocol>

/// 初始化U+App基础组件库
/// @param environment 运行环境
/// @param appId 应用ID
/// @param appKey 应用Key
/// @param appVersion 应用版本
/// @param isTestMode 是否灰度
+ (instancetype)initUPInitBaseKit:(UplusKitEnvironment)environment
                            appId:(NSString *)appId
                           appKey:(NSString *)appKey
                       appVersion:(NSString *)appVersion
                       isTestMode:(BOOL)isTestMode;
@end
