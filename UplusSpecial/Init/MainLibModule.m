//
//  MainLibModule.m
//  SYBirdSpecial
//
//  Created by 孙龙刚 on 2022/9/8.
//

#import "MainLibModule.h"
#import "OpenInstallConfig.h"
#import <UPCore/UPContext.h>
#import "InviteCodePatcher_special.h"
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <UPVDN/UpVdnUtils.h>
#import "InviteCodeViewModel_special.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import <uplog/UPLog.h>
#import <libOpenInstallSDK/OpenInstallSDK.h>
#import <UPCore/UPFunctionToggle.h>

@interface MainLibModule () <UpWorkflowTask, UpUserDomainObserver>

@property (nonatomic) UpLaunchStage currentStage;

@property (nonatomic) NSUserActivity *userActivity;

@end

@implementation MainLibModule

static NSString *const VerificationKey = @"Verification.disableInitialize";

- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    self.currentStage = stage;
    switch (stage) {
        case UpLaunchStageBeforePrivacy:
        case UpLaunchStageAfterPrivacy:
            return self;
        default:
            return nil;
    }
}

- (void)run
{
    switch (self.currentStage) {
        case UpLaunchStageBeforePrivacy:
            [self initBeforePrivacy];
            break;
        case UpLaunchStageAfterPrivacy:
            [self initAfterPrivacy];
            break;
        default:
            break;
    }
}

- (void)initBeforePrivacy
{
    [[UpUserDomainHolder instance].userDomain addObserver:self];
    //邀请家人pather
    [UpVdn registerLogicPatch:[[InviteCodePatcher_special alloc] init]];
}

- (void)initAfterPrivacy
{
    [OpenInstallConfig shareConfig];
}

- (BOOL)shouldHandleGuestURL
{
    return YES;
}

- (NSInteger)priorityForEvent:(enum UpLifeCircleEvent)event
{
    NSInteger priority = UpLifeCircleEventPriority.normal;
    if (event == UpLifeCircleEventOpenURL) {
        return UpLifeCircleEventPriority.normal + 30;
    }
    if (event == UpLifeCircleEventUserActivity) {
        return UpLifeCircleEventPriority.normal + 20;
    }
    return UpLifeCircleEventPriority.normal;
}

- (enum UpChainHandledResult)
    onOpenURL:(NSURL *)url
      options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{
    [OpenInstallSDK handLinkURL:url];
    return UpChainHandledResultIgnore;
}

- (enum UpChainHandledResult)
    onContinueUserActivity:(NSUserActivity *)userActivity
        restorationHandler:
            (void (^)(NSArray<id<UIUserActivityRestoring>> *_Nullable))
                restorationHandler
{
    if ([self gotoUniversalLink:userActivity]) {
        UPContext.sharedInstance.platformInfo = @"";
        return UpChainHandledResultTrue;
    }
    return UpChainHandledResultIgnore;
}

- (BOOL)gotoUniversalLink:(NSUserActivity *)userActivity
{
    if (UPContext.sharedInstance.isAgreenPrivacyPolicy) {
        return [self dealUniversalLink:userActivity];
    }
    else {
        BOOL res = [OpenInstallSDK continueUserActivity:userActivity];
        if (res) {
            self.userActivity = userActivity;
            [self addGuestModeDealUrlNotification];
        }
        return res;
    }
}

- (BOOL)dealUniversalLink:(NSUserActivity *)userActivity
{
    if (!userActivity) {
        // userActivity为nil，直接返回
        return YES;
    }
    return [OpenInstallSDK continueUserActivity:userActivity];
}

- (void)addGuestModeDealUrlNotification
{
    static dispatch_once_t onceT;
    dispatch_once(&onceT, ^{
      [[NSNotificationCenter defaultCenter]
          addObserver:self
             selector:@selector(guestModeDealUrlMethod)
                 name:UPNormalModeHandleUrlNotification
               object:nil];
    });
}

- (void)guestModeDealUrlMethod
{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)),
                   dispatch_get_main_queue(), ^{
                     [self dealUniversalLink:self.userActivity];
                   });
}

//刷新Token成功 oauthdata
- (void)onRefreshTokenSuccess:(ApplicationOauthData *)oauthData
{
    UPLogInfo(@"MainLibModule", @"用"
                                @"户刷新token成功，开始处理邀请码逻辑");
    NSMutableDictionary *dic =
        [[NSUserDefaults standardUserDefaults] objectForKey:@"inviteCodeV2Dic"];
    if (dic) {
        [InviteCodeViewModel_special fillInviteCode:dic];
    }
}

@end
