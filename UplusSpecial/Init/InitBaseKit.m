//
//  InitBaseKit.m
//  InitBaseKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/10/16.
//

#import "InitBaseKit.h"
#import <UPCore/UPContext.h>
#import <UPCore/UPFunctionToggle.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPLog/UPLog.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import <AMapFoundationKit/AMapFoundationKit.h>
#import <AMapSearchKit/AMapSearchKit.h>
#import "PushMessageHandler.h"
#import <upuserdomain/UserDataSource.h>
#import <upuserdomain/DeviceListDataSource.h>
#import <upuserdomain/FamilyDataSource.h>
#import "ResourceTrackerIMP.h"
#import <UpTrace/UPEventTrace.h>
#import "UPEventTraceConfig.h"
#import <UPStorage/UPStorage.h>
#import <UpTrace/UpTrace.h>
#import <UPHttpDNS/UPHttpDNS.h>
#import "UPUpgradeConfig.h"
#import <UPUpgrade/UPUpgrade.h>
#import <UplusKit/UPUPMConfig.h>
#import <UpCrash/UPCrashReportManager.h>
#import <UpCrash/UPCrashReportConfig.h>
#import <UPDeviceInitKit/UPDeviceInitKit.h>
#import <UPDevice/UpDeviceInjection.h>
#import <UPDevice/UpDeviceManager.h>
#import "WashProgramStartIntercepter.h"

#define DefaultLocationTimeout 3
#define DefaultReGeocodeTimeout 3

/// flutter 首页显示通知
static NSString *const FlutterHomePageNotificationName =
    @"flutter_package_maintab_switch";
// 是否为游客模式
NSString *const kEnablePrivacyAgreement = @"isGuestMode";

typedef void (^UpLocatingCompletionBlock)(AMapLocationReGeocode *location,
                                          NSError *error);

@interface InitBaseKit () <UpWorkflowTask, AMapLocationManagerDelegate,
                           AMapSearchDelegate, UpUserDomainObserver,
                           UPPushHelperTrackDelegate>

@property (nonatomic) UpLaunchStage currentStage;

@property (nonatomic, strong) UplusKit *kit;
@property (nonatomic, strong) AMapLocationManager *locationManager;
@property (nonatomic, strong) AMapSearchAPI *search;
@property (nonatomic, copy) NSString *userCenterId;

@end

@implementation InitBaseKit

static InitBaseKit *baseKitInstance = nil;
+ (instancetype)initUPInitBaseKit:(UplusKitEnvironment)environment
                            appId:(NSString *)appId
                           appKey:(NSString *)appKey
                       appVersion:(NSString *)appVersion
                       isTestMode:(BOOL)isTestMode
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      baseKitInstance = [[InitBaseKit alloc] initUPInitBaseKit:environment
                                                         appId:appId
                                                        appKey:appKey
                                                    appVersion:appVersion
                                                    isTestMode:isTestMode];
    });

    return baseKitInstance;
}

- (instancetype)initUPInitBaseKit:(UplusKitEnvironment)environment
                            appId:(NSString *)appId
                           appKey:(NSString *)appKey
                       appVersion:(NSString *)appVersion
                       isTestMode:(BOOL)isTestMode
{
    if (self = [super init]) {
        _kit = [[UplusKit alloc] initUplusKitWithEnvironment:environment
                                                       appId:appId
                                                      appKey:appKey
                                                  appVersion:appVersion];
        [self initUPlusLog];
        [self initUPCrash];
        [self initNetwork];
        [self initUpTrace];
        [self initUPUserDomain];
        [self initUPResource];
        [self initUPUpgrade];
        [self initVDN];

        if (UPContext.sharedInstance.isAgreenPrivacyPolicy) {
            [UPDeviceInitKit
                initializeWithAppId:appId
                             appKey:appKey
                        environment:environment == UplusKitEnvironmentProd ? UPDeviceInitKitEnvironmentProd : UPDeviceInitKitEnvironmentTest];
            [[UpDeviceInjection getInstance].deviceManager addCommandIntercepter:[WashProgramStartIntercepter new]];
        }
    }
    return self;
}

#pragma mark - init method
- (void)initNetwork
{
    if ([UPFunctionToggle.shareInstance boolForKey:@"LaunchTime.optimizeV3"
                                      defaultValue:YES]) {
        [_kit initNetwork:nil];
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
          UPNetworkSettings.sharedSettings.httpDNSDelegate =
              [UPHttpDNS sharedHTTPDns];
        });
        return;
    }
    [_kit initNetwork:[UPHttpDNS sharedHTTPDns]];
}

- (void)initUPlusLog
{
    // 冷启动优化代码的开关；必须在调用[UPLogConfig sharedInstance]前设置好
    [UPLogConfig enableOptimizeLaunchTime:[self enableOptimizeLaunchTime]];

    if (![UPStorage getBooleanValue:kEnablePrivacyAgreement defaultValue:NO]) {
        [[UPLogConfig sharedInstance] enablePrivacyAgreement:YES];
    }
    NSNumber *logThreshold = [[UPFunctionToggle shareInstance]
        toggleValueForKey:@"Cache.upLogThreshold"];

    NSInteger logThresholdSize = 50;
    if ([logThreshold isKindOfClass:NSNumber.class]) {
        logThresholdSize = [logThreshold integerValue];
    }

    [UPLogConfig sharedInstance].maximumFilesSize =
        logThresholdSize * 1024 * 1024;
    [_kit initUPLogWithPlatform:UPLogPlatformChina maxLogsPerSecond:2400];
}

- (void)initUPUpgrade
{
    [self initUPUpgradeWithPlatform:UPUpgradeAppPlatformChina];
}

- (void)initUPCrash
{
    NSDictionary *crashConfig = [UPFunctionToggle.shareInstance toggleMapOfFunction:@"UpCrash"];
    UPLogInfo(@"[UpCrash]", @"[CrashPlatform]| toggles config: %@", crashConfig);

    NSString *reportPlatform = crashConfig[@"ReportPlatformIOS"] ?: @"Uplus";
    [UPStorage putMemoryString:@"CrashReportPlatform" value:reportPlatform];
    if (![reportPlatform isEqualToString:@"Uplus"]) {
        return;
    }

    UPLogInfo(@"[UpCrash]", @"[CrashPlatform]| init KSCrash");
    if (![UPStorage getBooleanValue:kEnablePrivacyAgreement defaultValue:NO]) {
        [[UPCrashReportConfig sharedInstance] enablePrivacyAgreement:YES];
    }
    [self initUpCrashWithPlatform:UPCrashUploadRegion_ChinaLand];
}

- (void)initUPUserDomain
{
    id<UpUserDataSource> userDataSource = [UserDataSource new];
    id<UpDeviceListDataSource> deviceDataSource = [DeviceListDataSource new];
    id<UpFamilyDataSource> familyDataSource = [FamilyDataSource new];
    [_kit initUPUserDomainWithPlatform:UPUserDomainPlatformHomeland
                        userDataSource:userDataSource
                      familyDataSource:familyDataSource
                      deviceDataSource:deviceDataSource];
}

- (void)initUPResource
{
    if (![UPStorage getBooleanValue:kEnablePrivacyAgreement defaultValue:NO]) {
        [UPResourceConfig shareInstance].enablePrivacyAgreement = YES;
    }
    [_kit initUPResourceWithPlatform:UPResourceAppPlatformChina
                             tracker:[[ResourceTrackerIMP alloc] init]];
}

- (void)initVDN
{
    [_kit initVDN];
}

- (void)initUpTrace
{
    // 冷启动优化代码的开关
    [UpTraceSettings shareInstance].enableOptimizeLaunchTime =
        [self enableOptimizeLaunchTime];

    if (![UPStorage getBooleanValue:kEnablePrivacyAgreement defaultValue:NO]) {
        [UpTraceSettings shareInstance].enablePrivacyAgreement = YES;
    }
    [self initTrace];
}

- (void)initUPEventTrace
{
    [[UPEventTrace getInstance] disableDataCollect];
    [[UPEventTrace getInstance] setDeviceIDModeToCustomBlock:^NSString *_Nonnull {
      return [UPNetworkSettings sharedSettings].clientID;
    }];
    [[UPEventTrace getInstance] initializeTrace];
    UPEventTraceConfig *config = [[UPContext sharedInstance]
        configModelOfFeature:@"EventTrace"
                  modelClass:UPEventTraceConfig.class];
    NSString *gioAccountId = @"";
    if (config) {
        gioAccountId = config.appKeyProd;
        if (UPEnvironmentProd != UPContext.sharedInstance.env) {
            gioAccountId = config.appKeyAcceptance;
        }
    }
    if (gioAccountId.length == 0) {
        return;
    }
    [[UPEventTrace getInstance] startWithAccountId:gioAccountId];
}

- (void)initUPUpgradeWithPlatform:(UPUpgradeAppPlatform)platform
{
    // 初始化版本升级组件
    UPUpgradeEnv upgradeEnv = UPUpgradeEnvProduction;
    if ([UPContext sharedInstance].env == UplusKitEnvironmentAcceptance) {
        upgradeEnv = UPUpgradeEnvTest;
    }
    UPUpgradeSettings *settings = [UPUpgradeSettings defaultSettings];
    UPUpgradeConfig *config =
        [[UPContext sharedInstance] configModelOfFeature:@"Upgrade"
                                              modelClass:UPUpgradeConfig.class];
    if (config) {
        settings.maxnumAlertCount =
            config.maxAlertCount >= 0 ? config.maxAlertCount : NSIntegerMax;
        settings.maxnumBetaAlertCount =
            config.maxBetaAlertCount >= 0 ? config.maxBetaAlertCount : NSIntegerMax;
        settings.maxnumBetaExpirationAlertCount =
            config.maxBetaExpirationAlertCount >= 0 ? config.maxBetaExpirationAlertCount : NSIntegerMax;
        settings.alertInterval = config.alertInterval;
        settings.beforeExpiration = config.alertIntervalBeforeBetaExpiration;
    }
    [UPUpgrade initializeWithAppId:[UPContext sharedInstance].appID
                          clientId:[UPNetworkSettings sharedSettings].clientID
                               env:upgradeEnv
                       appPlatform:platform
                          settings:settings];
    [UPUpgrade checkFullVersionUseUI:NO
                            callback:^(BOOL newVersionEnabled){

                            }];
}

- (void)initUpCrashWithPlatform:(UPCrashUploadRegionType)platform
{
    NSString *upmAppId = @"";
    UPUPMConfig *config =
        [[UPContext sharedInstance] configModelOfFeature:@"UPM"
                                              modelClass:UPUPMConfig.class];
    if (config) {
        upmAppId = config.upmAppId;
    }
    if (upmAppId.length == 0) {
        return;
    }
    UPCrashUploadEnvironmentType kUPCrashUploadEnvironmentType =
        UPCrashUploadEnvironment_SC;
    if (UPTestTypeTest != UPContext.sharedInstance.testType) {
        kUPCrashUploadEnvironmentType = UPCrashUploadEnvironment_YS;
    }
    [UPCrashReportManager initCrashReportManager:[UPContext sharedInstance].appID
                                        upmAppId:upmAppId
                                          region:platform
                                     environment:kUPCrashUploadEnvironmentType];
    [UPCrashReportManager uploadCrashReport];
    //设置ClientId
    [UPCrashReportManager
        setClientId:[UPNetworkSettings sharedSettings].clientID];
    [UPCrashReportManager setGrayMode:UPContext.sharedInstance.isGrayscaleMode];
}

- (void)initTrace
{
    UpTraceEnvironment environment = UPT_Product;
    if (UPTestTypeTest != UPContext.sharedInstance.testType) {
        environment = UPT_Acceptance;
    }
    NSString *upmAppId = @"";
    UPUPMConfig *config =
        [[UPContext sharedInstance] configModelOfFeature:@"UPM"
                                              modelClass:UPUPMConfig.class];
    if (config) {
        upmAppId = config.upmAppId;
    }
    [UpTraceInjection
        initializeTraceForEnvironment:environment
                               appKey:[UPContext sharedInstance].appKey
                                appId:[UPContext sharedInstance].appID
                             upmAppId:upmAppId];
}

#pragma mark - Config Resource Download Gif
- (UPResourceDownloadGifType (^)(void))resourceDownloadGif
{
    return ^UPResourceDownloadGifType {
      NSString *userId =
          UpUserDomainHolder.instance.userDomain.user.user_center_userId;
      if ([userId isKindOfClass:NSString.class] && userId.length > 0) {
          NSString *xiaoYouKey =
              [NSString stringWithFormat:@"%@_virtualman/switch", userId];
          NSString *showXiaoYouGif =
              [UPStorage getStringValue:xiaoYouKey
                           defaultValue:@"false"];
          return [showXiaoYouGif isEqualToString:@"true"] ? UPRXiaoYouGif : UPRHaierGif;
      }
      return UPRHaierGif;
    };
}

#pragma mark - UPModuleProtocol
- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    self.currentStage = stage;
    switch (stage) {
        case UpLaunchStageSystem:
        case UpLaunchStageBeforePrivacy:
        case UpLaunchStageAfterPrivacy:
            return self;
        default:
            return nil;
    }
}

- (void)run
{
    switch (self.currentStage) {
        case UpLaunchStageSystem:
            [self systemInit];
            break;
        case UpLaunchStageBeforePrivacy:
            [self initBeforePrivacy];
            break;
        case UpLaunchStageAfterPrivacy:
            [self initAfterPrivacy];
            break;
        default:
            break;
    }
}

- (void)systemInit
{
    UPContext *context = UPContext.sharedInstance;
    [InitBaseKit initUPInitBaseKit:(UplusKitEnvironment)context.env
                             appId:context.appID
                            appKey:context.appKey
                        appVersion:context.appVersion
                        isTestMode:context.isGrayscaleMode];
}

- (void)initBeforePrivacy
{
    //备注：UPInitBaseKit对象存在2个，这里self是UPCore 中创建的UPInitBaseKit
    [[UpUserDomainHolder instance].userDomain addObserver:baseKitInstance];
    [baseKitInstance.kit initUPPageTraceWithPlatform:UPPageTraceAppPlatformChina];
    [baseKitInstance.kit initUPPushWithTrackDelegate:self];
    [[PushMessageHandler sharedHandler] observeNotification];
    //用户同意隐私协议弹窗展示之后上报APP启动次数信息
    [UPCrashReportManager reportAPPStartUpTimesRecord];
}

//同意隐私协议后调用
- (void)initAfterPrivacy
{
    [baseKitInstance initUPEventTrace];
    [UPPageTraceInjection getInstance]
        .pageTraceManager.settings.enablePrivacyAgreement = YES;
    [[UPLogConfig sharedInstance] enablePrivacyAgreement:YES];
    [UPResourceConfig shareInstance].enablePrivacyAgreement = YES;
    [UpTraceSettings shareInstance].enablePrivacyAgreement = YES;
    [[UPCrashReportConfig sharedInstance] enablePrivacyAgreement:YES];
    /// 用户未同意隐私时会在同意隐私后再初始化UPDeviceInitKit
    UPContext *context = UPContext.sharedInstance;
    [UPDeviceInitKit initializeWithAppId:context.appID
                                  appKey:context.appKey
                             environment:context.env == UplusKitEnvironmentProd ? UPDeviceInitKitEnvironmentProd : UPDeviceInitKitEnvironmentTest];
    [[UPEventTrace getInstance] enableDataCollect];
    [[UPEventTrace getInstance] trace:@"app_start"];
    if ([UPFunctionToggle.shareInstance boolForKey:@"LaunchTime.optimizeV3"
                                      defaultValue:YES]) {
        dispatch_after(
            dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)),
            dispatch_get_main_queue(), ^{
              [baseKitInstance initMapLocation];
            });
    }
    else {
        [baseKitInstance initMapLocation];
    }
    [UpTraceInjection.instance.traceManager enableDataCollect];
}

#pragma mark - UpUserDomainObserver
- (void)onRefreshTokenSuccess:(id<UDAuthDataDelegate>)oauthData
{
    self.userCenterId = oauthData.uc_user_id;
    [[UPEventTrace getInstance] setUserId:self.userCenterId];
    [[UPEventTrace getInstance] trace:@"setUserId"
                         withVariable:@{
                             @"user_id" : oauthData.uc_user_id ?: @""
                         }];
    [self setUpgradeUserId:oauthData.uc_user_id];
    [UPCrashReportManager setUserId:self.userCenterId];
    [UpTraceInjection.instance.traceManager setUserId:self.userCenterId];
}

- (void)onLogOut:(id<UpUserDomainDelegate>)userDomain
{
    [self clearData];
}
- (void)onInvalidRefreshToken
{
    [self clearData];
}
- (void)onLogInElsewhere
{
    [self clearData];
}
- (void)onTokenMismatchDevice
{
    [self clearData];
}

- (void)clearData
{
    [self clearGrowingData];
    [self setUpgradeUserId:nil];
    [UPCrashReportManager setUserId:nil];
    [UpTraceInjection.instance.traceManager setUserId:@""];
}

- (void)clearGrowingData
{
    [[UPEventTrace getInstance] clearUserId];
    [[UPEventTrace getInstance]
               trace:@"clearUserId"
        withVariable:@{
            @"user_id" : self.userCenterId.length ? self.userCenterId : @"null"
        }];
}

- (void)setUpgradeUserId:(NSString *)userId
{
    [UPUpgrade setUserId:userId];
    [UPUpgrade checkFullVersionUseUI:NO
                            callback:^(BOOL newVersionEnabled){

                            }];
}

#pragma mark - UPPushHelperTrackDelegate
- (void)track:(NSString *)eventID param:(NSDictionary *)param
{
    // 使用埋点平台化后新增埋点库实现
    [[UPEventTrace getInstance] trace:eventID withVariable:param];
}

#pragma mark - privateMethod

- (void)initMapLocation
{
    if (![CLLocationManager locationServicesEnabled] ||
        [CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied ||
        [CLLocationManager authorizationStatus] ==
            kCLAuthorizationStatusNotDetermined)
        return;
    NSString *apiKey = [UPContext sharedInstance].aMapApiKey;
    if (!apiKey || 0 == apiKey.length) {
        return;
    }
    UPLogInfo(@"UplusSpecial", @"%s[%d] aMap apiKey:%@", __PRETTY_FUNCTION__,
              __LINE__, apiKey);
    [AMapServices sharedServices].apiKey = apiKey;
    self.search = [[AMapSearchAPI alloc] init];
    self.search.delegate = self;
    [self configManager];
    [self location:^(AMapLocationReGeocode *location, NSError *error) {
      if (error) {
          UPLogError(@"UplusSpecial", @"%s[%d] request location error %@",
                     __PRETTY_FUNCTION__, __LINE__, error);
          return;
      }
      AMapGeocodeSearchRequest *kAMapGeocodeSearchRequest =
          [[AMapGeocodeSearchRequest alloc] init];
      kAMapGeocodeSearchRequest.country = location.country;
      kAMapGeocodeSearchRequest.city = location.city;
      kAMapGeocodeSearchRequest.address = [self
          appendCityToProvice:location.province
                         city:location.city ?: location.district]; //@"北京市[]";
      [self searchCityLevelAdcodeInfo:kAMapGeocodeSearchRequest];
      UPLogInfo(@"UplusSpecial", @"%s[%d] search local code:%@ ,error %@",
                __PRETTY_FUNCTION__, __LINE__, kAMapGeocodeSearchRequest.address,
                error);
    }];
}

- (NSString *)appendCityToProvice:(NSString *)province city:(NSString *)city
{
    if (![province isKindOfClass:[NSString class]]) {
        return @"";
    }

    if (![city isKindOfClass:[NSString class]]) {
        return province;
    }

    return [province
        stringByAppendingString:[NSString stringWithFormat:@"%@", city]];
}

- (NSString *)appendFormatCityToProvice:(NSString *)province
                                   city:(NSString *)city
{
    if (![province isKindOfClass:[NSString class]]) {
        return @"";
    }

    if (![city isKindOfClass:[NSString class]]) {
        return province;
    }

    return [[province stringByAppendingString:@"-"]
        stringByAppendingString:[NSString stringWithFormat:@"%@", city]];
}

- (BOOL)enableOptimizeLaunchTime
{
    BOOL optimize = NO;
    NSNumber *optimizeNum = [[UPFunctionToggle shareInstance]
        toggleValueForKey:@"LaunchTime.optimize"];
    if ([optimizeNum isKindOfClass:[NSNumber class]]) {
        optimize = [optimizeNum boolValue];
    }
    return optimize;
}

#pragma mark - Map Location public method

- (AMapLocationManager *)locationManager
{
    if (!_locationManager) {
        _locationManager = ({
            AMapLocationManager *manager = [[AMapLocationManager alloc] init];
            manager.delegate = self;
            manager;
        });
    }
    return _locationManager;
}

- (void)configManager
{
    //设置期望定位精度
    [self.locationManager setDesiredAccuracy:kCLLocationAccuracyThreeKilometers];
    //设置定位超时时间
    [self.locationManager setLocationTimeout:DefaultLocationTimeout];
    //设置逆地理超时时间
    [self.locationManager setReGeocodeTimeout:DefaultReGeocodeTimeout];
}
- (void)dispose
{
    [self.locationManager stopUpdatingLocation];
}

- (void)location:(UpLocatingCompletionBlock)locationBlock
{
    BOOL kSuccessful = [self.locationManager
        requestLocationWithReGeocode:YES
                     completionBlock:^(CLLocation *location,
                                       AMapLocationReGeocode *regeocode,
                                       NSError *error) {
                       if (error != nil && locationBlock) {
                           locationBlock(nil, error);
                       }
                       else if (locationBlock) {
                           locationBlock(regeocode, nil);
                       }
                       [self dispose];
                     }];
    if (!kSuccessful) {
        UPLogInfo(@"UplusSpecial", @"%s[%d] request location from AMap:%d",
                  __PRETTY_FUNCTION__, __LINE__, kSuccessful);
    }
}

- (void)amapLocationManager:(AMapLocationManager *)manager
      doRequireLocationAuth:(CLLocationManager *)locationManager
{
    if ([CLLocationManager authorizationStatus] ==
        kCLAuthorizationStatusNotDetermined) {
        [locationManager requestAlwaysAuthorization];
    }
}

- (void)searchCityLevelAdcodeInfo:(AMapGeocodeSearchRequest *)request
{
    [self.search AMapGeocodeSearch:request];
}

- (void)onGeocodeSearchDone:(AMapGeocodeSearchRequest *)request
                   response:(AMapGeocodeSearchResponse *)response
{
    if (response.geocodes.count == 0) {
        return;
    }

    //解析response获取地理信息，具体解析见 Demo
    AMapGeocode *kAMapGeocode = response.geocodes[0];
    [UPResourceConfig shareInstance].localCode = kAMapGeocode.adcode;
    NSString *kCity = kAMapGeocode.city;
    if (nil == kCity ||
        ([kCity isKindOfClass:[NSString class]] && kCity.length == 0)) {
        kCity = kAMapGeocode.district;
    }
    NSString *kFormatAreaInfo =
        [self appendFormatCityToProvice:kAMapGeocode.province
                                   city:kCity];
    UPLogInfo(@"UplusSpecial", @"%s[%d] revice local code:%@ format area info %@",
              __PRETTY_FUNCTION__, __LINE__, kAMapGeocode.adcode,
              kFormatAreaInfo);
    //格式：陕西省-西安市
    [UPCrashReportManager setUserArea:kFormatAreaInfo];
    [UPCrashReportManager setUserAreaCode:kAMapGeocode.adcode];
}

@end
