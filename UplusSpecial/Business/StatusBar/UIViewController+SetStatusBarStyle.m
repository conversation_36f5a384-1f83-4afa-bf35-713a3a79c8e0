//
//  UIViewController+SetStatusBarStyle.m
//  mainbox
//
//  Created by 孙龙刚 on 2023/1/13.
//

#import "UIViewController+SetStatusBarStyle.h"
#import <objc/runtime.h>

@implementation UIViewController (SetStatusBarStyle)

+ (void)load
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      Method fromMethod = class_getInstanceMethod([self class], @selector(viewDidAppear:));
      Method toMethod = class_getInstanceMethod([self class], @selector(StatusBarSwizzingViewDidAppear:));
      if (!class_addMethod([self class], @selector(StatusBarSwizzingViewDidAppear:), method_getImplementation(toMethod), method_getTypeEncoding(toMethod))) {
          method_exchangeImplementations(fromMethod, toMethod);
      }
    });
}

- (void)StatusBarSwizzingViewDidAppear:(BOOL)animate
{
    [self StatusBarSwizzingViewDidAppear:animate];
    if (self.navigationController.viewControllers.count > 1) {
        if (@available(iOS 13.0, *)) {
            [UIApplication.sharedApplication setStatusBarStyle:UIStatusBarStyleDarkContent];
        }
        else {
            [UIApplication.sharedApplication setStatusBarStyle:UIStatusBarStyleDefault];
            // Fallback on earlier versions
        }
    }
}

@end
