//
//  UserCenterAddStatusPatcher.m
//  mainbox
//
//  Created by ha<PERSON> on 2021/7/8.
//

#import "UserCenterAddStatusPatcher.h"

@implementation UserCenterAddStatusPatcher

- (NSString *)name
{
    return NSStringFromClass(self.class);
}

- (NSInteger)priority
{
    return 1;
}

- (BOOL)patch:(id<Page>)page
{
    if (![page.uri.queryParameterMap.allKeys containsObject:@"underneathStatusBar"]) {
        [page.uri addQueryParameters:@{
            @"underneathStatusBar" : @"1"
        }];
        [page moveToStage:VdnPageStageVdns];
    }
    return YES;
}

- (BOOL)isNeedPatch:(id<Page>)page
{
    if ([page.uri.host isEqualToString:@"feedback"]) {
        return YES;
    }
    return NO;
}

- (void)removeTrigger:(id<Page>)page
{
}


@end
