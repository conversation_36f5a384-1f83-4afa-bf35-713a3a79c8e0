//
//  UIViewController+PageStay.m
//  UplusSpecial
//
//  Created by 路标 on 2024/9/4.
//

#import "UIViewController+PageStay.h"
#import <objc/runtime.h>
#import <UPVDN/UIViewController+Vdn.h>
#import <UpTrace/UPEventTrace.h>
#import <WebKit/WKWebView.h>
#import "AnalyticsModule.h"

static const void *const PAGE_STAY_BEGIN_DATE = "PAGE_STAY_BEGIN_DATE";

@implementation UIViewController (PageStay)
+ (void)load
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      SEL orgSel = @selector(viewDidAppear:);
      SEL swizSel = @selector(pageStayViewDidAppear:);
      [self swizzingSel:orgSel with:swizSel];

      orgSel = @selector(viewDidDisappear:);
      swizSel = @selector(pageStayViewDidDisappear:);
      [self swizzingSel:orgSel with:swizSel];
    });
}

+ (void)swizzingSel:(SEL)orgSel with:(SEL)swizSel
{
    Class cls = [self class];
    Method orgMethod = class_getInstanceMethod(cls, orgSel);
    Method swizMethod = class_getInstanceMethod(cls, swizSel);
    if (class_addMethod(cls, swizSel, method_getImplementation(swizMethod), method_getTypeEncoding(swizMethod))) {
        class_replaceMethod(cls, swizSel, method_getImplementation(orgMethod), method_getTypeEncoding(orgMethod));
    }
    else {
        method_exchangeImplementations(orgMethod, swizMethod);
    }
}

- (void)pageStayViewDidAppear:(BOOL)animated
{
    if ([self shouldIgnoreTrace]) {
        [self pageStayViewDidAppear:animated];
        return;
    }

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppEnterForeground:) name:AppWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppEnterBackground:) name:AppDidEnterBackgroundNotification object:nil];
    [self beginTrace];
    [self pageStayViewDidAppear:animated];
}

- (void)pageStayViewDidDisappear:(BOOL)animated
{
    if ([self shouldIgnoreTrace]) {
        [self pageStayViewDidDisappear:animated];
        return;
    }

    [[NSNotificationCenter defaultCenter] removeObserver:self name:AppWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AppDidEnterBackgroundNotification object:nil];

    [self endTrace];
    [self pageStayViewDidDisappear:animated];
}

- (void)onAppEnterForeground:(NSNotification *)notification
{
    [self beginTrace];
}

- (void)onAppEnterBackground:(NSNotification *)notification
{
    [self endTrace];
}

- (void)beginTrace
{
    NSDate *begin = [NSDate date];
    objc_setAssociatedObject(self, PAGE_STAY_BEGIN_DATE, begin, OBJC_ASSOCIATION_RETAIN);
}

- (void)endTrace
{
    NSDate *begin = objc_getAssociatedObject(self, PAGE_STAY_BEGIN_DATE);
    if (begin) {
        NSInteger duration = [[NSDate date] timeIntervalSinceDate:begin] * 1000;
        [self traceDuration:@(duration)];
    }
    objc_setAssociatedObject(self, PAGE_STAY_BEGIN_DATE, nil, OBJC_ASSOCIATION_ASSIGN);
}

- (BOOL)shouldIgnoreTrace
{
    return [self isKindOfClass:[UINavigationController class]] || [self isKindOfClass:[UIAlertController class]] || [self isKindOfClass:NSClassFromString(@"UIApplicationRotationFollowingController")];
}

- (void)traceDuration:(NSNumber *)duration
{
    [self getPageTitle:^(NSString *title) {
      NSDictionary *variables = @{
          @"static_url" : [self getPageStaticUrl],
          @"title" : title,
          @"page_view_time" : duration.stringValue,
          @"page_view_time_int" : duration
      };
      [[UPEventTrace getInstance] trace:@"MB37141" withVariable:variables];
    }];
}

- (NSString *)getPageStaticUrl
{
    NSString *url;
    if (self.originalURL.length) {
        url = self.originalURL;
    }
    else if (self.realURL.length) {
        url = self.realURL;
    }
    else {
        url = [NSString stringWithFormat:@"native://%@", NSStringFromClass([self class])];
    }

    NSURLComponents *components = [NSURLComponents componentsWithString:url];
    if (![url hasPrefix:@"file"]) {
        return [NSString stringWithFormat:@"%@://%@%@", components.scheme, components.host ?: @"", components.path ?: @""];
    }

    NSError *error;
    NSRegularExpression *regular = [NSRegularExpression regularExpressionWithPattern:@"/([^/]+)@" options:NSRegularExpressionCaseInsensitive error:&error];
    if (error) {
        return url;
    }

    NSRange range = [regular rangeOfFirstMatchInString:url options:0 range:NSMakeRange(0, url.length)];
    if (range.location == NSNotFound) {
        return url;
    }

    NSString *capturedGroup = [url substringWithRange:range];
    capturedGroup = [[capturedGroup stringByReplacingOccurrencesOfString:@"/" withString:@""] stringByReplacingOccurrencesOfString:@"@" withString:@""];

    return [NSString stringWithFormat:@"mpaas://%@", capturedGroup];
}

- (void)getPageTitle:(void (^)(NSString *title))completionHandler
{
    // H5容器会监听document.title并设置给viewController.title
    if (self.title.length) {
        completionHandler(self.title);
        return;
    }

    WKWebView *webView = [self findWebView:self.view];
    if (!webView) {
        // 没有title的Native和Flutter容器
        completionHandler(@"");
        return;
    }

    // 兜底再取一次H5页面title
    if (webView.title.length) {
        completionHandler(webView.title);
        return;
    }
    [webView evaluateJavaScript:@"document.title"
              completionHandler:^(id result, NSError *error) {
                completionHandler(result ?: @"");
              }];
}

- (WKWebView *)findWebView:(UIView *)view
{
    if ([view isKindOfClass:[WKWebView class]]) {
        return (WKWebView *)view;
    }

    for (UIView *subView in view.subviews) {
        WKWebView *web = [self findWebView:subView];
        if (web) {
            return web;
        }
    }
    return nil;
}

@end
