//
//  AnalyticsModule.m
//  Uplus
//
//  Created by <PERSON> on 13/05/2017.
//  Copyright © 2017 北京海尔广科数字技术有限公司. All rights reserved.
//

#import "AnalyticsModule.h"
#import <UPLog/UPLog.h>
#import <UPTools/AnalyticsManager.h>
#import <UPCore/UPContext.h>
#import <UPTools/UserEventTool.h>
#import <uAnalytics/uAnalytics.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPStorage/UPStorage.h>

#define ENTER_UPLUS @"1001000000" // 进入U+APP
#define HOME_OUT @"1000999998" // 智能手机home键退出

const NSNotificationName AppWillEnterForegroundNotification = @"AnalyticsModuleAppWillEnterForeground";
const NSNotificationName AppDidEnterBackgroundNotification = @"AnalyticsModuleAppDidEnterBackground";

@interface AnalyticsModule () <UpWorkflowTask, UpUserDomainObserver>

@property (nonatomic) UpLaunchStage currentStage;

@end

@implementation AnalyticsModule

- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    self.currentStage = stage;
    switch (stage) {
        case UpLaunchStageBeforePrivacy:
        case UpLaunchStageAfterPrivacy:
            return self;
        default:
            return nil;
    }
}

- (void)run
{
    switch (self.currentStage) {
        case UpLaunchStageBeforePrivacy:
            [self initBeforePrivacy];
            break;
        case UpLaunchStageAfterPrivacy:
            [self initAfterPrivacy];
        default:
            break;
    }
}

#pragma mark - UPModuleProtocol
- (void)initBeforePrivacy
{
    //初始化社会化统计，hook页面打点事件
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      [[UpUserDomainHolder instance].userDomain addObserver:self];
      UPLogDebug(@"AnalyticsModule", @"%s[%d]AnalyticsManager初始化完毕。",
                 __PRETTY_FUNCTION__, __LINE__);
    });
}

- (void)initAfterPrivacy
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      [AnalyticsManager setupInit];
      [UserEventTool userClickEvent:ENTER_UPLUS];
    });
}

- (void)onAppBackground
{
    [[NSNotificationCenter defaultCenter]
        postNotificationName:AppDidEnterBackgroundNotification
                      object:nil];
    BOOL isGuest = [UPStorage getBooleanValue:UPIsGuestMode defaultValue:NO];
    if (!isGuest) {
        [UserEventTool userClickEvent:HOME_OUT];
    }
}

- (void)onAppForeground
{
    [[NSNotificationCenter defaultCenter]
        postNotificationName:AppWillEnterForegroundNotification
                      object:nil];
}

- (void)onRefreshTokenSuccess:(ApplicationOauthData *)oauthData
{
    [uAnalytics setUserId:oauthData.uhome_user_id];
    [uAnalytics event:@"t_user_start"
           attributes:[NSDictionary
                          dictionaryWithObjectsAndKeys:oauthData.uhome_user_id,
                                                       @"userId", nil]];
}

@end
