//
//  InviteCodeService_special.h
//  mainbox
//
//  Created by haier on 2020/1/14.
//

#import <Foundation/Foundation.h>
#import "HTTPRequestManager_special.h"
//#import "LGBase.h"
#import <UPTools/HDError.h>

NS_ASSUME_NONNULL_BEGIN

@interface InviteCodeService_special : NSObject

/**
 一键填写邀请码
 code 邀请码
 */
+ (void)inviteCodeFilled:(NSMutableDictionary *)body
             withSuccess:(void (^)(NSDictionary *info))success
                 failure:(void (^)(HDError *error, NSDictionary *info))failure;

/**
 登录情况下，判断邀请码是否有效
 */
+ (void)judgeUsedInviteCodeWithSuccess:(void (^)(NSDictionary *info))success
                               failure:(void (^)(HDError *error, NSDictionary *info))failure;


/**
 填写活动码或者渠道码
 */
+ (void)requestActivityChannelCode:(NSDictionary *)dic
                       WithSuccess:(void (^)(NSDictionary *info))success
                           failure:(void (^)(HDError *error, NSDictionary *info))failure;
/**
 活动码、邀请码、渠道码填写接口
 */
+ (void)requestNewAllCode:(NSMutableDictionary *)body WithSuccess:(void (^)(NSDictionary *info))success
                  failure:(void (^)(HDError *error, NSDictionary *info))failure;

@end

NS_ASSUME_NONNULL_END
