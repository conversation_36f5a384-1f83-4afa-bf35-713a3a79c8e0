//
//  InviteCodeViewModel_special.m
//  mainbox
//
//  Created by ha<PERSON> on 2020/8/17.
//  Copyright © 2020 路鹏. All rights reserved.
//

#import "InviteCodeViewModel_special.h"
#import <UPLog/UPLog.h>
#import <UPVDN/Page.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import "InviteCodeService_special.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPTools/KVNProgressShow.h>

@interface InviteCodeViewModel_special ()

@end

@implementation InviteCodeViewModel_special

+ (void)addInviteCodeDic:(NSDictionary *)dic
{
    NSString *inviteCode = dic[@"uplusAppInviteCode"];
    NSString *activeCode = dic[@"ua"];
    NSString *channelCode = dic[@"uc"];
    //    NSString *codeType = dic[@"inviteCodeType"];
    if (dic.count == 0) {
        UPLogInfo(@"MainBox", @"邀请码字典为空==%@", dic);
        return;
    }

    [self gotoTargetUrl:dic];
    if (inviteCode.length == 0 && activeCode.length == 0 &&
        channelCode.length == 0) {
        return;
    }
    if (UpUserDomainHolder.instance.userDomain.state ==
        UpUserDomainStateDidLogin) {
        [self fillInviteCode:dic];
    }
    else {
        [[NSUserDefaults standardUserDefaults] setObject:dic
                                                  forKey:@"inviteCodeV2Dic"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}

+ (void)gotoTargetUrl:(NSDictionary *)dic
{
    NSString *url = dic[@"jumpUrl"];
    NSString *startString = dic[@"jumpStart"];
    NSString *endString = dic[@"jumpEnd"];
    if (url.length == 0 && startString.length == 0 && endString.length == 0) {
        return;
    }
    url = [[url stringByRemovingPercentEncoding] stringByRemovingPercentEncoding];
    NSTimeInterval start = [startString doubleValue];
    NSTimeInterval end = [endString doubleValue];
    NSTimeInterval current = [NSDate.date timeIntervalSince1970] * 1000;
    if (current >= start && current <= end) {
        [UpVdn goToPage:url
                   flag:VdnPageFlagPush
             parameters:nil
               complete:nil
                  error:nil];
    }
}

+ (void)fillInviteCode:(NSDictionary *)dic
{
    //获取外部字段
    NSString *inviteCode = dic[@"uplusAppInviteCode"];
    NSString *activeCode = dic[@"ua"];
    NSString *channelCode = dic[@"uc"];
    NSString *codeType = dic[@"inviteCodeType"];

    //接口请求字段
    NSMutableDictionary *body = NSMutableDictionary.dictionary;
    body[@"inviteCode"] = inviteCode;
    body[@"codeType"] = codeType;
    body[@"activeCode"] = activeCode;
    body[@"channelCode"] = channelCode;
    [InviteCodeService_special requestNewAllCode:body
        WithSuccess:^(NSDictionary *_Nonnull info) {
          if (![info isKindOfClass:NSDictionary.class]) {
              [KVNProgressShow showText:@"返回数据为空"];
              return;
          }
          if ([info[@"retCode"] isEqualToString:@"00000"]) {
              [KVNProgressShow showText:@"邀请成功"];
          }
          else {
              [self showErrorInfo:info];
          }
          [[NSUserDefaults standardUserDefaults]
              removeObjectForKey:@"inviteCodeV2Dic"];
          [[NSUserDefaults standardUserDefaults] synchronize];
        }
        failure:^(HDError *_Nonnull error, NSDictionary *_Nonnull info) {
          [[NSUserDefaults standardUserDefaults] setObject:dic
                                                    forKey:@"inviteCodeV2Dic"];
          [[NSUserDefaults standardUserDefaults] synchronize];
          [KVNProgressShow showError:@"网"
                                     @"络连接不可用，请稍后重试或检查网络设置"];
        }];
}

+ (void)showErrorInfo:(NSDictionary *)info
{
    NSString *errorCode = info[@"info"];
    if ([errorCode isEqualToString:@"65101"]) {
        [KVNProgressShow showText:@"此设备关联账号超过上限！"];
    }
    else if ([errorCode isEqualToString:@"65201"]) {
        [KVNProgressShow showText:@"邀"
                                  @"请码存在于黑名单中，请联系客户进行处理"];
    }
    else if ([errorCode isEqualToString:@"65104"]) {
        [KVNProgressShow showText:@"不可以填写自己的邀请码!"];
    }
    else if ([errorCode isEqualToString:@"65106"]) {
        [KVNProgressShow showText:@"您已经注册过海尔智家"];
    }
    else {
        [KVNProgressShow showText:info[@"retInfo"]];
    }
}

@end
