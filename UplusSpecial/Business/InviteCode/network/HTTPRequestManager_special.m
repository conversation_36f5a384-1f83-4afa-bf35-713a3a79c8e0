//
//  HTTPRequestManager_special.m
//  Uplus
//
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights reserved.
//

#import "HTTPRequestManager_special.h"
//#import "UPTimeStatisticsManager.h"
#import "UPMutableRequest_special.h"

@implementation HTTPRequestManager_special
+ (void)postHTTPSRequestWithUrl:(NSString *)url
                        headers:(NSDictionary *)header
                           body:(id)body
                    OnOperation:(void (^)(AFHTTPRequestOperation_special *onOperation))onOperation
                     completion:(void (^)(NSDictionary *responseDic))completion
                          error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    void (^success)(NSDictionary *responseDic) = completion;
    void (^failure)(NSError *connectError, long responseStatusCode) = onError;
    NSURL *tmp = [NSURL URLWithString:url];
    NSString *baseUrl = tmp.port ? [NSString stringWithFormat:@"%@://%@:%@", tmp.scheme, tmp.host, tmp.port] : [NSString stringWithFormat:@"%@://%@", tmp.scheme, tmp.host];
    NSString *path = [tmp.absoluteString stringByReplacingOccurrencesOfString:baseUrl withString:@""];
    UPMutableRequest_special *request = [UPMutableRequest_special requestWithBaseUrl:baseUrl path:path method:UPRequestMethodPOST header:header body:body];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *payload = nil;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          payload = (NSDictionary *)responseObject;
      }
      success(payload);
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          failure(error, error.code);
        }];
}

+ (void)postRequestWithUrl:(NSString *)url
                   headers:(NSDictionary *)header
                      body:(id)body
               OnOperation:(void (^)(AFHTTPRequestOperation_special *onOperation))onOperation
                completion:(void (^)(NSDictionary *responseDic))completion
                     error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    [self postHTTPSRequestWithUrl:url headers:header body:body OnOperation:onOperation completion:completion error:onError];
}

+ (void)getHTTPSRequestWithUrl:(NSString *)url
                       headers:(NSDictionary *)header
                    parameters:(id)parameters
                   OnOperation:(void (^)(AFHTTPRequestOperation_special *onOperation))onOperation
                    completion:(void (^)(NSDictionary *responseDic))completion
                         error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    void (^success)(NSDictionary *responseDic) = completion;
    void (^failure)(NSError *connectError, long responseStatusCode) = onError;


    NSURL *tmp = [NSURL URLWithString:url];

    NSString *baseUrl = tmp.port ? [NSString stringWithFormat:@"%@://%@:%@", tmp.scheme, tmp.host, tmp.port] : [NSString stringWithFormat:@"%@://%@", tmp.scheme, tmp.host];
    NSString *path = [tmp.absoluteString stringByReplacingOccurrencesOfString:baseUrl withString:@""];
    UPMutableRequest_special *request = [UPMutableRequest_special requestWithBaseUrl:baseUrl path:path method:UPRequestMethodGET header:header body:parameters];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *payload = nil;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          payload = (NSDictionary *)responseObject;
      }
      success(payload);
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          failure(error, error.code);
        }];
}

+ (void)getRequestWithUrl:(NSString *)url
                  headers:(NSDictionary *)header
               parameters:(id)parameters
              OnOperation:(void (^)(AFHTTPRequestOperation_special *onOperation))onOperation
               completion:(void (^)(NSDictionary *responseDic))completion
                    error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    [self getHTTPSRequestWithUrl:url headers:header parameters:parameters OnOperation:onOperation completion:completion error:onError];
}

+ (void)deleteHTTPSRequestWithUrl:(NSString *)url
                          headers:(NSDictionary *)header
                       parameters:(NSDictionary *)parameters
                      OnOperation:(void (^)(AFHTTPRequestOperation_special *onOperation))onOperation
                       completion:(void (^)(NSDictionary *))completion
                            error:(void (^)(NSError *, long))onError
{
    void (^success)(NSDictionary *responseDic) = completion;
    void (^failure)(NSError *connectError, long responseStatusCode) = onError;
    NSURL *tmp = [NSURL URLWithString:url];
    NSString *baseUrl = tmp.port ? [NSString stringWithFormat:@"%@://%@:%@", tmp.scheme, tmp.host, tmp.port] : [NSString stringWithFormat:@"%@://%@", tmp.scheme, tmp.host];
    NSString *path = [tmp.absoluteString stringByReplacingOccurrencesOfString:baseUrl withString:@""];
    UPMutableRequest_special *request = [UPMutableRequest_special requestWithBaseUrl:baseUrl path:path method:UPRequestMethodDELETE header:header body:parameters];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *payload = nil;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          payload = (NSDictionary *)responseObject;
      }
      success(payload);
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          failure(error, error.code);
        }];
}

@end
