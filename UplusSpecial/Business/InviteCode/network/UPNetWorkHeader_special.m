//
//  UplusGlobal.m
//  Uplus
//
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights reserved.
//
#import "UPNetWorkHeader_special.h"
#import <upnetwork/UPNetworkSettings.h>
#import <UPTools/EncryptUserDefault.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <upnetwork/UPNetwork.h>

@implementation UPNetWorkHeader_special
NSString *const UNAppId_special = @"appId";
NSString *const UNAppVersion_special = @"appVersion";
NSString *const UNAppKey_special = @"appKey";
NSString *const UNClientId_special = @"clientId";
NSString *const UNSequenceId_special = @"sequenceId";
NSString *const UNAccessToken_special = @"accessToken";
NSString *const UNSign_special = @"sign";
NSString *const kUNUserID_special = @"kUNUserID";

+ (UPNetWorkHeader_special *)defaultHeader
{
    static UPNetWorkHeader_special *sharedInstance = nil;
    static dispatch_once_t oncePredicate;

    dispatch_once(&oncePredicate, ^{
      sharedInstance = [[UPNetWorkHeader_special alloc] init];
    });

    return sharedInstance;
}

- (id)init
{
    self = [super init];
    if (self) {
        self.clientId = [UPNetworkSettings sharedSettings].clientID;
        //        self.accessToken = [EncryptUserDefault valueForKey:UNAccessToken_special];
        _userID = [EncryptUserDefault valueForKey:kUNUserID_special];
    }

    return self;
}

- (NSString *)accessToken
{
    if (![UpUserDomainHolder instance].userDomain.oauthData.uhome_access_token) {
        return @"";
    }
    return [UpUserDomainHolder instance].userDomain.oauthData.uhome_access_token;
}


- (void)setUserID:(NSString *)userID
{
    NSString *userIDStorage = [userID isKindOfClass:[NSString class]] ? userID : @"";
    if ([_userID isEqualToString:userIDStorage]) {
        return;
    }
    _userID = userIDStorage;
    [EncryptUserDefault setObject:_userID forKey:kUNUserID_special];
}

- (NSString *)sequenceId
{
    NSDate *nowDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyyMMddHHmmssSSS";
    NSString *dateString = [dateFormatter stringFromDate:nowDate];
    NSString *sequenceidStr = [NSString stringWithFormat:@"%@000001", dateString];
    return sequenceidStr;
}

+ (NSString *)languageCode
{
    NSString *languageCode = [[NSUserDefaults standardUserDefaults] objectForKey:@"langeuageset"];
    if (languageCode) {
        return languageCode;
    }

    return @"en";
}

+ (NSDictionary *)getOpenApiHttpHeader:(NSDictionary *)bodyDictionary
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *sign = [UPCommonServerHeader signResultWithBody:bodyDictionary timestamp:header.timestamp].sign;

    return @{ UNAppId_special : header.appId,
              UNSequenceId_special : header.sequenceId,
              UNClientId_special : header.clientId,
              UNAppKey_special : header.appKey,
              UNAccessToken_special : header.accessToken,
              UNAppVersion_special : header.appVersion,
              UNSign_special : sign,
              @"timestamp" : header.timestamp,
              @"language" : [self languageCode],
              @"timezone" : @"1" };
}

#pragma mark 新版需要验证的app server header
+ (NSDictionary *)getInternationalAPPserverHttpHeader:(NSString *)url bodyDictionary:(NSDictionary *)bodyDictionary
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *sign = [UPCommonServerHeader signResultWithBody:bodyDictionary UrlString:url timestamp:header.timestamp].sign;

    return @{ UNAppId_special : header.appId,
              @"timestamp" : header.timestamp,
              UNClientId_special : header.clientId,
              UNAppKey_special : header.appKey,
              UNAccessToken_special : header.accessToken,
              UNAppVersion_special : header.appVersion,
              UNSign_special : sign,
              @"Accept-Language" : [self languageCode]
    };
}

+ (NSDictionary *)getUWSAppServerHttpHeaderWithUrl:(NSString *)url bodyDictionary:(NSDictionary *)bodyDictionary
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *appId = header.appId;
    NSString *sequenceId = header.sequenceId;
    NSString *clientId = header.clientId;
    NSString *accessToken = header.accessToken;
    NSString *appversion = header.appVersion;
    NSString *language = @"zh-cn";
    NSString *timeZone = @"8";
    NSString *timeStamp = header.timestamp;
    NSString *sign = [UPCommonServerHeader signResultWithBody:bodyDictionary UrlString:url timestamp:timeStamp].sign;

    return @{ UNAppId_special : appId,
              UNClientId_special : clientId,
              UNAppVersion_special : appversion,
              UNAccessToken_special : accessToken,
              @"timestamp" : timeStamp,
              @"timezone" : timeZone,
              @"language" : language,
              UNSequenceId_special : sequenceId,
              UNSign_special : sign };
}

+ (NSDictionary *)getAPPserverHttpHeader
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *appId = header.appId;
    NSString *sequenceId = header.sequenceId;
    NSString *clientId = header.clientId;
    NSString *appKey = header.appKey;
    NSString *accessToken = header.accessToken;
    NSString *appversion = header.appVersion;

    return @{UNAppId_special : appId,
             UNSequenceId_special : sequenceId,
             UNClientId_special : clientId,
             UNAppKey_special : appKey,
             UNAccessToken_special : accessToken,
             UNAppVersion_special : appversion};
}

+ (NSDictionary *)getNewAPPserverHttpHeader:(NSDictionary *)bodyDictionary
{
    return [UPCommonServerHeader signHeaderWithBody:bodyDictionary];
    ;
}

@end
