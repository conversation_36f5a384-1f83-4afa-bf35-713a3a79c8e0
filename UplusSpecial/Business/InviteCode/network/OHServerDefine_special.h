//
//  OHServerDefine.h
//  Uplus
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/5/27.
//  Copyright © 2017年 北京海尔广科数字技术有限公司. All rights reserved.
//

#ifndef OHServerDefine_h
#define OHServerDefine_h

#define OH_APPSERVER_HOST @"https://zj.haier.net/emuplus/" // 生产环境带后缀

#define OH_APPSERVER_IM_HOST @"http://uhome.haier.net:7580/UplusIm"
#define OH_OPENAPISERVER_HOST @"http://uhome.haier.net:7340" // OpenApi
#define OH_ALL_HOME_HOST @"https://zj.haier.net" //全屋数据zj.haier.net/emuplus
#define OH_SERVICE_SWITCHS @"secuag/getSwitchStatus" // 服务开关状态
#define OH_SERVICE_RECOMMEND @"secuag/service/getRecommends" //个性推荐列表
#define OH_GET_ELITE_GROUP @"/secuag/elite/home/<USER>" //获取首页群组精华 for 2.3.0
#define OH_IDFA_COLLECTION @"/omsappapi/idfa/v1/collect" //idfa 收集 for 6.8.0
#define OH_AccordRuleSendHaiBei @"/omsappapi/rule/v1/accordRuleSendHaiBei" //根据规则加海贝

//add by wangmiao for V2.1.0 开发：增加众创汇，商城，EMC,推荐;
#define ZhongchChannel 0 //webview跳转渠道：众创汇
#define HaierShopChannel 1 //webview跳转渠道：海尔商城
#define UhomeShopChannel 2 //webview跳转渠道：优家商城
#define StrollShopChannel 3 //webview跳转渠道：顺逛微店
#define HaiShellShopChannel 4 //webview跳转渠道：海贝商城
#define OtherChannel 5 //webview跳转渠道：其他（传所有参数）
#define ApplicationRecommendChannel 6 //webview跳转渠道：应用推荐
#define EMCViewControllChannel 7 //webview跳转渠道：EMC
#define ChunyuChannel 8 //webview跳转渠道：春雨医生
#define HealthyChannel 9 //webview跳转渠道：健康中心
#define EcommerceChannel 10 //webview跳转渠道：洗护中心
#define MenuChannel 11 //webview跳转渠道：菜谱中心
#define DirectChannel 12 //webview跳转渠道：无需传参
#define UnKnownChannel 13 //webview跳转渠道：未知频道

#define haierUrlLink @"http://user.haier.com/ids/userLoginByAPP.jsp?"

#define Circle_Module @"Circle_Module"
#define GoodRecommend_Module @"GoodRecommend_Module"
#define PictureSrcoll_Module @"PictureSrcoll_Module"
#define More_Module @"More_Module"


#define Recommend_Module @"Recommend_Module"
#define Order_Module @"OrderApply_Module"
#define Apply_Module @"OrderApply_Module"

#define OH_LOGIN_SUCCESS @"LOGIN_SUCCESS" // 登录成功
#define OH_RELOAD_DEVICE_LIST @"RELOAD_DEVICE_LIST"
#define OH_LOGOUT_RELOAD_SERVICE_INFO @"LOGOUT_RELOAD_SERVICE_INFO"
#define OH_ONDidUnBindAliasSuccess @"onDidUnBindAliasSuccess"


#define OH_BackGroundColor [UIColor colorWithRed:238 / 255.0f green:238 / 255.0f blue:238 / 255.0f alpha:1.0]

static NSString *const OH_AppserverRetcode = @"retCode";
static NSString *const OH_AppserverRetinfo = @"retInfo";
static NSString *const OH_AppserverUserID = @"userId";
static NSString *const OH_AppserverTransactionId = @"transactionId";
static NSString *const OH_AppserverSuccess = @"00000";

static NSString *const OH_OpenApiRetcode = @"retCode";
static NSString *const OH_OpenApiRetinfo = @"retInfo";
static NSString *const OH_OpenApiUserID = @"userId";
static NSString *const OH_OpenApiTransactionId = @"transactionId";
static NSString *const OH_OpenApiSuccess = @"00000";

//首页-综合推荐-接口
//#if ENV_YS
//#define OH_OMS_HOST @"http://*************:80/"
#define OH_OMS_HOST @"https://uhome.haier.net:443/"
#define OH_OMS_RECOMMEND_LIST @"omsappapi/recommend/v5/list"
#define OH_OMS_AD_START_PAGES @"omsappapi/ad/startpage"
#define OH_OMS_AD_V1_START_PAGES @"omsappapi/ad/v1/start/advertising"
#define OH_OMS_AD_POPUP @"omsappapi/ad/popup"
#define OH_OMS_AD_BIND @"omsappapi/ad/bind" //tabbar “+”标签页中问候语 接口
#define OH_ACCOUNT_API @"https://account-api.haier.net"

#define OH_IS_FIRST_LOGIN @"/haier/v2/user/login/status" //根据登录token获取用户是否第一次登录智家APP

#define OH_SERVER_SecurityMessages @"device/v1/security/dw/messages" //安防类产品日志

#define OH_SERVER_FamilyFeviceScore @"family/v1/device/score" //家庭设备积分


#define OH_OMS_APPVERSION @"emuplus/secuag/common/appversion"

#define OH_FAMILY_UPDATE_LOCATION @"family/v1/family/edit"

#define OH_HOUSE_AIR_DATA @"/wisdomapi/house/v1/air/detail" //查询家庭房间空气四度数据

#define OH_ALLHOME_ALARM @"/wisdomapi/house/v1/consumables/family/aggregation" //获取家庭设备聚合耗材信息

#define OH_ALLHOME_WEATHER @"/rcs/weather/current-forecast" //当前及未来天气信息

#define OH_OMS_AD_HOST @"https://zj.haier.net"

#define OH_ONE_KEY_LOGIN @"/api-gw/oauthserver/account/v1/onekey/login" //一键登录

#define OH_NEW_CLIENT_AD_LIST @"/omsappapi/ad/v1/rotation" //新手广告接口

#define OH_CHANGED_LONG_LINK @"/omsappapi/omsva/secuag/getLongLink" //短链换长链

#define OH_IDFA_ASA @"/api-gw/zjBaseServer/app/download/IOS/ASA" //ASA数据接口

#endif /* OHServerDefine_h */
