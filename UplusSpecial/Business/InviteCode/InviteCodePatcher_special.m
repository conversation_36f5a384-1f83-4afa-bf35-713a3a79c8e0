//
//  InviteCodePatcher_special.m
//  mainbox
//
//  Created by ha<PERSON> on 2020/1/14.
//

#import "InviteCodePatcher_special.h"
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPVDN/UpVdnUtils.h>
#import <UPPageTrace/UPPageTraceInjection.h>
#import "InviteCodeService_special.h"
#import "InviteCodeViewModel_special.h"

@interface InviteCodePatcher_special ()

@property (nonatomic, strong) NSString *codeString;
@property (nonatomic, strong) NSString *nameString;
@property (nonatomic, strong) NSString *codeType;
@property (nonatomic, strong) NSString *channelCode;
@property (nonatomic, strong) NSString *activeCode;
@property (nonatomic, strong) NSMutableDictionary *dataDic;

@end

@implementation InviteCodePatcher_special

- (NSString *)name
{
    return NSStringFromClass(self.class);
}

- (NSInteger)priority
{
    return 1;
}

- (BOOL)patch:(id<Page>)page
{
    self.codeString = @"";
    self.nameString = @"";
    NSDictionary *dic = page.uri.queryParameterMap;
    [InviteCodeViewModel_special addInviteCodeDic:dic];

    return YES;
}

- (BOOL)isNeedPatch:(id<Page>)page
{
    if ([page.originURL containsString:@"type=inviteSource"]) {
        return YES;
    }
    return NO;
}
- (void)removeTrigger:(id<Page>)page
{
}

- (NSMutableDictionary *)dataDic
{
    if (!_dataDic) {
        _dataDic = NSMutableDictionary.dictionary;
    }
    return _dataDic;
}
@end
