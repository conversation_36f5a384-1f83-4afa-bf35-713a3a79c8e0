//
//  InviteCodeService_special.m
//  mainbox
//
//  Created by ha<PERSON> on 2020/1/14.
//

#import "InviteCodeService_special.h"
#import "HTTPRequestManager_special.h"
#import "UPNetWorkHeader_special.h"
#import "OHServerDefine_special.h"
#import <upuserdomain/UpUserDomainHolder.h>

#define inviteCodeUrl @"/omsappapi/inviteUser/v2/code/set"
#define UHOME_NET @"https://zj.haier.net"
#define JudgeUsedCode @"/omsappapi/inviteUser/code/setting/query"
#define GetNickNamebyCode @"/omsappapi/inviteUser/getUserInfo"
#define FillActivityChannelCode @"/omsappapi/inviteUser/v2/active/channel/set"
#define ALLCODEURL @"/omsappapi/inviteUser/v3/code/set"


#define LONG_TO_STRING(Code) [NSString stringWithFormat:@"%ld", (long)Code]
#define LINE_FMT(point, meg) @"%@,%@:%s [Line %d] :%@", [self class], point, __PRETTY_FUNCTION__, __LINE__, meg


@implementation InviteCodeService_special


+ (void)inviteCodeFilled:(NSMutableDictionary *)body
             withSuccess:(void (^)(NSDictionary *info))success
                 failure:(void (^)(HDError *error, NSDictionary *info))failure
{
    NSString *urlstring = [NSString stringWithFormat:@"%@%@", UHOME_NET, inviteCodeUrl];

    NSMutableDictionary *header = [[UPNetWorkHeader_special getInternationalAPPserverHttpHeader:inviteCodeUrl bodyDictionary:body] mutableCopy];
    header[@"Content-Type"] = @"application/json";
    header[@"accountToken"] = UpUserDomainHolder.instance.userDomain.oauthData.access_token;

    [HTTPRequestManager_special postRequestWithUrl:urlstring
        headers:header
        body:body
        OnOperation:^(AFHTTPRequestOperation_special *onOperation) {
        }
        completion:^(NSDictionary *responseDic) {
          success(responseDic);
        }
        error:^(NSError *connectError, long responseStatusCode) {
          if (connectError && failure) {
              failure([HDError initwith:HttpError errorCode:LONG_TO_STRING(responseStatusCode) message:connectError.description], connectError.userInfo);
          }
        }];
}

+ (void)judgeUsedInviteCodeWithSuccess:(void (^)(NSDictionary *info))success
                               failure:(void (^)(HDError *error, NSDictionary *info))failure
{
    NSString *urlstring = [NSString stringWithFormat:@"%@%@", UHOME_NET, JudgeUsedCode];
    NSMutableDictionary *header = [[UPNetWorkHeader_special getInternationalAPPserverHttpHeader:JudgeUsedCode bodyDictionary:nil] mutableCopy];
    header[@"Content-Type"] = @"application/json";
    header[@"accountToken"] = UpUserDomainHolder.instance.userDomain.oauthData.access_token;

    [HTTPRequestManager_special postRequestWithUrl:urlstring
        headers:header
        body:nil
        OnOperation:^(AFHTTPRequestOperation_special *onOperation) {
        }
        completion:^(NSDictionary *responseDic) {
          success(responseDic);
        }
        error:^(NSError *connectError, long responseStatusCode) {
          if (connectError && failure) {
              failure([HDError initwith:HttpError errorCode:LONG_TO_STRING(responseStatusCode) message:connectError.description], connectError.userInfo);
          }
        }];
}

+ (void)requestActivityChannelCode:(NSDictionary *)dic
                       WithSuccess:(void (^)(NSDictionary *info))success
                           failure:(void (^)(HDError *error, NSDictionary *info))failure
{
    NSString *urlstring = [NSString stringWithFormat:@"%@%@", UHOME_NET, FillActivityChannelCode];
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    body = [dic mutableCopy];
    NSMutableDictionary *header = [[UPNetWorkHeader_special getInternationalAPPserverHttpHeader:FillActivityChannelCode bodyDictionary:dic] mutableCopy];
    header[@"Content-Type"] = @"application/json";
    header[@"accountToken"] = UpUserDomainHolder.instance.userDomain.oauthData.access_token;

    [HTTPRequestManager_special postRequestWithUrl:urlstring
        headers:header
        body:body
        OnOperation:^(AFHTTPRequestOperation_special *onOperation) {
        }
        completion:^(NSDictionary *responseDic) {
          success(responseDic);
        }
        error:^(NSError *connectError, long responseStatusCode) {
          if (connectError && failure) {
              failure([HDError initwith:HttpError errorCode:LONG_TO_STRING(responseStatusCode) message:connectError.description], connectError.userInfo);
          }
        }];
}

+ (void)requestNewAllCode:(NSMutableDictionary *)body WithSuccess:(void (^)(NSDictionary *info))success
                  failure:(void (^)(HDError *error, NSDictionary *info))failure
{
    NSString *urlstring = [NSString stringWithFormat:@"%@%@", OH_OMS_AD_HOST, ALLCODEURL];

    NSMutableDictionary *header = [[UPNetWorkHeader_special getInternationalAPPserverHttpHeader:ALLCODEURL bodyDictionary:body] mutableCopy];
    header[@"Content-Type"] = @"application/json";
    header[@"accountToken"] = UpUserDomainHolder.instance.userDomain.oauthData.access_token;

    [HTTPRequestManager_special postRequestWithUrl:urlstring
        headers:header
        body:body
        OnOperation:^(AFHTTPRequestOperation_special *onOperation) {
        }
        completion:^(NSDictionary *responseDic) {
          success(responseDic);
        }
        error:^(NSError *connectError, long responseStatusCode) {
          if (connectError && failure) {
              failure([HDError initwith:HttpError errorCode:LONG_TO_STRING(responseStatusCode) message:connectError.description], connectError.userInfo);
          }
        }];
}


@end
