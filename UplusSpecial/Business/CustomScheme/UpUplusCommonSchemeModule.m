//
//  UpUplusCommonSchemeModule.m
//  UplusSpecial
//
//  Created by whenwe on 2023/7/14.
//

#import "UpUplusCommonSchemeModule.h"
#import <UPCore/UPContext.h>
#import <UpTrace/UPEventTrace.h>

static NSString *const UpSchemeLaunchSource = @"up_launch_source";

/**
 针对智家App的通用Scheme的特殊业务处理
 */
@interface UpUplusCommonSchemeModule ()

@end

@implementation UpUplusCommonSchemeModule

- (NSInteger)priorityForEvent:(enum UpLifeCircleEvent)event
{
    if (event == UpLifeCircleEventOpenURL) {
        return UpLifeCircleEventPriority.high;
    }
    return UpLifeCircleEventPriority.normal;
}

- (enum UpChainHandledResult)onOpenURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{
    //当使用 haierUplus:// 打开 app 时，openURL 实际是：haieruplus://,url
    //对大小写是不敏感的，所以此处需要这样判断

    if ([url.scheme compare:UPContext.sharedInstance.appSchemeUrl
                    options:NSCaseInsensitiveSearch] == NSOrderedSame) {

        // 1. haieruplus://data/?up_launch_source=xiaoyi
        // 2. haieruplus://target/https://www.baidu.com?up_launch_source=xiaoyi
        // 3. haieruplus://jump/https://www.baidu.com?up_launch_source=xiaoyi
        // 4. haieruplus://jump/?up_launch_source=xiaoyi

        ///埋点打开 App 的来源（
        ///需求：https://ihaier.feishu.cn/wiki/AWiZwFcnXiaO3Hk4btEcK6bLnDd ）
        NSString *launchSouce = [self getLaunchSource:url.absoluteString];

        [UPEventTrace.getInstance
                   trace:@"MB34270"
            withVariable:@{
                @"entrance" : [self stringIsAvailable:launchSouce] ? launchSouce : @"NULL"
            }];
    }
    return UpChainHandledResultIgnore;
}

- (BOOL)stringIsAvailable:(NSString *)string
{
    if (![string isKindOfClass:[NSString class]]) {
        return NO;
    }
    return string.length;
}

- (NSString *)getLaunchSource:(NSString *)urlString
{
    if (![self stringIsAvailable:urlString]) {
        return @"";
    }
    __block NSString *launchSource = @"";
    NSURLComponents *components =
        [NSURLComponents componentsWithString:urlString];
    [components.queryItems
        enumerateObjectsUsingBlock:^(NSURLQueryItem *_Nonnull obj, NSUInteger idx,
                                     BOOL *_Nonnull stop) {
          if ([UpSchemeLaunchSource isEqualToString:obj.name]) {
              launchSource = obj.value;
              *stop = YES;
          }
        }];
    return launchSource;
}

@end
