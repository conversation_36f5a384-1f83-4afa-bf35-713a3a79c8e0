//
//  JoinFamilyAlertPatcher.m
//  mainbox
//
//  Created by haier on 2019/11/5.
//

#import "JoinFamilyAlertPatcher.h"
#import <UPVDN/UPVDN.h>
#import <YYModel/YYModel.h>
#import <upuserdomain/UpUserDomainHolder.h>

@interface JoinFamilyAlertPatcher () <UpUserDomainObserver>

@property (nonatomic, strong) NSString *code;

@end

@implementation JoinFamilyAlertPatcher

- (NSString *)name
{
    return NSStringFromClass(self.class);
}

- (NSInteger)priority
{
    return 1;
}
- (BOOL)patch:(id<Page>)page
{
    NSString *url = [page.originURL stringByRemovingPercentEncoding];
    NSArray *array = [url componentsSeparatedByString:@"/"];
    self.code = array.lastObject;
    NSDictionary *indexDic = @{
        @"flutter_package_main_join_family_code" : self.code
    };
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:indexDic options:NSJSONWritingPrettyPrinted error:nil];
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

    NSMutableDictionary *dic = NSMutableDictionary.dictionary;
    dic[@"messageName"] = @"flutter_package_main_join_family";
    dic[@"messageData"] = jsonString;
    [[NSNotificationCenter defaultCenter] postNotificationName:@"SendMessage2Flutter" object:nil userInfo:dic];

    return YES;
}

- (BOOL)isNeedPatch:(id<Page>)page
{
    if ([page.originURL containsString:@"joinFamily"]) {
        return YES;
    }
    return NO;
}
- (void)removeTrigger:(id<Page>)page
{
}


@end
