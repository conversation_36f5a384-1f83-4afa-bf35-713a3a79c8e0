//
//  DeviceBindPatcher.swift
//  UplusSpecial
//
//  Created by 闫振 on 2025/3/13.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.


import UIKit
import UPVDN
import uplog
import UPStorage
import UPTools
@objcMembers
open class DeviceBindPatcher: NSObject, LogicPatch {
        
    public var name: String! {
        get  {
            return NSStringFromClass(self.classForCoder)
        }
    }
    
    public var priority: Int {
        get  {
            return 10
        }
    }
    
    
    // MARK: - Properties
    private let vdnRouteURLs: [[String: String]] = [
        
        ["url": "http://uplus.haier.com/uplusapp/main/guidIntro.html", "routeUrl": "mpaas://devicebind?entranceType=guidIntro"],
        ["url": "https://uplus.haier.com/uplusapp/main/guidIntro.html", "routeUrl": "mpaas://devicebind?entranceType=guidIntro"],
        ["url": "http://uplus.haier.com/uplusapp/main/qrcodescan.html", "routeUrl": "mpaas://devicebind"],
        ["url": "https://uplus.haier.com/uplusapp/main/qrcodescan.html", "routeUrl": "mpaas://devicebind"],
        ["url": "http://uplus.haier.com/uplusapp/main/deviceaddmodeladditional.html", "routeUrl": "mpaas://devicebind?entranceType=modeladditional"],
        ["url": "https://uplus.haier.com/uplusapp/main/deviceaddmodeladditional.html", "routeUrl": "mpaas://devicebind?entranceType=modeladditional"],
        ["url": "https://uplus.haier.com/uplusapp/main/collectwifiinfo.html", "routeUrl": "mpaas://devicebind?entranceType=collectwifiinfo"],
        ["url": "https://uplus.haier.com/uplusapp/bind/reportrepairentrance.html", "routeUrl": "mpaas://devicebind?entranceType=reportrepair"],
        ["url": "https://uplus.haier.com/uplusapp/main/bindsuccess.html", "routeUrl": "mpaas://devicebind?entranceType=bindsuccess"],
        ["url": "https://uplus.haier.com/uplusapp/bind/wifisettingalert.html", "routeUrl": "mpaas://devicebind?entranceType=collectwifiinfo&type=1"
        ],
        ["url": "https://uplus.haier.com/uplusapp/bind/nfcTouch.html", "routeUrl": "mpaas://devicebind"]
    ]
    
    // MARK: - Methods
    public func isNeedPatch(_ page: Page?) -> Bool {
        guard let page = page else { return false }
        guard let originURL = page.originURL,
              let components = URLComponents(string: originURL),
              let targetURL = URL(string: "\(components.scheme ?? "")://\(components.host ?? "")\(components.path)")
        else {
            return false
        }
     
        //如果是虚拟绑定,且不再绑定中，重定向跳转H5
        if isNFCBindingURL(targetURL.absoluteString) {
            return handleVirtualBindingURLIfNeeded(page)
        }
        
        //如果是配置表中的URL 且 资源已经安装 则重定向跳转H5
        if vdnRouteURLs.contains(where: { $0["url"] == targetURL.absoluteString }) {
            //不关心是否安装资源 无条件打开H5
            return true
        }
        return false
    }
    
    
    private func isNFCBindingURL(_ targetURL: String) -> Bool {
        return targetURL == "https://uplus.haier.com/uplusapp/bind/nfcTouch.html"
    }
    private func handleVirtualBindingURLIfNeeded(_ page: Page) -> Bool {
        
        if isBinding() {
            KVNProgressShow.showInfo("请等待设备添加完成后再试")
            return false;
        }
        //不关心是否安装资源 无条件打开H5
        return true
        
    }
    
    func isBinding() -> Bool {
        guard let currentVC = UpVdnUtils.getCurrentViewController() else {
            return false
        }
        if currentVC.originalURL?.hasPrefix("mpaas://devicebind") == true {//H5原生绑定
            return true
        }
        return false
    }
        
    
    public func patch(_ page: Page?) -> Bool {
        guard let page = page else { return false }
        guard let urlComp = page.uri as? NSURLComponents,
              let originURL = page.originURL,
              let com = NSURLComponents(string: originURL) else {
            return true
        }
        
        let sourceUrl = "\(com.scheme ?? "")://\(com.host ?? "")\(com.path ?? "")"
        
        for dic in vdnRouteURLs {
            if let url = dic["url"], url == sourceUrl {
                guard let routeUrl = dic["routeUrl"],
                      let newComp = NSURLComponents(string: routeUrl) else {
                    return true
                }
                
                // 拼接 query 参数
                if let queryItems = urlComp.queryItems {
                    newComp.queryItems = (newComp.queryItems ?? []) + queryItems
                }
                
                if let newUrl = newComp.url?.absoluteString {
                    UPPrintInfo(moduleName: "UplusSpecial", message: "[DeviceBindPatcher]|(resetURL:\(newUrl))")
                    page.resetURL(newUrl)
                    page.move(to: VdnPageStage.vdns)
                }
                return true
            }
        }
        return true
    }
    
    public func removeTrigger(_ page: Page?) {}
    
    
    
    
    
}
