//
//  DeviceBindLauncher.swift
//  UplusSpecial
//
//  Created by 闫振 on 2025/4/16.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.


import UIKit
import Foundation
import uplog
import UPVDN
@objcMembers
open class DeviceBindLauncher: NSObject, Launcher {
    
    public var launchHandledPaths: [String : [String]]! {
        
        return [
            "https": ["uplus.haier.com/uplusapp/bind/nfcTouch.html"]
        ]
    }
    
    
    public func launcherPage(_ page: Page?, complete: UpVdnCompleteBlock?, error: UpVdnErrorBlock?) {
        
        UPPrintInfo(moduleName: "UplusSpecial", message: "[DeviceBindLauncher] doNothing")

    }
    
    
    
}


