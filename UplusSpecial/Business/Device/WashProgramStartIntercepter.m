//
//  WashProgramStartIntercepter.m
//  UplusSpecial
//
//  Created by pc on 2025/8/11.
//

#import "WashProgramStartIntercepter.h"
#import <UPDevice/UpDevice.h>
#import <UPDevice/UpDeviceCommand.h>
#import <UPDevice/UPDeviceLog.h>
#import <UPDeviceInitKit/UplusDeviceUtils.h>

@interface WashProgramStartIntercepter ()
@property (nonatomic, strong) NSSet<NSString *> *washGroupNameKeys;
@end

@implementation WashProgramStartIntercepter

- (instancetype)init
{
    self = [super init];
    if (self) {
        _washGroupNameKeys = [NSSet setWithArray:@[
            @"grCardWash",
            @"grCommonWash",
            @"grAdvancedWash",
            @"grDrySet",
            @"grCommonWashUP",
            @"grCommonWashDN",
            @"grSuperAirWash",
            @"grSuperAirWashDN",
            @"grDryerSuperAirWash",
            @"grOnlineWash",
            @"grSuperAirWashOnline",
            @"grDryCloudProg",
            @"grDryOnline",
            @"grOnlineWashDN",
            @"grOnlineWashUP",
            @"grFPA",
            @"grSuperAirWashUP"
        ]];
    }
    return self;
}

#pragma mark - UpDeviceCommandIntercepter Protocol

- (BOOL)isMatch:(id<UpDevice>)device
{
    if (!device || !device.getInfo) {
        return NO;
    }

    NSString *typeId = device.getInfo.typeId;
    if (!typeId || typeId.length == 0 || [typeId characterAtIndex:0] == '1') {
        UPDeviceLogDebug(@"[%s][%d]wash typeID not match - typeid:%@", __PRETTY_FUNCTION__, __LINE__, typeId);
        return NO;
    }

    return [self isWashingMachineTypeid:typeId];
}

- (void)onCommandSuccess:(id<UpDevice>)device command:(id<UpDeviceCommand>)command
{
    if (!device || !command) {
        return;
    }
    if ([self.washGroupNameKeys containsObject:command.groupName]) {
        UPDeviceLogDebug(@"[%s][%d]wash program start detected for device: %@ - group name:%@",
                         __PRETTY_FUNCTION__, __LINE__, device.getInfo.deviceId, command.groupName);

        //TODO: 向实时活动发起洗衣机启动消息
    }
}

#pragma mark - Private Methods

- (BOOL)isWashingMachineTypeid:(NSString *)typeId
{
    NSString *hexResult = [UplusDeviceUtils deivceClassNumber:typeId deviceClassType:UPDeviceClassType_First];
    if ([hexResult isEqualToString:@"4"] || [hexResult isEqualToString:@"5"] || [hexResult isEqualToString:@"31"]) {
        return YES;
    }
    return NO;
}
@end
