//
//  GyOneKeyLoginModule.swift
//  UplusSpecial
//
//  Created by 郑连乐 on 2025/6/4.
//

import Foundation
import LaunchKitCommon
import UPCore
import UpVdnModule
import uplog
import GeYanSdk
import UPStorage
import UpTrace
import UPVDN
import UPUserDomain

var gtInitSuccess: Bool = false
var gtPreGetTokening: Bool = false

private let GyDataForFlutterKey = "GY_DATA_FOR_FLUTTER"

@objc public class GyOneKeyLoginModule: NSObject, ModuleProtocol, WorkflowTask, UpUserDomainObserver {
    // MARK: - Private Properties
    private var currentStage: LaunchStage?
    
    private let VerificationKey = "Verification.disableInitialize"
    
    // MARK: - ModuleProtocol Methods
    public func initializeTask(for stage: LaunchStage) -> WorkflowTask? {
        self.currentStage = stage
        switch stage {
        case .beforePrivacy, .afterPrivacy:
            return self
        default:
            return nil
        }
    }
    
    // MARK: - WorkflowTask Methods
    public func run() {
        switch currentStage {
        case .beforePrivacy:
            initBeforePrivacy()
        case .afterPrivacy:
            initAfterPrivacy()
        default:
            break
        }
    }
    
    // MARK: - Initialization Methods
    private func initBeforePrivacy() {
        let key = (UPFunctionToggle.shareInstance().toggleValue(forKey: VerificationKey) as AnyObject).boolValue
        if !(key ?? false) {
            let UpVdn = UPCore.sharedInstance().createService(UpVdnModuleServiceProtocol.self) as? UpVdnModuleServiceProtocol
            UpVdn?.register(GyOneKeyLoginPatcher() as! any LogicPatch as any LogicPatch)
            UpVdn?.add(GyOneKeyLoginLauncher() as! any Launcher as any Launcher)
        }
    }
    
    private func initAfterPrivacy() {
        let key = (UPFunctionToggle.shareInstance().toggleValue(forKey: VerificationKey) as AnyObject).boolValue
        if !(key ?? false) && UpUserDomainHolder.instance().userDomain.state() == .unLogin {
            addAdvertising()
        }
        UpUserDomainHolder.instance().userDomain.add(self)
    }
    
    // MARK: - Advertising Setup
    private func addAdvertising() {
        guard UpUserDomainHolder.instance().userDomain.state() != .didLogin else {
            return
        }
        
        UPPrintInfo(moduleName: "UplusSpecial", message: "getui init start")
        
        _ = Static.onceToken  // 触发执行
    }
    
    // MARK: - UpUserDomainObserver Methods
    public func onLogOut(_ userDomain: UpUserDomainDelegate) {
        let key = (UPFunctionToggle.shareInstance().toggleValue(forKey: VerificationKey) as AnyObject).boolValue
        if !(key ?? false) {
            // 提前进行预登录
            UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk start preLogin")
            Static.preGetToken()
        }
    }
}

private struct Static {
    static var executionCount = 0
    
    static let onceToken: Void = {
        // 预登录超时时长
        GeYanSdk.setPreLoginTimeout(5)
        // 设置IDFA
        GeYanSdk.setIDFA(UPContext.sharedInstance().appIdfa)
        // 初始化 SDK
        UPEventTrace.getInstance().trace("MBTI00004")
        GeYanSdk.start(withAppId: UPContext.sharedInstance().gYanAppId) { isSuccess, error, gtcid in
            if isSuccess {
                let variables = [
                    "code": (error as NSError?)?.code ?? -1,
                    "extend_info": error?.localizedDescription ?? "Unknown error"
                ]
                UPEventTrace.getInstance().trace("MBTI00006", withVariable: variables)
                
                UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk startWithAppId gtcid:\(gtcid ?? "")")
                gtInitSuccess = true
                
                // 提前进行预登录
                UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk start preLogin")
                Static.preGetToken()
            }else {
                let variables = [
                    "code": (error as NSError?)?.code ?? -1,
                    "extend_info": error?.localizedDescription ?? "Unknown error"
                ]
                UPEventTrace.getInstance().trace("MBTI00005", withVariable: variables)
                
                UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk startWithAppId error:\(error?.localizedDescription ?? "nil")")
            }
        }
    }()
    
    static func preGetToken() {
        UPEventTrace.getInstance().trace("MBTI00001")
        gtPreGetTokening = true
        GeYanSdk.preGetToken{ dic in
            gtPreGetTokening = false
            if (GeYanSdk.isPreGettedTokenValidate()) {
                let variables = [
                    "code": dic?["code"] ?? -1,
                    "extend_info": dic?["msg"] ?? "Unknown error"
                ]
                UPEventTrace.getInstance().trace("MBTI00002", withVariable: variables)
                
                UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk preLogin success")
                
                guard let gyuid = dic?["gyuid"] as? String,
                      let number = dic?["number"] as? String,
                      let operatorType = dic?["operatorType"] as? Int else {
                    UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk preLogin error")
                    return
                }
                var operatorKey = ""
                if operatorType == 0 { // 未知
                } else if operatorType == 1 { // 移动
                    operatorKey = "CM"
                }else if operatorType == 2 { // 联通
                    operatorKey = "CU"
                }else if operatorType == 3 { // 电信
                    operatorKey = "CT"
                }
                if let jsonData = generateJSONData(gyuid: gyuid, number: number, operatorKey: operatorKey) {
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        UPStorage.putMemoryString(GyDataForFlutterKey, value: jsonString)
                    }
                }
            }else {
                let variables = [
                    "code": dic?["code"] ?? -1,
                    "extend_info": dic?["msg"] ?? "Unknown error"
                ]
                UPEventTrace.getInstance().trace("MB31029", withVariable: variables)
                
                UPPrintInfo(moduleName: "UplusSpecial", message: "GeYanSdk preLogin error:\(dic?["code"] ?? "nil") msg:\(dic?["msg"] ?? "nil")")
                
                guard Static.executionCount < 2 else { return }
                Static.executionCount += 1
                Static.preGetToken()
            }
        }
        
    }
}

private func generateJSONData(gyuid: String, number: String, operatorKey: String) -> Data? {
    // 包装 gyuid 和 number 到内部字典
    let map = [
        "gyuid": gyuid,
        "number": number,
        "operator": operatorKey
    ] as [String : Any]
    
    // 转换为 JSON 数据
    do {
        let jsonData = try JSONSerialization.data(
            withJSONObject: map,
            options: .prettyPrinted
        )
        return jsonData
    } catch {
        print("JSON 序列化失败: \(error)")
        return nil
    }
}

