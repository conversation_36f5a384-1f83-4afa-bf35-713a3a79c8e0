//
//  GyOneKeyLoginPatcher.swift
//  UplusSpecial
//
//  Created by 郑连乐 on 2025/6/4.
//

import Foundation
import UPUserDomain
import UPVDN
import GeYanSdk
import UPVDN.LogicPatch
import UpTrace

@objcMembers
open class GyOneKeyLoginPatcher: NSObject, LogicPatch {
    private let ONEKEYLAUNCHER = "https://uplus.haier.com/uplusapp/mainbox/onekeylogin.html"

    public var name: String! {
        get {
            return NSStringFromClass(self.classForCoder)
        }
    }
    
    public var priority: Int {
        get  {
            return 1
        }
    }
    
    public func patch(_ page: Page?) -> Bool {
        guard let page = page else { return false }
        
        page.resetURL(ONEKEYLAUNCHER)
        page.move(to: .vdns)
        return true
    }
    
    public func isNeedPatch(_ page: Page?) -> Bool {
        guard let page = page else { return false }
        guard let uri = page.uri else { return false }
        
        // 检查用户是否已登录
        guard UpUserDomainHolder.instance().userDomain.state() != .didLogin else {
            return false
        }
        
        // 检查当前vdn的目标页面是登录页
        guard let host = uri.host, host == "usercenter" else {
            return false
        }
        
        // 检查个推初始化成功
        guard gtInitSuccess else {
            let variables = [
                "code": 100001,
                "extend_info": "初始化未成功"
            ] as [String : Any]
            UPEventTrace.getInstance().trace("MB34032", withVariable: variables)
            return false
        }
        
        // 检查自定义参数fromOneKeyLogin的值不等于1
        var params = (uri as NSURLComponents).queryParameterMap()
        if params == nil {
            params = [String:String]()
        }
        let fromOneKeyLogin = (params?["fromOneKeyLogin"] ?? "") as String
        guard fromOneKeyLogin != "1" else {
            return false
        }
        
        // 预取号中
        if gtPreGetTokening {
            let variables = [
                "code": 100003,
                "extend_info": "预取号中"
            ] as [String : Any]
            UPEventTrace.getInstance().trace("MB34032", withVariable: variables)
            return false
        }
        
        // 检查个推SDK预登录可用
        guard GeYanSdk.isPreGettedTokenValidate() else {
            let variables = [
                "code": 100002,
                "extend_info": "预登录结果无效"
            ] as [String : Any]
            UPEventTrace.getInstance().trace("MB34032", withVariable: variables)
            return false
        }
        
        return true
    }
    
    public func removeTrigger(_ page: Page) {
        
    }
}


