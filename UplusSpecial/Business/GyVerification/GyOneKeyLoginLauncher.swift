//
//  GyOneKeyLoginLauncher.swift
//  UplusSpecial
//
//  Created by 郑连乐 on 2025/6/4.
//

import Foundation
import UPVDN
import UpTrace
@objcMembers
open class GyOneKeyLoginLauncher: NSObject, Launcher {
    
    public var launchHandledPaths: [String: [String]]! {
        return [
            "https": ["uplus.haier.com/uplusapp/mainbox/onekeylogin.html"]
        ]
    }
    
    public func launcherPage(_ page: Page?, complete: UpVdnCompleteBlock?, error: UpVdnErrorBlock?) {

        UPVDNManager.share().vdnDomain.go(toPage: "flutter://verification", flag: .push, parameters: nil, complete: { dic in
        }, error: { error in
            let variables = [
                "code": (error as NSError?)?.code ?? -1,
                "extend_info": error?.localizedDescription ?? "Unknown error"
            ]
            UPEventTrace.getInstance().trace("MB34032", withVariable: variables)
        })
    }
}


