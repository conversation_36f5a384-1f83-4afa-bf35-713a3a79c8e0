//
//  ResourceTrackerIMP.m
//  UPInitBaseKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/11/4.
//

#import "ResourceTrackerIMP.h"
#import <UpTrace/UPEventTrace.h>
#import <UPLog/UPLog.h>
#import <UPPageTrace/UPPageTraceReachability.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <UPResource/UPResourceConfig.h>
#import <AFNetworking/AFNetworkReachabilityManager.h>
#import <UPCore/UPFunctionToggle.h>
static NSString *const kUplusResourceInstalled = @"uplusResourceInstalled";
static NSString *const kCTRadioAccessTechnologyNRNSA = @"CTRadioAccessTechnologyNRNSA";
static NSString *const kCTRadioAccessTechnologyNR = @"CTRadioAccessTechnologyNR";
@implementation ResourceTrackerIMP

- (void)reportInstallResult:(UPResourceReportInfo *)reportInfo
{
    if (reportInfo != nil) {
        NSDictionary *param = @{ @"res_name" : reportInfo.res_name,
                                 @"res_version" : reportInfo.res_version,
                                 @"res_type" : reportInfo.res_type };
        [[UPEventTrace getInstance] trace:@"MB16459" withVariable:param];
    }
}

- (void)reportABTestTrack:(UPResourceReportInfo *)reportInfo
{
    [[UPEventTrace getInstance] trace:@"MB17694"];
}

- (void)reporResDownloadTrack:(UPResourceReportInfo *)reportInfo
{
    if (reportInfo != nil && [reportInfo.res_type isEqualToString:@"mPaaS"]) {
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        param[@"res_name"] = reportInfo.res_name;
        param[@"res_version"] = reportInfo.res_version;
        param[@"res_type"] = reportInfo.res_type;
        param[@"time_length"] = reportInfo.time_length;
        param[@"value"] = reportInfo.value;
        param[@"net"] = [UPResourceConfig shareInstance].enablePrivacyAgreement ? [self getNetworkType] : @"";
        param[@"reTryCount"] = reportInfo.reTryCount;
        param[@"result"] = reportInfo.result;
        param[@"user_id"] = reportInfo.user_id;
        param[@"showDialog"] = reportInfo.showDialog ? @"true" : @"false";
        param[@"resourceIP"] = reportInfo.resourceIP;
        param[@"downloadBlockInfos"] = reportInfo.downloadBlockInfos;
        param[@"downloadNetInfos"] = reportInfo.downloadNetInfos;
        param[@"downloadBackgroundRunInfos"] = reportInfo.downloadBackgroundRunInfos;
        [[UPEventTrace getInstance] trace:@"MB18015" withVariable:param.copy];
    }
}
- (NSString *)getNetworkTypeOld
{
    UPPageTraceReachability *reachability = [UPPageTraceReachability reachabilityWithHostName:@"www.baidu.com"];
    NetworkStatus internetStatus = [reachability currentReachabilityStatus];
    switch (internetStatus) {
        case ReachableViaWiFi:
            return @"WiFi";
            break;
        case ReachableViaWWAN:
            return [self getNetType];
            break;
        case NotReachable:
            return @"NONE";
        default:
            break;
    }
    return @"NONE";
}
- (NSString *)getNetworkType
{
    //判断开关状态；
    BOOL enable = [[UPFunctionToggle shareInstance]
          boolForKey:@"pagetraceHook.enable"
        defaultValue:NO];
    if (enable) {
        return [self getNetworkTypeOld];
    }

    AFNetworkReachabilityStatus internetStatus = [AFNetworkReachabilityManager sharedManager].networkReachabilityStatus;
    switch (internetStatus) {
        case AFNetworkReachabilityStatusReachableViaWiFi:
            return @"WiFi";
            break;
        case AFNetworkReachabilityStatusReachableViaWWAN:
            return [self getNetType];
            break;
        case AFNetworkReachabilityStatusNotReachable:
            return @"NONE";
        default:
            break;
    }
    return @"NONE";
}
- (NSString *)getNetType
{
    CTTelephonyNetworkInfo *info = [[CTTelephonyNetworkInfo alloc] init];
    NSString *currentStatus = info.currentRadioAccessTechnology;
    NSString *netconnType = @"";
    NSArray *typeStrings2G = @[ CTRadioAccessTechnologyEdge, CTRadioAccessTechnologyGPRS, CTRadioAccessTechnologyCDMA1x ];
    NSArray *typeStrings3G = @[ CTRadioAccessTechnologyHSDPA, CTRadioAccessTechnologyWCDMA, CTRadioAccessTechnologyHSUPA, CTRadioAccessTechnologyCDMAEVDORev0, CTRadioAccessTechnologyCDMAEVDORevA, CTRadioAccessTechnologyCDMAEVDORevB, CTRadioAccessTechnologyeHRPD ];

    NSArray *typeStrings4G = @[ CTRadioAccessTechnologyLTE ];
    NSArray *typeStrings5G = @[ kCTRadioAccessTechnologyNRNSA, kCTRadioAccessTechnologyNR ];
    if ([typeStrings2G containsObject:currentStatus]) {
        netconnType = @"2G";
    }
    else if ([typeStrings3G containsObject:currentStatus]) {
        netconnType = @"3G";
    }
    else if ([typeStrings4G containsObject:currentStatus]) {
        netconnType = @"4G";
    }
    else if ([typeStrings5G containsObject:currentStatus]) {
        netconnType = @"5G";
    }
    return netconnType;
}
@end
