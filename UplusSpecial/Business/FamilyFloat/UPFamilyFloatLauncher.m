//
//  UPFamilyFloatLauncher.m
//  mainbox
//
//  Created by 路标 on 2021/1/7.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPFamilyFloatLauncher.h"
#import "UPFamilyFloatView.h"
#import <UPVDN/UpVdnUtils.h>

@implementation UPFamilyFloatLauncher
- (NSDictionary<NSString *, NSArray<NSString *> *> *)launchHandledPaths
{
    return @{ @"http" : @[ @"uplus.haier.com/uplusapp/main/familyFloatView.html" ],
              @"https" : @[ @"uplus.haier.com/uplusapp/main/familyFloatView.html" ] };
}

- (void)launcherPage:(id<Page>)page
            complete:(UpVdnCompleteBlock)complete
               error:(UpVdnErrorBlock)error
{
    if ([page.originURL containsString:@"/main/familyFloatView.html"]) {
        NSString *deviceId = page.uri.queryParameterMap[@"deviceId"];
        UPFamilyFloatView *inviteView = [[UPFamilyFloatView alloc] initWithDeviceId:deviceId];
        [inviteView show];
    }
}

@end
