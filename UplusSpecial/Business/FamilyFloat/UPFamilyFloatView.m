//
//  UPFamilyFloatView.m
//  mainbox
//
//  Created by 路标 on 2021/1/7.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPFamilyFloatView.h"
#import <uplog/UPLog.h>
#import <UPVDN/Page.h>
#import <UHMasonry/UHMasonry.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <UHWebImage/UIImageView+UHWebCache.h>
#import <UPPageTrace/UPPageTraceInjection.h>
#import <AFNetworking/AFNetworkReachabilityManager.h>
#import <UPTools/UPToast.h>
// 打点
#define FAMILY_FLOAT_URL @"uplus.haier.com/uplusapp/main/familyFloatView.html"
#define FAMILY_FLOAT_ACTION_SHOW @"MB13800"
#define FAMILY_FLOAT_ACTION_INVITE_1 @"MB13801"
#define FAMILY_FLOAT_ACTION_INVITE_2 @"MB13803"
#define FAMILY_FLOAT_ACTION_SHARE @"MB13804"
#define FAMILY_FLOAT_ACTION_MANAGE @"MB13805"
#define FAMILY_FLOAT_ACTION_HIDE @"MB13806"

// 布局相关
#define FAMILY_FLOAT_SCALE ([UIScreen mainScreen].bounds.size.width / 375.0)
#define FAMILY_FLOAT_HEIGHT 316
#define FAMILY_FLOAT_CORNER 10
#define FAMILY_FLOAT_HEADER 55
#define FAMILY_FLOAT_EDGE 12
#define FAMILY_FLOAT_TITLE_WIDTH 138
#define FAMILY_FLOAT_NAME_WIDTH 72
#define FAMILY_FLOAT_IS_IPHONEX (familyFloat_isPhoneX())
#define FAMILY_FLOAT_SAFE_BOTTOM ((FAMILY_FLOAT_IS_IPHONEX) ? 20 : 0)
#define FAMILY_FLOAT_FONT @"PingFangSC-Regular"

// 跳转URL
#define FAMILY_MANAGE_URL @"mpaas://familymanage?needAuthLogin=1&familyId=%@#familyinfo"
#define FAMILY_MANAGE_LIST_URL @"mpaas://familymanage?needAuthLogin=1#familylist"
#define FAMILY_INVITE_URL @"mpaas://familymanage?needAuthLogin=1#members/invite"
#define FAMILY_MYFAMILY_URL @"mpaas://familymanage?needAuthLogin=1#members/myFamily"

CG_INLINE BOOL familyFloat_isPhoneX()
{
    BOOL isIPhoneXSerial = NO;
    if (@available(iOS 11.0, *)) {
        isIPhoneXSerial = [[UIApplication sharedApplication] delegate].window.safeAreaInsets.bottom > 0.0;
    }
    return isIPhoneXSerial;
}

@interface UIImage (FamilyFloat)

+ (UIImage *)MBBundleImage:(NSString *)name;

@end

@implementation UIImage (FamilyFloat)

+ (UIImage *)MBBundleImage:(NSString *)name
{
    NSString *imageName = [NSString stringWithFormat:@"UplusSpecial.bundle/%@", name];
    return [UIImage imageNamed:imageName];
}

@end

@interface UPFamilyFloatView () <UIGestureRecognizerDelegate>

@property (nonatomic, strong) id<UDDeviceDelegate> device;

@property (nonatomic, strong) id<UDFamilyDelegate> family;

@property (nonatomic, weak) UIViewController *controller;

@property (nonatomic, weak) UIView *floatView;

@property (nonatomic) CGFloat safeBottom;

@end

@implementation UPFamilyFloatView

#pragma mark - Interfaces
- (instancetype)initWithDeviceId:(NSString *)deviceId
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        [self loadFamilyInfo:deviceId];
        if (nil == self.family) {
            UPLogInfo(@"mainbox", @"UPFamilyFloatView family not found for deviceId %@", deviceId);
            return nil;
        }
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.0];
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGesture:)];
        tap.delegate = self;
        [self addGestureRecognizer:tap];
        [self initSubviews];
    }
    return self;
}

- (void)show
{
    self.controller = [UpVdnUtils getCurrentViewController];
    [self.controller.view addSubview:self];
    [self uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.trailing.top.bottom.equalTo(self.controller.view);
    }];
    [self setNeedsLayout];
    [self layoutIfNeeded];

    [self.floatView uh_updateConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(self.uh_bottom).offset(-FAMILY_FLOAT_HEIGHT - FAMILY_FLOAT_CORNER - self.safeBottom);
      make.height.mas_equalTo(@(FAMILY_FLOAT_HEIGHT + FAMILY_FLOAT_CORNER * 2 + self.safeBottom));
      make.leading.equalTo(self);
      make.trailing.equalTo(self);
    }];
    [self setNeedsLayout];
    [UIView animateWithDuration:0.3
        animations:^{
          self.backgroundColor = [self.backgroundColor colorWithAlphaComponent:0.5];
          [self layoutIfNeeded];
        }
        completion:^(BOOL finished) {
          [self traceAction:FAMILY_FLOAT_ACTION_SHOW];
        }];
}

- (void)dismiss
{
    if (self.superview != nil) {
        [self.floatView uh_updateConstraints:^(UHConstraintMaker *make) {
          make.top.equalTo(self.uh_bottom);
          make.height.mas_equalTo(@(FAMILY_FLOAT_HEIGHT + FAMILY_FLOAT_CORNER * 2 + self.safeBottom));
          make.leading.equalTo(self);
          make.trailing.equalTo(self);
        }];
        [self setNeedsLayout];
        [UIView animateWithDuration:0.3
            animations:^{
              self.backgroundColor = [self.backgroundColor colorWithAlphaComponent:0.0];
              [self layoutIfNeeded];
            }
            completion:^(BOOL finished) {
              if (self.superview != nil) {
                  [self removeFromSuperview];
              }
            }];
    }
}

#pragma mark - Initialization
- (void)loadFamilyInfo:(NSString *)deviceId
{
    if (deviceId.length < 1) {
        UPLogInfo(@"mainbox", @"UPFamilyFloatView `deviceId` is null");
        return;
    }
    id<UDUserDelegate> user = [UpUserDomainHolder instance].userDomain.user;
    id<UDDeviceDelegate> device = [user getDeviceById:deviceId];
    if (nil == device) {
        UPLogInfo(@"mainbox", @"UPFamilyFloatView device not found");
        return;
    }
    self.device = device;

    id<UDFamilyDelegate> family = [user getFamilyById:device.familyId];
    self.family = family;
}

- (void)initSubviews
{
    self.safeBottom = FAMILY_FLOAT_SAFE_BOTTOM;

    UIView *floatView = [self createFloatView];

    UIView *header = [self floatViewHeader];
    [floatView addSubview:header];
    [header uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.trailing.top.equalTo(floatView);
      make.height.mas_equalTo(@(FAMILY_FLOAT_HEADER));
    }];

    UIView *bodyView;
    CGFloat bodyMargin = 12;
    CGFloat bodyHeight = 178;
    if (self.family.members.count > 1) {
        bodyView = [self multiMemberView];
        bodyMargin = 30;
        bodyHeight = 160;
    }
    else {
        bodyView = [self singleMemberView];
    }
    [floatView addSubview:bodyView];
    [bodyView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.trailing.equalTo(floatView);
      make.top.equalTo(header.uh_bottom).offset(bodyMargin);
      make.height.mas_equalTo(@(bodyHeight));
    }];

    UIView *footerView = [self footerButtonsView];
    [floatView addSubview:footerView];
    [footerView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.trailing.equalTo(floatView);
      make.top.equalTo(bodyView.uh_bottom).offset(15);
      make.height.mas_equalTo(@(44));
    }];
}

- (UIView *)createFloatView
{
    UIView *floatView = [[UIView alloc] init];
    floatView.backgroundColor = [UIColor whiteColor];
    floatView.layer.cornerRadius = 10;
    floatView.layer.masksToBounds = YES;
    [self addSubview:floatView];
    [floatView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(self.uh_bottom);
      make.height.mas_equalTo(@(FAMILY_FLOAT_HEIGHT + FAMILY_FLOAT_CORNER * 2 + self.safeBottom));
      make.leading.equalTo(self);
      make.trailing.equalTo(self);
    }];

    self.floatView = floatView;
    return floatView;
}

- (UIView *)floatViewHeader
{
    UIView *header = [[UIView alloc] init];

    UIButton *settingButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [settingButton setImage:[UIImage MBBundleImage:@"family_float_setting"] forState:UIControlStateNormal];
    [settingButton addTarget:self action:@selector(settingsButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [header addSubview:settingButton];
    [settingButton uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.mas_equalTo(@(FAMILY_FLOAT_EDGE));
      make.centerY.equalTo(header);
    }];

    UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeButton setImage:[UIImage MBBundleImage:@"family_float_close"] forState:UIControlStateNormal];
    [closeButton addTarget:self action:@selector(closeButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [header addSubview:closeButton];
    [closeButton uh_makeConstraints:^(UHConstraintMaker *make) {
      make.trailing.mas_equalTo(@(-FAMILY_FLOAT_EDGE));
      make.centerY.equalTo(header);
    }];

    UILabel *title = [[UILabel alloc] init];
    title.textAlignment = NSTextAlignmentCenter;
    title.text = self.family.info.familyName;
    title.font = [UIFont fontWithName:@"PingFangSC-Medium" size:17];
    title.textColor = [UIColor colorWithWhite:51 / 255.0 alpha:1.0];
    [header addSubview:title];
    [title uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(header);
      make.centerY.equalTo(header);
      make.width.mas_equalTo(@(FAMILY_FLOAT_TITLE_WIDTH * FAMILY_FLOAT_SCALE));
    }];

    UIView *line = [[UIView alloc] init];
    line.backgroundColor = [UIColor colorWithWhite:238 / 255.0 alpha:1.0];
    [header addSubview:line];
    [line uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.trailing.bottom.equalTo(header);
      make.height.mas_equalTo(@(0.5));
    }];
    return header;
}

- (UIView *)singleMemberView
{
    UIView *singleView = [[UIView alloc] init];

    UILabel *bodyTitle = [[UILabel alloc] init];
    bodyTitle.textAlignment = NSTextAlignmentCenter;
    bodyTitle.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:15];
    bodyTitle.textColor = [UIColor colorWithWhite:51 / 255.0 alpha:1.0];
    bodyTitle.text = @"邀请家人，共享该设备的使用权限";
    [singleView addSubview:bodyTitle];
    [bodyTitle uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(singleView);
      make.leading.mas_equalTo(@(FAMILY_FLOAT_EDGE));
      make.trailing.mas_equalTo(@(-FAMILY_FLOAT_EDGE));
      make.height.mas_equalTo(@(20));
    }];

    UIImageView *linkIcon = [[UIImageView alloc] init];
    linkIcon.image = [UIImage MBBundleImage:@"family_float_link"];
    [singleView addSubview:linkIcon];
    [linkIcon uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(bodyTitle.uh_bottom).offset(44);
      make.centerX.equalTo(singleView);
    }];

    CGFloat iconMargin = 25;
    UIImageView *devImgView = [[UIImageView alloc] init];
    devImgView.image = [UIImage MBBundleImage:@"family_float_device"];
    [singleView addSubview:devImgView];
    [devImgView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerY.equalTo(linkIcon);
      make.trailing.equalTo(linkIcon.uh_leading).offset(-iconMargin);
    }];

    UILabel *devName = [[UILabel alloc] init];
    devName.textAlignment = NSTextAlignmentCenter;
    devName.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:12];
    devName.textColor = [UIColor colorWithWhite:153 / 255.0 alpha:1.0];
    devName.text = self.device.deviceName;
    [singleView addSubview:devName];
    [devName uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(devImgView);
      make.top.equalTo(devImgView.uh_bottom).offset(10);
      make.width.mas_equalTo(@(FAMILY_FLOAT_NAME_WIDTH * FAMILY_FLOAT_SCALE));
    }];

    UIImageView *avatarView = [[UIImageView alloc] init];
    avatarView.image = [UIImage MBBundleImage:@"family_float_avatar"];
    [singleView addSubview:avatarView];
    [avatarView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(linkIcon.uh_trailing).offset(iconMargin);
      make.centerY.equalTo(linkIcon);
    }];

    UILabel *memberName = [[UILabel alloc] init];
    memberName.textAlignment = NSTextAlignmentCenter;
    memberName.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:12];
    memberName.textColor = [UIColor colorWithWhite:153 / 255.0 alpha:1.0];
    memberName.text = @"家人";
    [singleView addSubview:memberName];
    [memberName uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(avatarView);
      make.top.equalTo(avatarView.uh_bottom).offset(10);
      make.width.mas_equalTo(@(FAMILY_FLOAT_NAME_WIDTH * FAMILY_FLOAT_SCALE));
    }];

    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.textAlignment = NSTextAlignmentCenter;
    descLabel.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:10];
    descLabel.textColor = [UIColor colorWithWhite:153 / 255.0 alpha:1.0];
    descLabel.text = @"家庭成员可共享多种家庭应用，查看和管理家人健康信息";
    [singleView addSubview:descLabel];
    [descLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(singleView);
      make.leading.mas_equalTo(@(FAMILY_FLOAT_EDGE));
      make.trailing.mas_equalTo(@(-FAMILY_FLOAT_EDGE));
      make.bottom.equalTo(singleView);
    }];

    return singleView;
}

- (UIView *)multiMemberView
{
    UIView *multiView = [[UIView alloc] init];
    static NSInteger _inviteViewMaxMemberCount = 3;
    NSInteger count;
    if (self.family.members.count > _inviteViewMaxMemberCount) {
        count = _inviteViewMaxMemberCount + 1;
    }
    else {
        count = self.family.members.count;
    }
    UIImage *avatar = [UIImage MBBundleImage:@"family_float_avatar"];
    UIImage *moreImg = [UIImage MBBundleImage:@"family_float_more"];
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat margin = (screenWidth - count * avatar.size.width) / count;
    for (int i = 0; i < count; i++) {
        id<UDFamilyMemberDelegate> member = self.family.members[i];
        CGFloat leading = -0.5 * margin + i * avatar.size.width + (i + 1) * margin;
        UIImageView *avatarView = [[UIImageView alloc] init];
        if (i == _inviteViewMaxMemberCount) {
            avatarView.image = moreImg;
        }
        else {
            avatarView.layer.cornerRadius = avatar.size.width / 2;
            avatarView.layer.masksToBounds = YES;
            [avatarView uh_setImageWithURL:[NSURL URLWithString:member.memberInfo.avatarUrl] placeholderImage:avatar];
        }
        [multiView addSubview:avatarView];
        [avatarView uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.equalTo(multiView);
          make.leading.mas_equalTo(@(leading));
          make.width.mas_equalTo(@(avatar.size.width));
          make.height.mas_equalTo(@(avatar.size.height));
        }];
        UILabel *name = [[UILabel alloc] init];
        name.textAlignment = NSTextAlignmentCenter;
        name.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:12];
        name.textColor = [UIColor colorWithWhite:153 / 255.0 alpha:1.0];
        name.text = i == count - 1 ? @"" : member.memberInfo.name;
        [multiView addSubview:name];
        [name uh_makeConstraints:^(UHConstraintMaker *make) {
          make.centerX.equalTo(avatarView);
          make.top.equalTo(avatarView.uh_bottom).offset(10);
          make.width.mas_equalTo(@(FAMILY_FLOAT_NAME_WIDTH * FAMILY_FLOAT_SCALE));
        }];
    }
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.textAlignment = NSTextAlignmentCenter;
    descLabel.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:10];
    descLabel.textColor = [UIColor colorWithWhite:153 / 255.0 alpha:1.0];
    descLabel.text = @"家庭成员可共享多种家庭应用，查看和管理家人健康信息";
    [multiView addSubview:descLabel];
    [descLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(multiView);
      make.leading.mas_equalTo(@(FAMILY_FLOAT_EDGE));
      make.trailing.mas_equalTo(@(-FAMILY_FLOAT_EDGE));
      make.bottom.equalTo(multiView);
    }];
    UIImage *arrowImg = [UIImage MBBundleImage:@"family_float_forward"];
    UIButton *shareButton = [UIButton buttonWithType:UIButtonTypeCustom];
    shareButton.titleLabel.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:15];
    [shareButton setTitleColor:[UIColor colorWithWhite:51 / 255.0 alpha:1.0] forState:UIControlStateNormal];
    [shareButton setImage:arrowImg forState:UIControlStateNormal];
    NSString *title = [NSString stringWithFormat:@"%ld位家人共享中", (long)self.family.members.count];
    [shareButton setTitle:title forState:UIControlStateNormal];
    CGSize titleSize = [title boundingRectWithSize:CGSizeMake(1000, 22) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{ NSFontAttributeName : shareButton.titleLabel.font } context:nil].size;
    static CGFloat _arrowImageRight = 10;
    static CGFloat _titleImageMargin = 6;
    CGFloat imageLeft = titleSize.width + _titleImageMargin;
    CGFloat titleLeft = -(arrowImg.size.width + _arrowImageRight);
    [shareButton setTitleEdgeInsets:UIEdgeInsetsMake(0, titleLeft, 0, 0)];
    [shareButton setImageEdgeInsets:UIEdgeInsetsMake(0, imageLeft, 0, -_arrowImageRight)];
    [shareButton addTarget:self action:@selector(shareButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [multiView addSubview:shareButton];
    [shareButton uh_makeConstraints:^(UHConstraintMaker *make) {
      make.centerX.equalTo(multiView);
      make.bottom.equalTo(descLabel.uh_top).offset(-20);
    }];
    return multiView;
}

- (UIView *)footerButtonsView
{
    UIView *footer = [[UIView alloc] init];

    static CGFloat _footerHeight = 44;
    UIColor *blueColor = [UIColor colorWithRed:34 / 255.0 green:131 / 255.0 blue:226 / 255.0 alpha:1.0];
    if (self.family.members.count > 1) {
        UIButton *inviteButton = [UIButton buttonWithType:UIButtonTypeCustom];
        inviteButton.layer.cornerRadius = _footerHeight / 2;
        inviteButton.layer.borderColor = blueColor.CGColor;
        inviteButton.layer.borderWidth = 1.0;
        inviteButton.layer.masksToBounds = YES;
        inviteButton.backgroundColor = [UIColor whiteColor];
        inviteButton.titleLabel.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:15];
        [inviteButton setTitleColor:blueColor forState:UIControlStateNormal];
        [inviteButton setTitle:@"邀请家人" forState:UIControlStateNormal];
        [inviteButton addTarget:self action:@selector(inviteButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
        [footer addSubview:inviteButton];

        UIButton *manageButton = [UIButton buttonWithType:UIButtonTypeCustom];
        manageButton.layer.cornerRadius = _footerHeight / 2;
        manageButton.layer.masksToBounds = YES;
        manageButton.backgroundColor = blueColor;
        manageButton.titleLabel.font = [UIFont fontWithName:FAMILY_FLOAT_FONT size:15];
        [manageButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [manageButton setTitle:@"家庭管理" forState:UIControlStateNormal];
        [manageButton addTarget:self action:@selector(familyManagementButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
        [footer addSubview:manageButton];

        static CGFloat buttonMargin = 32;
        CGFloat buttonWidth = ([UIScreen mainScreen].bounds.size.width - 2 * FAMILY_FLOAT_EDGE - buttonMargin) / 2;
        [inviteButton uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.bottom.equalTo(footer);
          make.leading.mas_equalTo(@(FAMILY_FLOAT_EDGE));
          make.width.mas_equalTo(@(buttonWidth));
        }];

        [manageButton uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.bottom.equalTo(footer);
          make.trailing.mas_equalTo(@(-FAMILY_FLOAT_EDGE));
          make.width.mas_equalTo(@(buttonWidth));
        }];
    }
    else {
        UIButton *inviteButton = [UIButton buttonWithType:UIButtonTypeCustom];
        inviteButton.layer.cornerRadius = _footerHeight / 2;
        inviteButton.layer.masksToBounds = YES;
        inviteButton.backgroundColor = blueColor;
        [inviteButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [inviteButton setTitle:@"立即邀请" forState:UIControlStateNormal];
        [inviteButton addTarget:self action:@selector(inviteButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
        [footer addSubview:inviteButton];
        [inviteButton uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.bottom.equalTo(footer);
          make.leading.mas_equalTo(@(FAMILY_FLOAT_EDGE));
          make.trailing.mas_equalTo(@(-FAMILY_FLOAT_EDGE));
        }];
    }

    return footer;
}

#pragma mark - Actions
- (void)settingsButtonTouched:(id)sender
{
    if (![AFNetworkReachabilityManager sharedManager].isReachable) {
        [[UPToast shareManager] showWithText:@"网络开小差了，请稍后再试～"];
        return;
    }

    NSString *title = @"关闭家庭标识区";
    NSString *content = @"您可以在当前家庭所有设备页面关闭家庭标识区";

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:title message:content preferredStyle:UIAlertControllerStyleAlert];
    NSDictionary *attr;
    attr = @{NSFontAttributeName : [UIFont fontWithName:FAMILY_FLOAT_FONT size:17], NSForegroundColorAttributeName : [UIColor colorWithWhite:51 / 255.0 alpha:1.0]};
    NSAttributedString *text = [[NSAttributedString alloc] initWithString:title attributes:attr];
    [alert setValue:text forKey:@"attributedTitle"];

    attr = @{NSFontAttributeName : [UIFont fontWithName:FAMILY_FLOAT_FONT size:12],
             NSForegroundColorAttributeName : [UIColor colorWithWhite:102 / 255.0 alpha:1.0]};
    text = [[NSAttributedString alloc] initWithString:content attributes:attr];
    [alert setValue:text forKey:@"attributedMessage"];

    UIAlertAction *cancel = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:nil];
    UIColor *textColor = [UIColor colorWithWhite:102 / 255.0 alpha:1.0];
    [cancel setValue:textColor forKey:@"titleTextColor"];

    UIAlertAction *setting = [UIAlertAction actionWithTitle:@"前往设置"
                                                      style:UIAlertActionStyleDefault
                                                    handler:^(UIAlertAction *_Nonnull action) {
                                                      [self traceAction:FAMILY_FLOAT_ACTION_HIDE];
                                                      NSString *url = [NSString stringWithFormat:FAMILY_MANAGE_URL, self.family.familyId];
                                                      [UpVdn goToPage:url flag:VdnPageFlagPush parameters:nil complete:nil error:nil];
                                                      [self dismiss];
                                                    }];
    textColor = [UIColor colorWithRed:34 / 255.0 green:131 / 255.0 blue:226 / 255.0 alpha:1.0];
    [setting setValue:textColor forKey:@"titleTextColor"];

    [alert addAction:cancel];
    [alert addAction:setting];

    [self.controller presentViewController:alert animated:YES completion:nil];
}

- (void)closeButtonTouched:(id)sender
{
    [self dismiss];
}

- (void)inviteButtonTouched:(id)sender
{
    if (![AFNetworkReachabilityManager sharedManager].isReachable) {
        [[UPToast shareManager] showWithText:@"网络开小差了，请稍后再试～"];
        return;
    }

    if (self.family.members.count > 1) {
        [self traceAction:FAMILY_FLOAT_ACTION_INVITE_2];
    }
    else {
        [self traceAction:FAMILY_FLOAT_ACTION_INVITE_1];
    }
    [UpVdn goToPage:FAMILY_INVITE_URL flag:VdnPageFlagPush parameters:nil complete:nil error:nil];
    [self dismiss];
}

- (void)shareButtonTouched:(id)sender
{
    if (![AFNetworkReachabilityManager sharedManager].isReachable) {
        [[UPToast shareManager] showWithText:@"网络开小差了，请稍后再试～"];
        return;
    }

    [self traceAction:FAMILY_FLOAT_ACTION_SHARE];
    [UpVdn goToPage:FAMILY_MYFAMILY_URL flag:VdnPageFlagPush parameters:nil complete:nil error:nil];
    [self dismiss];
}

- (void)familyManagementButtonTouched:(id)sender
{
    if (![AFNetworkReachabilityManager sharedManager].isReachable) {
        [[UPToast shareManager] showWithText:@"网络开小差了，请稍后再试～"];
        return;
    }

    [self traceAction:FAMILY_FLOAT_ACTION_MANAGE];
    [UpVdn goToPage:FAMILY_MANAGE_LIST_URL flag:VdnPageFlagPush parameters:nil complete:nil error:nil];
    [self dismiss];
}

#pragma mark - Gesture
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if (CGRectContainsPoint(self.floatView.frame, [gestureRecognizer locationInView:self])) {
        return NO;
    }
    return YES;
}

- (void)handleTapGesture:(UIGestureRecognizer *)gesture
{
    [self dismiss];
}

#pragma mark - Trace
- (void)traceAction:(NSString *)action
{
    [[UPPageTraceInjection getInstance].pageTraceManager savePageClickEvent:FAMILY_FLOAT_URL action_Code:action extend_info:@{ @"FamilyID" : self.family.familyId }];
}

@end
