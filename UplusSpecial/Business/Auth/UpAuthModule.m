//
//  UpAuthModule.m
//  UplusSpecial
//
//  Created by 路标 on 2023/5/26.
//

#import "UpAuthModule.h"
#import <UPCore/UPContext.h>
#import "UpAuthManager.h"
#import <uplog/UPLog.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>

@interface UpAuthModule () <UpWorkflowTask>

@property (nonatomic) UpLaunchStage currentStage;

/// App安装后没有启动过(用户还没有明确的同意或拒绝隐私协议),被三方App拉起授权,要等点击同意后才开始授权流程,同意之前先记录下URL
@property (nonatomic, copy) NSURL *waitingProcessURL;
@property (nonatomic, strong) NSDictionary *waitingProcessOptions;

@end

@implementation UpAuthModule

- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    self.currentStage = stage;
    switch (stage) {
        case UpLaunchStageBeforePrivacy:
        case UpLaunchStageAfterPrivacy:
            return self;
        default:
            return nil;
    }
}

- (void)run
{
    switch (self.currentStage) {
        case UpLaunchStageBeforePrivacy:
            [self initBeforePrivacy];
            break;
        case UpLaunchStageAfterPrivacy:
            [self initAfterPrivacy];
            break;
        default:
            break;
    }
}

- (void)initBeforePrivacy
{
    [UpVdn appendItemWithKey:A2AVDNPageURL
                       value:@"native://UplusSpecial/UpAuthViewController"];
}

- (void)initAfterPrivacy
{
    /// 1. 点击"同意"隐私协议后会调用一次该方法
    /// 2. 已经同意隐私协议的情况下冷启动,会调用一次该方法
    /// 只有在三方App通过`zjauth://`拉起智家,并且还没同意隐私协议时,self.waitingProcessURL才有值
    if (self.waitingProcessURL) {
        dispatch_after(
            dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)),
            dispatch_get_main_queue(), ^{
              [self processOpenURL:self.waitingProcessURL
                           options:self.waitingProcessOptions];
            });
    }
}

- (void)onAppBackground
{
    self.waitingProcessURL = nil;
    self.waitingProcessOptions = nil;
}

- (BOOL)shouldHandleGuestURL
{
    /// 响应游客模式下的openURL
    /// 用户还没有选择同意或拒绝隐私协议的场景,点击"同意"隐私协议,会调用moduleLazyInit
    return YES;
}

- (enum UpChainHandledResult)
    onOpenURL:(NSURL *)url
      options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{
    if (!url) {
        return UpChainHandledResultIgnore;
    }

    NSURLComponents *components =
        [[NSURLComponents alloc] initWithURL:url
                     resolvingAgainstBaseURL:NO];
    if (NO == [components.scheme isEqualToString:@"zjauth"]) {
        UPLogInfo(A2AModuleName, @"url scheme is `%@`, but A2A auth want `zjzuth`",
                  components.scheme);
        return UpChainHandledResultIgnore;
    }

    if (NO == [UPContext sharedInstance].isAgreenPrivacyPolicy) {
        self.waitingProcessURL = url;
        self.waitingProcessOptions = options;
        return UpChainHandledResultTrue;
    }

    return [self processOpenURL:url options:options];
}

- (enum UpChainHandledResult)processOpenURL:(NSURL *)url
                                    options:(NSDictionary *)options
{
    self.waitingProcessURL = nil;
    self.waitingProcessOptions = nil;

    BOOL result =
        [[UpAuthManager sharedInstance] openAuthUrl:url
                                            options:options];
    return result ? UpChainHandledResultTrue : UpChainHandledResultFalse;
}

@end
