//
//  UpAuthManager+Reporter.m
//  UplusSpecial
//
//  Created by 路标 on 2023/6/12.
//

#import "UpAuthManager+Reporter.h"
#import <uplog/UPLog.h>
#import <UIKit/UIKit.h>
#import <UPCore/UPContext.h>
#import <UpTrace/UPEventTrace.h>
@implementation UpAuthManager (Reporter)
- (void)report:(NSString *)fmt, ...
{
    va_list args;
    va_start(args, fmt);
    NSString *msg = [[NSString alloc] initWithFormat:fmt arguments:args];
    va_end(args);
    UPLogInfo(A2AModuleName, @"%@", msg);
    if ([UPContext sharedInstance].env == UPEnvironmentAcceptance && self.isDebug && self.redirectUrl) {
        NSString *UrlStr = [NSString stringWithFormat:@"%@?errorInfo=%@", self.redirectUrl, [msg stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UrlStr] options:@{} completionHandler:nil];
    }
}

+ (void)track:(NSString *)eventId variables:(NSDictionary *__nullable)variables
{
    if (variables.count) {
        [[UPEventTrace getInstance] trace:eventId withVariable:variables];
    }
    else {
        [[UPEventTrace getInstance] trace:eventId];
    }
}

@end
