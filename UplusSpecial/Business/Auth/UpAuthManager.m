//
//  UpAuthManager.m
//  UplusSpecial
//
//  Created by 路标 on 2023/5/26.
//

#import "UpAuthManager.h"
#import "UpAuthManager+Reporter.h"
#import <UPVDN/Page.h>
#import <UPCore/UPSHA256.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <uplog/UPLog.h>
#import "UpAuthCodeRequest.h"

@interface UpAuthManager ()

/// 海极网分配给三方App的systemId
@property (nonatomic, copy) NSString *systemId;

/// 海极网分配给三方App的appId
@property (nonatomic, copy) NSString *appId;

/// 回调三方App的url
@property (nonatomic, copy) NSString *redirectUrl;

@property (nonatomic, copy) NSString *state;

// MARK: - 调试属性
@property (nonatomic) BOOL isDebug;

@end

@implementation UpAuthManager
+ (instancetype)sharedInstance
{
    static dispatch_once_t _authToken;
    static UpAuthManager *_manager = nil;
    dispatch_once(&_authToken, ^{
      if (_manager == nil) {
          _manager = [[UpAuthManager alloc] init];
      }
    });

    return _manager;
}

- (NSSet *)supportedBusinessPaths
{
    static NSSet *_businessPaths;
    if (_businessPaths == nil) {
        _businessPaths = [NSSet setWithObjects:@"/deviceauth", nil];
    }
    return _businessPaths;
}

- (BOOL)openAuthUrl:(NSURL *)url options:(NSDictionary *)options
{
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    [self clear];

    NSDictionary *params = [self parseParameters:components.queryItems];

    if (NO == [self.supportedBusinessPaths containsObject:components.path]) {
        [self report:@"Does NOT support auth path: %@", components.path];
        [self clear];
        return NO;
    }

    if (NO == [self isRequiredParamsValid:params path:components.path]) {
        [self report:@"Some required parameter(s) is missing"];
        [self clear];
        return NO;
    }

    NSSet<NSString *> *requiredParams = [self requiedParamNamesWithBusinessPath:components.path];
    if ([requiredParams containsObject:@"sign"]) {
        NSDictionary *signInfo = [self getSignInfoWithPath:components.path params:params];
        NSString *calculated = signInfo[@"calculatedSign"];
        NSString *received = signInfo[@"receivedSign"];
        if (NO == [calculated isEqualToString:received]) {
            [self report:@"Invalid sign: source=%@, sign=%@, received Sign=%@", signInfo[@"source"], calculated, received];
            [self clear];
            return NO;
        }
    }

    // 1. 跳转授权页
    NSDictionary *vdnParams = @{ @"needAuthLogin" : @"1",
                                 @"hidesBottomBarWhenPushed" : @"1" };
    __weak typeof(self) weakself = self;
    [UpVdn goToPage:A2AVDNPageURL
        flag:VdnPageFlagPush
        parameters:vdnParams
        complete:^(NSDictionary *data) {
          UPLogInfo(A2AModuleName, @"goto authPage success");
        }
        error:^(NSError *error) {
          [weakself report:@"goto authPage error: %@", error.localizedDescription];
          [weakself clear];
        }];
    return YES;
}

- (void)clear
{
    self.isDebug = NO;
    self.redirectUrl = nil;
    self.systemId = nil;
    self.appId = nil;
    self.state = nil;
}

- (BOOL)isRequiredParamsValid:(NSDictionary *)params path:(NSString *)path
{
    NSMutableSet<NSString *> *requiredParams = [self requiedParamNamesWithBusinessPath:path];
    NSMutableSet<NSString *> *receivedParams = [NSMutableSet setWithArray:params.allKeys];
    UPLogInfo(A2AModuleName, @"required params: %@", requiredParams);
    UPLogInfo(A2AModuleName, @"received params: %@", receivedParams);
    [requiredParams minusSet:receivedParams];
    return requiredParams.count == 0;
}


- (NSMutableSet<NSString *> *)requiedParamNamesWithBusinessPath:(NSString *)path
{
    static NSDictionary *_path2NamesInfo = nil;
    if (_path2NamesInfo == nil) {
        _path2NamesInfo = @{
            @"/deviceauth" : [NSSet setWithObjects:@"systemid",
                                                   @"appid",
                                                   @"redirect_url",
                                                   @"sign",
                                                   nil],
        };
    }
    return [_path2NamesInfo[path] mutableCopy];
}

- (NSDictionary *)getSignInfoWithPath:(NSString *)path params:(NSDictionary *)params
{
    NSString *state = params[@"state"] ?: @"";
    NSString *source = [NSString stringWithFormat:@"%@%@%@%@%@",
                                                  path,
                                                  params[@"redirect_url"],
                                                  params[@"systemid"],
                                                  state,
                                                  params[@"appid"]];
    NSString *calculatedSign = [UPSHA256 getSHA256:source];
    return @{ @"source" : source,
              @"calculatedSign" : calculatedSign,
              @"receivedSign" : params[@"sign"]
    };
}

- (NSDictionary *)parseParameters:(NSArray<NSURLQueryItem *> *)queryItems
{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [queryItems enumerateObjectsUsingBlock:^(NSURLQueryItem *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      params[obj.name] = [obj.value stringByRemovingPercentEncoding];
    }];
    self.systemId = params[@"systemid"];
    self.appId = params[@"appid"];
    self.redirectUrl = params[@"redirect_url"];
    self.state = params[@"state"];
    self.isDebug = params[@"debug"];
    return params;
}

- (void)requestAuthCode:(void (^)(NSURL *__nullable callbackURL))completionBlock
{
    UpAuthCodeRequest *request = [[UpAuthCodeRequest alloc] init];
    __weak typeof(self) weakself = self;
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSURL *resultUrl;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          NSDictionary *respInfo = (NSDictionary *)responseObject;
          NSString *retCode = respInfo[@"retCode"];
          if ([retCode isEqualToString:@"00000"] && respInfo[@"data"]) {
              NSString *code = [weakself parseAuthCode:respInfo[@"data"]];
              if (code.length) {
                  resultUrl = [weakself generateAuthCallbackURL:@"000000" authCode:code];
              }
              else {
                  [weakself report:@"request auth code success, but could not parse auth code"];
              }
          }
          else {
              [weakself report:[request errorDescription:retCode]];
          }
      }
      else {
          [weakself report:@"Auth code request response error data, %@", responseObject];
      }
      [weakself clear];
      dispatch_async(dispatch_get_main_queue(), ^{
        completionBlock(resultUrl);
      });
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          [weakself report:error.localizedDescription];
          [weakself clear];
          dispatch_async(dispatch_get_main_queue(), ^{
            completionBlock(nil);
          });
        }];
}

- (NSURL *)generateAuthCallbackURL:(NSString *)retCode authCode:(NSString *)authCode
{
    NSCharacterSet *charSet = [NSCharacterSet URLQueryAllowedCharacterSet];

    // self.redirectUrl 是经过url decode的,回调时如果有参数需要把参数重新encode
    NSURLComponents *urlComponents = [NSURLComponents componentsWithString:self.redirectUrl];
    // 如果传过来的redirectUrl中带参数,需要把参数再重新url encode
    NSMutableDictionary *urlParams = [NSMutableDictionary dictionary];
    for (NSURLQueryItem *item in urlComponents.queryItems) {
        urlParams[item.name] = [item.value stringByAddingPercentEncodingWithAllowedCharacters:charSet];
    }

    // 拼接授权结果参数retcode、code、state
    urlParams[@"retcode"] = [retCode stringByAddingPercentEncodingWithAllowedCharacters:charSet];
    urlParams[@"code"] = [authCode stringByAddingPercentEncodingWithAllowedCharacters:charSet];
    if (self.state.length) {
        urlParams[@"state"] = [self.state stringByAddingPercentEncodingWithAllowedCharacters:charSet];
    }

    NSURLComponents *callbackComp = [[NSURLComponents alloc] init];
    callbackComp.scheme = urlComponents.scheme;
    callbackComp.host = urlComponents.host;
    callbackComp.path = urlComponents.path;
    NSMutableArray<NSURLQueryItem *> *items = [NSMutableArray array];
    [urlParams enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
      NSURLQueryItem *it = [NSURLQueryItem queryItemWithName:key value:obj];
      [items addObject:it];
    }];
    callbackComp.queryItems = items;
    return callbackComp.URL;
}

- (NSString *)parseAuthCode:(NSDictionary *)retData
{
    NSString *code = @"";
    NSString *redirectCodeUrl = retData[@"redirectCodeUrl"];
    if (redirectCodeUrl && redirectCodeUrl.length) {
        NSURLComponents *components = [NSURLComponents componentsWithString:redirectCodeUrl];
        for (NSURLQueryItem *item in components.queryItems) {
            if ([item.name isEqualToString:@"code"]) {
                code = item.value;
                break;
            }
        }
    }
    return code;
}

@end
