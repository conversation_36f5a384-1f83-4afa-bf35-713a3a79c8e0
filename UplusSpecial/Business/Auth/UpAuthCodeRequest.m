//
//  UpAuthCodeRequest.m
//  UplusSpecial
//
//  Created by 路标 on 2023/6/13.
//

#import "UpAuthCodeRequest.h"
#import <UPCore/UPContext.h>
#import <upnetwork/UPCommonServerHeader.h>
#import "UpAuthManager.h"

@implementation UpAuthCodeRequest
- (NSString *)baseURL
{
    if ([[UPContext sharedInstance] env] == UPEnvironmentAcceptance) {
        return @"https://zj-yanshou.haier.net";
    }
    return @"https://zj.haier.net";
}

- (NSString *)path
{
    return @"/api-gw/oauthserver/third/party/v1/client/jump";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{ @"systemId" : [UpAuthManager sharedInstance].systemId };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:(NSDictionary *)self.requestBody];
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSString *)errorDescription:(NSString *)code
{
    static NSDictionary *_errorInfo = nil;
    if (_errorInfo == nil) {
        _errorInfo = @{
            @"43004" : @"无权调用，或所传client_id/client_secret非法",
            @"43005" : @"授权异常或失败",
            @"43006" : @"应用未被授权此 grant_type",
            @"43007" : @"系统不支持此grant_type",
            @"43028" : @"客户端IP不能为空",
            @"43050" : @"redirect_uri非法或为空",
            @"43085" : @"token和userId信息不匹配",
            @"43086" : @"客户端agent非法或为空",
            @"43087" : @"target信息获取异常",
            @"43088" : @"非法的target信息",
            @"43089" : @"target gateway信息并未提供用户中心",
            @"43090" : @"redirect_uri和跳转gateway的域名不一致",
        };
    }
    return _errorInfo[code] ?: @"未知异常";
}

@end
