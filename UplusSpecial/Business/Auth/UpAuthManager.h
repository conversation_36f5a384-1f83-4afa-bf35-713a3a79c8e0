//
//  UpAuthManager.h
//  UplusSpecial
//
//  Created by 路标 on 2023/5/26.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
static NSString *const A2AModuleName = @"A2AAuth";
static NSString *const A2AVDNPageURL = @"https://uplus.haier.com/uplusapp/yeelightauth.html";

/// 智家App给第三方授权
@interface UpAuthManager : NSObject

+ (instancetype)sharedInstance;

/// 海极网分配给三方App的systemId
@property (nonatomic, copy, readonly) NSString *systemId;

/// 海极网分配给三方App的appId
@property (nonatomic, copy, readonly) NSString *appId;

/// 回调三方App的url
@property (nonatomic, copy, readonly) NSString *redirectUrl;

@property (nonatomic, copy, readonly) NSString *state;

/// 智家App给第三方授权
/// - url: 第三方拉起智家App时传入的url
/// - options: 智家App被拉起时,AppDelegate的openURL方法中的options参数
- (BOOL)openAuthUrl:(NSURL *)url options:(NSDictionary *)options;

/// 清除记录的回调信息,包括调试属性及三方App属性
- (void)clear;

/// 从App Server获取授权code
- (void)requestAuthCode:(void (^)(NSURL *__nullable callbackURL))completionBlock;

// MARK: - 调试属性
/// 第三方在拉起智家时增加参数debug=1
@property (nonatomic, readonly) BOOL isDebug;

@end

NS_ASSUME_NONNULL_END
