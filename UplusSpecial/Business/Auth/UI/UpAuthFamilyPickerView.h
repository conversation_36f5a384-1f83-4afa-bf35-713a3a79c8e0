//
//  UpAuthFamilyPickerView.h
//  UplusSpecial
//
//  Created by 路标 on 2023/6/12.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class UpAuthFamilyPickerView;
@protocol UDFamilyDelegate;

@protocol UpAuthFamilyPickerViewDelegate <NSObject>

- (void)familyPickerViewDidCancel:(UpAuthFamilyPickerView *)pickerView;

- (void)familyPickerView:(UpAuthFamilyPickerView *)pickerView didPickFamily:(id<UDFamilyDelegate>)family;

@end

@interface UpAuthFamilyPickerView : UIView

- (instancetype)initWithDelegate:(id<UpAuthFamilyPickerViewDelegate>)delegate;

- (void)show;

- (void)dismiss;

@end

NS_ASSUME_NONNULL_END
