//
//  UpAuthFamilyPickerCell.m
//  UplusSpecial
//
//  Created by 路标 on 2023/6/13.
//

#import "UpAuthFamilyPickerCell.h"
#import <UHMasonry/UHMasonry.h>

@interface UpAuthFamilyPickerCell ()

@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UIImageView *selectedIcon;

@property (nonatomic) CGFloat screenFactor;

@end

@implementation UpAuthFamilyPickerCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupSubviews];
    }
    return self;
}

- (void)awakeFromNib
{
    [super awakeFromNib];
    [self setupSubviews];
}

- (void)setupSubviews
{
    self.screenFactor = [UIScreen mainScreen].bounds.size.width / 375;
    self.contentView.backgroundColor = [UIColor whiteColor];
    self.backgroundColor = [UIColor whiteColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.separatorInset = UIEdgeInsetsZero;

    NSString *iconName = @"UplusSpecial.bundle/auth_family_sel";
    self.selectedIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:iconName]];
    [self.contentView addSubview:self.selectedIcon];
    [self.selectedIcon uh_makeConstraints:^(UHConstraintMaker *make) {
      make.trailing.equalTo(@(-12));
      make.centerY.equalTo(self.contentView);
    }];

    self.nameLabel = [[UILabel alloc] init];
    self.nameLabel.font = [UIFont fontWithName:@"" size:15 * self.screenFactor];
    self.nameLabel.textColor = [[UIColor blackColor] colorWithAlphaComponent:0.93];
    [self.contentView addSubview:self.nameLabel];
    [self.nameLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(16));
      make.centerY.equalTo(self.contentView);
      make.trailing.lessThanOrEqualTo(self.selectedIcon.uh_leading).offset(-12);
    }];
}

- (NSString *)familyName
{
    return self.nameLabel.text;
}

- (void)setFamilyName:(NSString *)familyName
{
    self.nameLabel.text = familyName;
}

- (void)markSelected:(BOOL)selected
{
    self.selectedIcon.hidden = !selected;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated
{
    [super setSelected:selected animated:animated];
}

@end
