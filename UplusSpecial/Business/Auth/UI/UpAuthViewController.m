//
//  UpAuthViewController.m
//  UplusSpecial
//
//  Created by 路标 on 2023/6/12.
//

#import "UpAuthViewController.h"
#import <UPLog/UPLog.h>
#import <UHMasonry/UHMasonry.h>
#import <UPTools/UIButton+Factory.h>
#import "UpAuthPageCell.h"
#import "UpAuthManager+Reporter.h"
#import "UpAuthFamilyPickerView.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import <UHWebImage/UHWebImage.h>
#import <UHProgressHUD/UHProgressHUD.h>
#import <UPTools/UPToast.h>
#import <UPCore/UPContext.h>

@interface UpAuthViewController () <UITableViewDelegate, UITableViewDataSource, UpAuthFamilyPickerViewDelegate>

/// key是三方app的systemId,value中有三方App的名称和图标
@property (nonatomic, strong) NSDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *appInfo;

@property (nonatomic, strong) NSArray<NSString *> *accessItems;

@property (nonatomic) CGFloat screenFactor;

@property (nonatomic, weak) UIView *headerLine;
@property (nonatomic, weak) UIButton *authButton;
@property (nonatomic, weak) UIImageView *avatarView;
@property (nonatomic, weak) UILabel *nicknameLabel;

@end

@interface UpAuthViewController (UserInfo) <UpUserDomainObserver>

/// 能够拉起授权页,说明已经登录,但是用户信息和家庭信息可能还没有刷新,这里需要等待刷新
- (void)updateUserInfo;

@end

@implementation UpAuthViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    self.screenFactor = [UIScreen mainScreen].bounds.size.width / 375.0;
    [self initDataSource];
    [self setupSubviews];
    [self updateUserInfo];

    [UpAuthManager track:@"MB33847"
               variables:@{
                   @"app_version" : [UPContext sharedInstance].appVersion,
                   @"OSName" : @"ios",
                   @"entrance" : @"易来",
               }];
}

- (void)initDataSource
{
    self.appInfo = @{
        @"SV-ISPYL-0000" : @{@"name" : @"易来", @"icon" : @"yeelight_icon"},
    };

    self.accessItems = @[
        @"允许该第三方应用访问您海尔智家的个人资料信息（昵称、头像等）；",
        @"允许海尔智家使用您于该第三方应用已綁定的设备信息并加入海尔智能家庭服务（如有）；",
        @"允许海尔智家使用您于该第三方应用已綁定的设备信息。"
    ];
}

- (void)setupSubviews
{
    [self setupThirdAppInfoView];
    [self setupBottomButtons];
    [self setupUplusInfoView];
}

/// 三方App信息视图,图标+名称
- (void)setupThirdAppInfoView
{
    NSDictionary *appInfo = self.appInfo[UpAuthManager.sharedInstance.systemId];
    NSString *iconName = appInfo[@"icon"];
    UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:[NSString stringWithFormat:@"UplusSpecial.bundle/%@", iconName]]];
    [self.view addSubview:icon];
    [icon uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(@(92 * self.screenFactor));
      make.centerX.equalTo(self.view);
    }];

    NSString *name = appInfo[@"name"];
    NSString *desc = [NSString stringWithFormat:@"使用海尔智家账号登录%@", name];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:desc];
    UIFont *font = [UIFont fontWithName:@"PingFang SC" size:17 * self.screenFactor];
    NSDictionary *attr = @{
        NSFontAttributeName : font,
        NSForegroundColorAttributeName : [[UIColor blackColor] colorWithAlphaComponent:0.8],
    };
    [attrStr addAttributes:attr range:NSMakeRange(0, desc.length - name.length)];

    UIColor *blue = [UIColor colorWithRed:34 / 255.0 green:131 / 255.0 blue:229 / 255.0 alpha:1.0];
    attr = @{
        NSFontAttributeName : font,
        NSForegroundColorAttributeName : blue,
    };
    [attrStr addAttributes:attr range:NSMakeRange(desc.length - name.length, name.length)];
    UILabel *label = [[UILabel alloc] init];
    label.attributedText = attrStr;
    [self.view addSubview:label];
    [label uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(icon.uh_bottom).offset(16 * self.screenFactor);
      make.centerX.equalTo(self.view);
    }];

    UIView *headerLine = [[UIView alloc] init];
    headerLine.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.1];
    [self.view addSubview:headerLine];
    [headerLine uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(label.uh_bottom).offset(40 * self.screenFactor);
      make.height.equalTo(@(1));
      make.leading.equalTo(@(16));
      make.trailing.equalTo(@(-16));
    }];
    self.headerLine = headerLine;
}

/// 智家App用户信息及具体的授权内容
- (void)setupUplusInfoView
{
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    if (@available(iOS 11.0, *)) {
        tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    tableView.contentInset = UIEdgeInsetsMake(0, 0, 20, 0);
    tableView.estimatedRowHeight = 64 * self.screenFactor;
    tableView.delegate = self;
    tableView.dataSource = self;
    [tableView registerClass:[UpAuthPageCell class] forCellReuseIdentifier:@"UpAuthPageCell"];
    tableView.tableHeaderView = [self createTableHeaderView];
    [self.view addSubview:tableView];
    [tableView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.trailing.equalTo(@(0));
      make.top.equalTo(self.headerLine.uh_bottom);
      make.bottom.equalTo(self.authButton.uh_top);
    }];
}

/// 智家App用户头像昵称+浅灰色文字描述
- (UIView *)createTableHeaderView
{
    NSString *prompt = @"为实现本服务目的您同意授权第三方应用及海尔智家使用您的如下信息：";
    UIFont *promptFont = [UIFont fontWithName:@"PingFang SC" size:13 * self.screenFactor];
    CGFloat maxWidth = [UIScreen mainScreen].bounds.size.width - 16 * 2;

    UILabel *promptLabel = [[UILabel alloc] init];
    promptLabel.font = promptFont;
    promptLabel.textColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
    promptLabel.numberOfLines = 0;
    promptLabel.lineBreakMode = NSLineBreakByWordWrapping;
    promptLabel.text = prompt;
    CGSize promptSize = [promptLabel sizeThatFits:CGSizeMake(maxWidth, CGFLOAT_MAX)];
    CGFloat promptHeight = promptSize.height;

    static NSString *defaultIcon = @"UplusSpecial.bundle/auth_default_avatar";
    UIImage *image = [UIImage imageNamed:defaultIcon];
    CGFloat height = 30 * self.screenFactor + image.size.height + 16 * self.screenFactor + promptHeight + 12 * self.screenFactor;

    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, height)];

    UIImageView *avatar = [[UIImageView alloc] initWithImage:image];
    avatar.layer.cornerRadius = image.size.width / 2;
    avatar.layer.masksToBounds = YES;
    [header addSubview:avatar];
    self.avatarView = avatar;

    UILabel *nickName = [[UILabel alloc] init];
    nickName.font = [UIFont fontWithName:@"PingFang SC" size:15 * self.screenFactor];
    nickName.textColor = [[UIColor blackColor] colorWithAlphaComponent:0.8];
    nickName.text = @"";
    [header addSubview:nickName];
    self.nicknameLabel = nickName;

    [header addSubview:promptLabel];

    [avatar uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(16));
      make.top.equalTo(@(30 * self.screenFactor));
      make.width.equalTo(@(image.size.width));
      make.height.equalTo(@(image.size.height));
    }];

    [nickName uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(avatar.uh_trailing).offset(12);
      make.centerY.equalTo(avatar);
      make.trailing.equalTo(@(-16));
    }];

    [promptLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(16));
      make.trailing.equalTo(@(-16));
      make.top.equalTo(avatar.uh_bottom).offset(16 * self.screenFactor);
      make.height.equalTo(@(promptHeight));
    }];

    return header;
}

/// 授权和取消按钮
- (void)setupBottomButtons
{
    static CGFloat height = 40.0;
    UIColor *color = [[UIColor blackColor] colorWithAlphaComponent:0.8];
    UIFont *font = [UIFont fontWithName:@"PingFang SC" size:16 * self.screenFactor];
    UIButton *cancel = [UIButton createButtonWithTitle:@"取消" titleColor:color backgroudColor:self.view.backgroundColor font:font];
    [cancel addTarget:self action:@selector(cancelButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:cancel];

    color = [UIColor colorWithRed:34 / 255.0 green:131 / 255.0 blue:226 / 255.0 alpha:1.0];
    UIButton *auth = [UIButton createButtonWithTitle:@"确认授权" titleColor:[UIColor whiteColor] backgroudColor:color font:font];
    auth.layer.cornerRadius = height / 2.0;
    [auth addTarget:self action:@selector(authButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:auth];

    [cancel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(16));
      make.trailing.equalTo(@(-16));
      make.bottom.equalTo(self.view).offset(-56 * self.screenFactor);
      make.height.equalTo(@(height));
    }];

    [auth uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(16));
      make.trailing.equalTo(@(-16));
      make.bottom.equalTo(cancel.uh_top).offset(-8);
      make.height.equalTo(@(height));
    }];
    self.authButton = auth;
}

- (void)authButtonTouched:(UIButton *)button
{
    button.enabled = NO;
    [self showFamilyPickerView];

    [UpAuthManager track:@"MB33849"
               variables:@{
                   @"app_version" : [UPContext sharedInstance].appVersion,
                   @"OSName" : @"ios",
                   @"entrance" : @"易来",
               }];

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      button.enabled = YES;
    });
}

- (void)cancelButtonTouched:(UIButton *)button
{
    [[UpAuthManager sharedInstance] report:@"Authorization canceled by user"];
    [[UpAuthManager sharedInstance] clear];

    [UpAuthManager track:@"MB33848"
               variables:@{
                   @"app_version" : [UPContext sharedInstance].appVersion,
                   @"OSName" : @"ios",
                   @"entrance" : @"易来",
               }];

    [self.navigationController popViewControllerAnimated:YES];
}

- (void)showFamilyPickerView
{
    UpAuthFamilyPickerView *picker = [[UpAuthFamilyPickerView alloc] initWithDelegate:self];
    [picker show];
}

// MARK: - UITableViewDelegate && dataSource
// MARK: - 具体授权的每一条信息
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.accessItems.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return [UpAuthPageCell heightForText:self.accessItems[indexPath.row]];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UpAuthPageCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UpAuthPageCell" forIndexPath:indexPath];
    cell.descInfo = self.accessItems[indexPath.row];
    return cell;
}

// MARK: -
// MARK: - UpAuthFamilyPickerViewDelegate
- (void)familyPickerViewDidCancel:(UpAuthFamilyPickerView *)pickerView
{
    [UpAuthManager track:@"MB33850" variables:nil];
}

- (void)familyPickerView:(UpAuthFamilyPickerView *)pickerView didPickFamily:(id<UDFamilyDelegate>)family
{
    [UHProgressHUD showHUDAddedTo:self.view animated:YES];
    UPLogInfo(A2AModuleName, @"select family %@", family.info.familyName);
    __weak typeof(self) weakself = self;
    [[UpAuthManager sharedInstance] requestAuthCode:^(NSURL *_Nullable callbackURL) {
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UHProgressHUD hideHUDForView:weakself.view animated:NO];
        if (callbackURL) {
            [[UIApplication sharedApplication] openURL:callbackURL options:@{} completionHandler:nil];
            [self.navigationController popViewControllerAnimated:NO];
        }
        else {
            [[UPToast shareManager] showWithText:@"授权失败，请稍后再试"];
        }
      });
    }];
    [UpAuthManager track:@"MB33851" variables:nil];
}

@end

@implementation UpAuthViewController (UserInfo)

- (void)updateUserInfo
{
    self.authButton.enabled = NO;

    [[UpUserDomainHolder instance].userDomain addObserver:self];

    id<UDUserInfoDelegate> userInfo = [UpUserDomainHolder instance].userDomain.user.extraInfo;
    if (userInfo != nil) {
        [[UpUserDomainHolder instance].userDomain removeObserver:self];
        [self updateUserInfoUI:userInfo];
    }
    else {
        [UHProgressHUD showHUDAddedTo:self.view animated:YES];
    }
}

- (void)updateUserInfoUI:(id<UDUserInfoDelegate>)user
{
    dispatch_async(dispatch_get_main_queue(), ^{
      UIImage *avatar = self.avatarView.image;
      [self.avatarView uh_setImageWithURL:[NSURL URLWithString:user.avatarUrl] placeholderImage:avatar];

      self.nicknameLabel.text = user.nickname ?: (user.givenName ?: user.mobile);
      self.authButton.enabled = YES;
    });
}

- (void)onRefeshUserSuccess:(id<UDUserDelegate>)user
{
    [UHProgressHUD hideHUDForView:self.view animated:YES];
    [[UpUserDomainHolder instance].userDomain removeObserver:self];
    [self updateUserInfoUI:user.extraInfo];
}

- (void)onRefeshUserFaild:(id<UDUserDelegate>)user
{
    [UHProgressHUD hideHUDForView:self.view animated:NO];
    [[UpUserDomainHolder instance].userDomain removeObserver:self];
    [[UPToast shareManager] showWithText:@"用户信息获取失败"];
    [self.navigationController popViewControllerAnimated:YES];
    [[UpAuthManager sharedInstance] report:@"Refresh User info failed."];
}

@end
