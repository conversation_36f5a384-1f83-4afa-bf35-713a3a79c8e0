//
//  UpAuthPageCell.m
//  UplusSpecial
//
//  Created by 路标 on 2023/6/12.
//

#import "UpAuthPageCell.h"
#import <UHMasonry/UHMasonry.h>
@interface UpAuthPageCell ()

@property (nonatomic, strong) UILabel *descLabel;

@end

@implementation UpAuthPageCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupSubviews];
    }
    return self;
}

- (void)awakeFromNib
{
    [super awakeFromNib];
    [self setupSubviews];
}

- (void)setupSubviews
{
    self.backgroundColor = [UIColor whiteColor];
    self.contentView.backgroundColor = self.backgroundColor;
    self.separatorInset = UIEdgeInsetsZero;
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    static NSString *name = @"UplusSpecial.bundle/auth_item_dot";
    UIImageView *circlePoint = [[UIImageView alloc] initWithImage:[UIImage imageNamed:name]];
    [self.contentView addSubview:circlePoint];

    self.descLabel = [[UILabel alloc] init];
    self.descLabel.font = [UIFont fontWithName:@"PingFang SC" size:15];
    self.descLabel.textColor = [[UIColor blackColor] colorWithAlphaComponent:0.93];
    self.descLabel.numberOfLines = 0;
    self.descLabel.lineBreakMode = NSLineBreakByWordWrapping;
    [self.contentView addSubview:self.descLabel];

    [circlePoint uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(20));
      make.top.equalTo(@(18));
    }];

    [self.descLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(circlePoint.uh_trailing).offset(8);
      make.trailing.equalTo(@(-20));
      make.top.equalTo(@(12));
    }];
}

- (NSString *)descInfo
{
    return self.descLabel.attributedText.string;
}

- (void)setDescInfo:(NSString *)descInfo
{
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 15;
    style.lineBreakMode = NSLineBreakByWordWrapping;
    NSDictionary *attr = @{
        NSFontAttributeName : self.descLabel.font,
        NSParagraphStyleAttributeName : style,
    };
    self.descLabel.attributedText = [[NSAttributedString alloc] initWithString:descInfo attributes:attr];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated
{
    [super setSelected:selected animated:animated];
}

+ (CGFloat)heightForText:(NSString *)text
{
    CGFloat maxWidth = [UIScreen mainScreen].bounds.size.width - 20 * 2 - 8 - 8;
    UIFont *font = [UIFont fontWithName:@"PingFang SC" size:15];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 8;
    style.lineBreakMode = NSLineBreakByWordWrapping;
    NSDictionary *attr = @{
        NSFontAttributeName : font,
        NSParagraphStyleAttributeName : style,
    };
    CGSize size = [text boundingRectWithSize:CGSizeMake(maxWidth, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:attr context:nil].size;
    return size.height + 12 * 2 /*顶部和底部留白*/;
}

@end
