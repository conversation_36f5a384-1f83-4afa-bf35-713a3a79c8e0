//
//  UpAuthFamilyPickerView.m
//  UplusSpecial
//
//  Created by 路标 on 2023/6/12.
//

#import "UpAuthFamilyPickerView.h"
#import <UHMasonry/UHMasonry.h>
#import <UPTools/UIButton+Factory.h>
#import "UpAuthFamilyPickerCell.h"
#import <upuserdomain/UpUserDomainHolder.h>


@interface UpAuthFamilyPickerView () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, weak) id<UpAuthFamilyPickerViewDelegate> delegate;

@property (nonatomic, strong) NSArray<id<UDFamilyDelegate>> *families;
@property (nonatomic, strong) NSArray<NSNumber *> *selectedFlags;

@property (nonatomic, weak) UIView *contentView;
@property (nonatomic, weak) UILabel *titleLabel;
@property (nonatomic, weak) UIButton *confirmButton;
@property (nonatomic, weak) UITableView *tableView;

@property (nonatomic) CGFloat screenFactor;

@end

@implementation UpAuthFamilyPickerView

- (instancetype)initWithDelegate:(id<UpAuthFamilyPickerViewDelegate>)delegate
{
    if (self = [super initWithFrame:[UIScreen mainScreen].bounds]) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
        self.delegate = delegate;
        self.screenFactor = [UIScreen mainScreen].bounds.size.width / 375.0;
        [self loadFamilies];
        [self setupSubviews];
    }
    return self;
}

- (void)loadFamilies
{
    // 当前家庭就是默认家庭,如果切换当前家庭,defaultFamily也会改变 @zhukaiqi
    id<UDFamilyDelegate> defaultFamily = [UpUserDomainHolder instance].userDomain.user.currentFamily;
    // 如果家庭信息没有刷新完成,这里只用到了家庭名称,和Android保持一致,拼一个默认家庭名称即可
    if (defaultFamily) {
        self.families = @[ defaultFamily ];
        self.selectedFlags = @[ @(YES) ];
    }
    else {
        self.families = @[];
        self.selectedFlags = @[];
    }

    //    NSMutableArray<id<UDFamilyDelegate>> *families = [NSMutableArray array];
    //    NSMutableArray<NSNumber *> *flags = [NSMutableArray array];
    //    [[[UpUserDomainHolder instance].userDomain.user getFamilyList] enumerateObjectsUsingBlock:^(id<UDFamilyDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
    //      // 第一版只显示并选中默认家庭
    //      if (obj.defaultFamily) {
    //          [families addObject:obj];
    //          [flags addObject:@(YES)];
    //          *stop = YES;
    //      }
    //    }];
    //    self.families = families;
    //    self.selectedFlags = flags;
}

- (void)show
{
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    [window addSubview:self];
    [window setNeedsLayout];
    [window layoutIfNeeded];

    [self.contentView uh_remakeConstraints:^(UHConstraintMaker *make) {
      make.bottom.equalTo(self).offset(12);
      make.leading.trailing.equalTo(@(0));
      make.height.equalTo(@(320 * self.screenFactor));
    }];

    [self setNeedsLayout];
    [UIView animateWithDuration:0.35
                     animations:^{
                       [self layoutIfNeeded];
                     }];
}

- (void)dismiss
{
    [self.contentView uh_remakeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(self.uh_bottom);
      make.leading.trailing.equalTo(@(0));
      make.height.equalTo(@(320 * self.screenFactor));
    }];
    [self setNeedsLayout];
    [UIView animateWithDuration:0.35
        animations:^{
          [self layoutIfNeeded];
        }
        completion:^(BOOL finished) {
          [self removeFromSuperview];
        }];
}

- (void)setupSubviews
{
    UIView *content = [[UIView alloc] init];
    content.backgroundColor = [UIColor whiteColor];
    content.layer.cornerRadius = 12;
    [self addSubview:content];
    self.contentView = content;
    [content uh_remakeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(self.uh_bottom);
      make.leading.trailing.equalTo(@(0));
      make.height.equalTo(@(320 * self.screenFactor));
    }];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:17 * self.screenFactor];
    titleLabel.textColor = [[UIColor blackColor] colorWithAlphaComponent:0.8];
    titleLabel.text = @"设备将同步到当前默认家庭";
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [content addSubview:titleLabel];
    [titleLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(@(14));
      make.leading.equalTo(@(16));
      make.trailing.equalTo(@(-16));
    }];
    self.titleLabel = titleLabel;

    UIFont *font = [UIFont fontWithName:@"PingFang SC" size:16 * self.screenFactor];
    UIColor *fontColor = [UIColor colorWithRed:34 / 255.0 green:131 / 255.0 blue:226 / 255.0 alpha:1.0];
    UIColor *bgColor = [UIColor colorWithRed:233 / 255.0 green:240 / 255.0 blue:254 / 255.0 alpha:1.0];

    UIButton *cancel = [UIButton createButtonWithTitle:@"取消" titleColor:fontColor backgroudColor:bgColor font:font];
    cancel.layer.cornerRadius = 40 * self.screenFactor / 2;
    cancel.layer.borderColor = [UIColor colorWithRed:174 / 255.0 green:208 / 255.0 blue:246 / 255.0 alpha:1.0].CGColor;
    cancel.layer.borderWidth = 1.0;
    cancel.layer.masksToBounds = YES;
    [cancel addTarget:self action:@selector(cancelButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [content addSubview:cancel];
    CGFloat buttonWidth = ([UIScreen mainScreen].bounds.size.width - 16 * 2 - 18) / 2;
    [cancel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(@(16));
      make.bottom.equalTo(@(-44 * self.screenFactor - content.layer.cornerRadius));
      make.width.equalTo(@(buttonWidth));
      make.height.equalTo(@(40 * self.screenFactor));
    }];

    bgColor = fontColor;
    fontColor = [UIColor whiteColor];
    UIButton *confirm = [UIButton createButtonWithTitle:@"确定" titleColor:fontColor backgroudColor:bgColor font:font];
    confirm.layer.cornerRadius = cancel.layer.cornerRadius;
    confirm.layer.masksToBounds = YES;
    [confirm addTarget:self action:@selector(confirmButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [content addSubview:confirm];
    [confirm uh_makeConstraints:^(UHConstraintMaker *make) {
      make.leading.equalTo(cancel.uh_trailing).offset(18);
      make.centerY.equalTo(cancel);
      make.width.height.equalTo(cancel);
    }];
    self.confirmButton = confirm;

    [self setupTableView];
}

- (void)setupTableView
{
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    if (@available(iOS 11.0, *)) {
        tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    tableView.contentInset = UIEdgeInsetsMake(12, 0, 0, 0);
    [tableView registerClass:[UpAuthFamilyPickerCell class] forCellReuseIdentifier:@"UpAuthFamilyPickerCell"];
    tableView.delegate = self;
    tableView.dataSource = self;
    [self.contentView addSubview:tableView];
    [tableView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.equalTo(self.titleLabel.uh_bottom).offset(12);
      make.leading.trailing.equalTo(@(0));
      make.bottom.equalTo(self.confirmButton.uh_top).offset(-12);
    }];
    self.tableView = tableView;
}

- (void)cancelButtonTouched:(UIButton *)button
{
    [self.delegate familyPickerViewDidCancel:self];
    [self dismiss];
}

- (void)confirmButtonTouched:(UIButton *)button
{
    [self.selectedFlags enumerateObjectsUsingBlock:^(NSNumber *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.boolValue) {
          [self.delegate familyPickerView:self didPickFamily:self.families[idx]];
          *stop = YES;
      }
    }];
    [self dismiss];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    // 家庭信息获取失败或者还没刷新完成,显示一个默认的家庭
    return self.families.count ?: 1;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 21;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UpAuthFamilyPickerCell *cell = (UpAuthFamilyPickerCell *)[tableView dequeueReusableCellWithIdentifier:@"UpAuthFamilyPickerCell" forIndexPath:indexPath];
    if (self.families.count) {
        cell.familyName = self.families[indexPath.row].info.familyName;
        [cell markSelected:self.selectedFlags[indexPath.row].boolValue];
    }
    else {
        // 能显示当前UI,前面的逻辑保证了用户信息一定刷新完成了
        id<UDUserInfoDelegate> user = [UpUserDomainHolder instance].userDomain.user.extraInfo;
        // 和Android端逻辑保持一致,拼一个默认家庭名称
        cell.familyName = [NSString stringWithFormat:@"%@的默认家庭", user.nickname];
        [cell markSelected:YES];
    }
    return cell;
}

@end
