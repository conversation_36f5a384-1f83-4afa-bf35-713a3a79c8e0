//
//  HTTPRequestManager.m
//  Uplus
//
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights reserved.
//

#import "HTTPRequestManager.h"
//#import "UPTimeStatisticsManager.h"
#import "UPMutableRequest.h"

@implementation HTTPRequestManager
+ (void)postHTTPSRequestWithUrl:(NSString *)url
                        headers:(NSDictionary *)header
                           body:(id)body
                    OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                     completion:(void (^)(NSDictionary *responseDic))completion
                          error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    void (^success)(NSDictionary *responseDic) = completion;
    void (^failure)(NSError *connectError, long responseStatusCode) = onError;
    NSURL *tmp = [NSURL URLWithString:url];
    NSString *baseUrl = tmp.port ? [NSString stringWithFormat:@"%@://%@:%@", tmp.scheme, tmp.host, tmp.port] : [NSString stringWithFormat:@"%@://%@", tmp.scheme, tmp.host];
    NSString *path = [tmp.absoluteString stringByReplacingOccurrencesOfString:baseUrl withString:@""];
    UPMutableRequest *request = [UPMutableRequest requestWithBaseUrl:baseUrl path:path method:UPRequestMethodPOST header:header body:body];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *payload = nil;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          payload = (NSDictionary *)responseObject;
      }
      success(payload);
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          failure(error, error.code);
        }];

    //-----------

    //    [[UPTimeStatisticsManager shareInstance] netStartLoad:url]; //edit by chenruirui  网络开始时间埋点
    //
    //    //  AFHTTPRequestOperationManager *manager = [AFHTTPRequestOperationManager manager];
    //    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    //    manager.requestSerializer = [AFJSONRequestSerializer serializer];
    //    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    //    manager.requestSerializer.timeoutInterval = 15; //edit by 郭发玉 v2.1.0要求超时时间改为15秒
    //    AFSecurityPolicy *securityPolicy = [AFSecurityPolicy defaultPolicy];
    //    securityPolicy.allowInvalidCertificates = YES;
    //    securityPolicy.validatesDomainName = NO;
    //    manager.securityPolicy = securityPolicy;
    //
    //    [manager.requestSerializer setValue:@"application/json;charset=UTF-8" forHTTPHeaderField:@"Content-Type"];
    //
    //    //add by liujian to use af 3.0
    //    [self setHTTPHeaderField:header AFHTTPSessionManager:manager];
    //    [manager POST:url
    //        parameters:body
    //        progress:nil
    //        success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          NSDictionary *dic = nil;
    //          if ([responseObject isKindOfClass:[NSDictionary class]]) {
    //              dic = (NSDictionary *)responseObject;
    //          }
    //          completion(dic);
    //          //edit by chenruirui  时间埋点
    //          [[UPTimeStatisticsManager shareInstance] netStaticsDotWithUrl:url];
    //        }
    //        failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          onError(error, ((NSHTTPURLResponse *)task.response).statusCode);
    //        }];
}

+ (void)postRequestWithUrl:(NSString *)url
                   headers:(NSDictionary *)header
                      body:(id)body
               OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                completion:(void (^)(NSDictionary *responseDic))completion
                     error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    [self postHTTPSRequestWithUrl:url headers:header body:body OnOperation:onOperation completion:completion error:onError];
    //    [[UPTimeStatisticsManager shareInstance] netStartLoad:url]; //edit by chenruirui  网络开始时间埋点
    //
    //    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    //    manager.requestSerializer = [AFJSONRequestSerializer serializer];
    //    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    //    manager.requestSerializer.timeoutInterval = 15; //edit by 郭发玉 v2.1.0要求超时时间改为15秒
    //    [manager.requestSerializer setValue:@"application/json;charset=UTF-8" forHTTPHeaderField:@"Content-Type"];
    //    [self setHTTPHeaderField:header AFHTTPSessionManager:manager];
    //    [manager POST:url
    //        parameters:body
    //        progress:nil
    //        success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          NSDictionary *dic = nil;
    //          if ([responseObject isKindOfClass:[NSDictionary class]]) {
    //              dic = (NSDictionary *)responseObject;
    //          }
    //          completion(dic);
    //          //edit by chenruirui  时间埋点
    //          [[UPTimeStatisticsManager shareInstance] netStaticsDotWithUrl:url];
    //        }
    //        failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          onError(error, ((NSHTTPURLResponse *)task.response).statusCode);
    //        }];
}

+ (void)getHTTPSRequestWithUrl:(NSString *)url
                       headers:(NSDictionary *)header
                    parameters:(id)parameters
                   OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                    completion:(void (^)(NSDictionary *responseDic))completion
                         error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    void (^success)(NSDictionary *responseDic) = completion;
    void (^failure)(NSError *connectError, long responseStatusCode) = onError;


    NSURL *tmp = [NSURL URLWithString:url];

    NSString *baseUrl = tmp.port ? [NSString stringWithFormat:@"%@://%@:%@", tmp.scheme, tmp.host, tmp.port] : [NSString stringWithFormat:@"%@://%@", tmp.scheme, tmp.host];
    NSString *path = [tmp.absoluteString stringByReplacingOccurrencesOfString:baseUrl withString:@""];
    UPMutableRequest *request = [UPMutableRequest requestWithBaseUrl:baseUrl path:path method:UPRequestMethodGET header:header body:parameters];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *payload = nil;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          payload = (NSDictionary *)responseObject;
      }
      success(payload);
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          failure(error, error.code);
        }];

    //    [[UPTimeStatisticsManager shareInstance] netStartLoad:url]; //edit by chenruirui  网络开始时间埋点
    //
    //    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    //    manager.requestSerializer = [AFJSONRequestSerializer serializer];
    //    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    //    manager.requestSerializer.timeoutInterval = 15; //edit by 郭发玉 v2.1.0要求超时时间改为15秒
    //    AFSecurityPolicy *securityPolicy = [AFSecurityPolicy defaultPolicy];
    //    securityPolicy.allowInvalidCertificates = YES;
    //    securityPolicy.validatesDomainName = NO;
    //    manager.securityPolicy = securityPolicy;
    //
    //    [manager.requestSerializer setValue:@"application/json;charset=UTF-8" forHTTPHeaderField:@"Content-Type"];
    //
    //    //add by liujian to use af 3.0
    //    [self setHTTPHeaderField:header AFHTTPSessionManager:manager];
    //
    //    [manager GET:url
    //        parameters:parameters
    //        progress:nil
    //        success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          NSDictionary *dic = nil;
    //          if ([responseObject isKindOfClass:[NSDictionary class]]) {
    //              dic = (NSDictionary *)responseObject;
    //          }
    //          completion(dic);
    //          //edit by chenruirui  时间埋点
    //          [[UPTimeStatisticsManager shareInstance] netStaticsDotWithUrl:url];
    //        }
    //        failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          onError(error, ((NSHTTPURLResponse *)task.response).statusCode);
    //        }];
}

+ (void)getRequestWithUrl:(NSString *)url
                  headers:(NSDictionary *)header
               parameters:(id)parameters
              OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
               completion:(void (^)(NSDictionary *responseDic))completion
                    error:(void (^)(NSError *connectError, long responseStatusCode))onError
{
    [self getHTTPSRequestWithUrl:url headers:header parameters:parameters OnOperation:onOperation completion:completion error:onError];
    //    [[UPTimeStatisticsManager shareInstance] netStartLoad:url]; //edit by chenruirui  网络开始时间埋点
    //
    //    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    //    manager.requestSerializer = [AFJSONRequestSerializer serializer];
    //    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    //    manager.requestSerializer.timeoutInterval = 15; //edit by 郭发玉 v2.1.0要求超时时间改为15秒
    //    [manager.requestSerializer setValue:@"application/json;charset=UTF-8" forHTTPHeaderField:@"Content-Type"];
    //    // add by liujian to af 3.0
    //    [self setHTTPHeaderField:header AFHTTPSessionManager:manager];
    //    [manager GET:url
    //        parameters:parameters
    //        progress:nil
    //        success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          NSDictionary *dic = nil;
    //          if ([responseObject isKindOfClass:[NSDictionary class]]) {
    //              dic = (NSDictionary *)responseObject;
    //          }
    //          completion(dic);
    //          //edit by chenruirui  时间埋点
    //          [[UPTimeStatisticsManager shareInstance] netStaticsDotWithUrl:url];
    //        }
    //        failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          onError(error, ((NSHTTPURLResponse *)task.response).statusCode);
    //        }];
}

+ (void)deleteHTTPSRequestWithUrl:(NSString *)url
                          headers:(NSDictionary *)header
                       parameters:(NSDictionary *)parameters
                      OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                       completion:(void (^)(NSDictionary *))completion
                            error:(void (^)(NSError *, long))onError
{
    void (^success)(NSDictionary *responseDic) = completion;
    void (^failure)(NSError *connectError, long responseStatusCode) = onError;
    NSURL *tmp = [NSURL URLWithString:url];
    NSString *baseUrl = tmp.port ? [NSString stringWithFormat:@"%@://%@:%@", tmp.scheme, tmp.host, tmp.port] : [NSString stringWithFormat:@"%@://%@", tmp.scheme, tmp.host];
    NSString *path = [tmp.absoluteString stringByReplacingOccurrencesOfString:baseUrl withString:@""];
    UPMutableRequest *request = [UPMutableRequest requestWithBaseUrl:baseUrl path:path method:UPRequestMethodDELETE header:header body:parameters];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *payload = nil;
      if ([responseObject isKindOfClass:NSDictionary.class]) {
          payload = (NSDictionary *)responseObject;
      }
      success(payload);
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          failure(error, error.code);
        }];
    //    [[UPTimeStatisticsManager shareInstance] netStartLoad:url]; //edit by chenruirui  网络开始时间埋点
    //
    //    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    //    manager.requestSerializer = [AFJSONRequestSerializer serializer];
    //    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    //    manager.requestSerializer.timeoutInterval = 15; //edit by 郭发玉 v2.1.0要求超时时间改为15秒
    //    AFSecurityPolicy *securityPolicy = [AFSecurityPolicy defaultPolicy];
    //    securityPolicy.allowInvalidCertificates = YES;
    //    securityPolicy.validatesDomainName = NO;
    //    manager.securityPolicy = securityPolicy;
    //
    //    [manager.requestSerializer setValue:@"application/json;charset=UTF-8" forHTTPHeaderField:@"Content-Type"];
    //
    //    //add by liujian to use af 3.0
    //    [self setHTTPHeaderField:header AFHTTPSessionManager:manager];
    //
    //    [manager DELETE:url
    //        parameters:parameters
    //        success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          NSDictionary *dic = nil;
    //          if ([responseObject isKindOfClass:[NSDictionary class]]) {
    //              dic = (NSDictionary *)responseObject;
    //          }
    //          completion(dic);
    //          //edit by chenruirui  时间埋点
    //          [[UPTimeStatisticsManager shareInstance] netStaticsDotWithUrl:url];
    //        }
    //        failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
    //          onOperation((AFHTTPRequestOperation *)task);
    //          onError(error, ((NSHTTPURLResponse *)task.response).statusCode);
    //        }];
}

@end
