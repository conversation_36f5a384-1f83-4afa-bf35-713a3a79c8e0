//
//  UPMutableRequest.h
//  mainbox
//
//  Created by 张虎 on 2020/6/3.
//

#import <upnetwork/UPNetwork.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMutableRequest : UPRequest

@property (nonatomic) NSTimeInterval timeoutInterval;
@property (nonatomic) NSUInteger retryTimes;
@property (nonatomic) NSTimeInterval retryDelay;
@property (nonatomic) UPRequestSerializerType requestSerializerType;
@property (nonatomic) UPResponseSerializerType responseSerializerType;
@property (nonatomic) NSDictionary<NSString *, NSString *> *requestHeaders;
@property (nonatomic) NSObject *requestBody;
@property (nonatomic) id<UPResponseParser> responseParser;
@property (nonatomic) NSArray<NSString *> *authorization;
@property (nonatomic, copy) UPRequestConstructingBodyBlock constructingBodyBlock;
@property (nonatomic, copy) UPRequestProgressBlock progressBlock;
@property (nonatomic) BOOL isHTTPDnsEnabled;

+ (instancetype)requestWithBaseUrl:(NSString *)baseUrl
                              path:(NSString *)path
                            method:(UPRequestMethod)method
                            header:(nullable NSDictionary<NSString *, NSString *> *)header
                              body:(nullable id)body;

@end

NS_ASSUME_NONNULL_END
