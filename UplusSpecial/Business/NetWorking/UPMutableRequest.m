//
//  UPMutableRequest.m
//  mainbox
//
//  Created by 张虎 on 2020/6/3.
//

#import "UPMutableRequest.h"

@interface UPMutableRequest () {
    @private
    NSString *_baseURL_MR;
    NSString *_path_MR;
    UPRequestMethod _method_MR;
    NSTimeInterval _timeoutInterval_MR;
    NSUInteger _retryTimes_MR;
    NSTimeInterval _retryDelay_MR;
    UPRequestSerializerType _requestSerializerType_MR;
    UPResponseSerializerType _responseSerializerType_MR;
    NSDictionary<NSString *, NSString *> *_requestHeaders_MR;
    NSObject *_requestBody_MR;
    id<UPResponseParser> _responseParser_MR;
    NSArray<NSString *> *_authorization_MR;
    UPRequestConstructingBodyBlock _constructingBodyBlock_MR;
    UPRequestProgressBlock _progressBlock_MR;
    BOOL _isHTTPDnsEnabled_MR;
}

@end

@implementation UPMutableRequest
@dynamic timeoutInterval;
@dynamic retryTimes;
@dynamic retryDelay;
@dynamic requestSerializerType;
@dynamic responseSerializerType;
@dynamic requestHeaders;
@dynamic requestBody;
@dynamic responseParser;
@dynamic authorization;
@dynamic constructingBodyBlock;
@dynamic progressBlock;
@dynamic isHTTPDnsEnabled;

+ (instancetype)requestWithBaseUrl:(NSString *)baseUrl
                              path:(NSString *)path
                            method:(UPRequestMethod)method
                            header:(NSDictionary<NSString *, NSString *> *)header
                              body:(id)body
{
    UPMutableRequest *req = [[self alloc] initWithBaseUrl:baseUrl path:path method:method header:header body:body];
    return req;
}

- (instancetype)initWithBaseUrl:(NSString *)baseUrl
                           path:(NSString *)path
                         method:(UPRequestMethod)method
                         header:(NSDictionary<NSString *, NSString *> *)header
                           body:(id)body
{
    self = [super init];
    if (self) {
        _baseURL_MR = baseUrl;
        _path_MR = path;
        _method_MR = method;
        _requestHeaders_MR = header;
        _requestBody_MR = body;

        _timeoutInterval_MR = [super timeoutInterval];
        _retryTimes_MR = [super retryTimes];
        _retryDelay_MR = [super retryDelay];
        _requestSerializerType_MR = [super requestSerializerType];
        _responseSerializerType_MR = [super responseSerializerType];
        _responseParser_MR = [super responseParser];
        _authorization_MR = [super authorization];
        _isHTTPDnsEnabled_MR = [super isHTTPDnsEnabled];
    }
    return self;
}

- (void)startRequestWithSuccess:(UPRequestSuccess)success failure:(UPRequestFailure)failure
{
    [super startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      if (!success) {
          return;
      }
      if ([NSThread isMainThread]) {
          success(responseObject);
      }
      else {
          dispatch_async(dispatch_get_main_queue(), ^{
            success(responseObject);
          });
      }
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          if (!failure) {
              return;
          }
          if ([NSThread isMainThread]) {
              failure(error, info);
          }
          else {
              dispatch_async(dispatch_get_main_queue(), ^{
                failure(error, info);
              });
          }
        }];
}

#pragma mark - Accessors

- (NSString *)baseURL
{
    return _baseURL_MR;
}

- (NSString *)path
{
    return _path_MR;
}

- (UPRequestMethod)method
{
    return _method_MR;
}

- (void)setTimeoutInterval:(NSTimeInterval)timeoutInterval
{
    _timeoutInterval_MR = timeoutInterval;
}

- (NSTimeInterval)timeoutInterval
{
    return _timeoutInterval_MR;
}

- (void)setRetryTimes:(NSUInteger)retryTimes
{
    _retryTimes_MR = retryTimes;
}

- (NSUInteger)retryTimes
{
    return _retryTimes_MR;
}

- (void)setRetryDelay:(NSTimeInterval)retryDelay
{
    _retryDelay_MR = retryDelay;
}

- (NSTimeInterval)retryDelay
{
    return _retryDelay_MR;
}

- (void)setRequestSerializerType:(UPRequestSerializerType)requestSerializerType
{
    _requestSerializerType_MR = requestSerializerType;
}

- (UPRequestSerializerType)requestSerializerType
{
    return _requestSerializerType_MR;
}

- (void)setResponseSerializerType:(UPResponseSerializerType)responseSerializerType
{
    _responseSerializerType_MR = responseSerializerType;
}

- (UPResponseSerializerType)responseSerializerType
{
    return _responseSerializerType_MR;
}

- (void)setRequestHeaders:(NSDictionary<NSString *, NSString *> *)requestHeaders
{
    _requestHeaders_MR = requestHeaders;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary<NSString *, NSString *> *result = [NSMutableDictionary dictionary];
    [_requestHeaders_MR enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSString *_Nonnull obj, BOOL *_Nonnull stop) {
      if (obj.length) {
          result[key] = obj;
      }
    }];
    if (!result.count) {
        result[@"Content-Type"] = @"application/json;charset=utf-8";
    }
    return result.copy;
}

- (void)setRequestBody:(NSObject *)requestBody
{
    _requestBody_MR = requestBody;
}

- (NSObject *)requestBody
{
    return _requestBody_MR;
}

- (void)setResponseParser:(id<UPResponseParser>)responseParser
{
    _responseParser_MR = responseParser;
}

- (id<UPResponseParser>)responseParser
{
    return _responseParser_MR;
}

- (void)setAuthorization:(NSArray<NSString *> *)authorization
{
    _authorization_MR = authorization;
}

- (NSArray<NSString *> *)authorization
{
    return _authorization_MR;
}

- (void)setConstructingBodyBlock:(UPRequestConstructingBodyBlock)constructingBodyBlock
{
    _constructingBodyBlock_MR = [constructingBodyBlock copy];
}

- (UPRequestConstructingBodyBlock)constructingBodyBlock
{
    return _constructingBodyBlock_MR;
}

- (void)setProgressBlock:(UPRequestProgressBlock)progressBlock
{
    _progressBlock_MR = [progressBlock copy];
}

- (UPRequestProgressBlock)progressBlock
{
    return _progressBlock_MR;
}

- (void)setIsHTTPDnsEnabled:(BOOL)isHTTPDnsEnabled
{
    _isHTTPDnsEnabled_MR = isHTTPDnsEnabled;
}

- (BOOL)isHTTPDnsEnabled
{
    return _isHTTPDnsEnabled_MR;
}

@end
