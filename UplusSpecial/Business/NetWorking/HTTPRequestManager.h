//
//  HTTPRequestManager.h
//  Uplus
//
//  Created by 郑振兴 on 15/1/5.
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AFHTTPRequestOperation.h"

@interface HTTPRequestManager : NSObject

+ (void)postHTTPSRequestWithUrl:(NSString *)url
                        headers:(NSDictionary *)header
                           body:(id)body
                    OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                     completion:(void (^)(NSDictionary *responseDic))completion
                          error:(void (^)(NSError *connectError, long responseStatusCode))onError;

+ (void)postRequestWithUrl:(NSString *)url
                   headers:(NSDictionary *)header
                      body:(id)body
               OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                completion:(void (^)(NSDictionary *responseDic))completion
                     error:(void (^)(NSError *connectError, long responseStatusCode))onError;

+ (void)getHTTPSRequestWithUrl:(NSString *)url
                       headers:(NSDictionary *)header
                    parameters:(id)parameters
                   OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                    completion:(void (^)(NSDictionary *responseDic))completion
                         error:(void (^)(NSError *connectError, long responseStatusCode))onError;

+ (void)getRequestWithUrl:(NSString *)url
                  headers:(NSDictionary *)header
               parameters:(id)parameters
              OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
               completion:(void (^)(NSDictionary *responseDic))completion
                    error:(void (^)(NSError *connectError, long responseStatusCode))onError;

+ (void)deleteHTTPSRequestWithUrl:(NSString *)url
                          headers:(NSDictionary *)header
                       parameters:(NSDictionary *)parameters
                      OnOperation:(void (^)(AFHTTPRequestOperation *onOperation))onOperation
                       completion:(void (^)(NSDictionary *responseDic))completion
                            error:(void (^)(NSError *error, long responseStatusCode))onError;

@end
