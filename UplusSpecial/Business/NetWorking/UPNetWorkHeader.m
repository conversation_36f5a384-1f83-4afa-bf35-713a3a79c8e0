//
//  UplusGlobal.m
//  Uplus
//
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights reserved.
//
#import "UPNetWorkHeader.h"
#import <upnetwork/UPNetworkSettings.h>
#import <UPTools/EncryptUserDefault.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <upnetwork/UPNetwork.h>

@implementation UPNetWorkHeader
NSString *const UNAppId = @"appId";
NSString *const UNAppVersion = @"appVersion";
NSString *const UNAppKey = @"appKey";
NSString *const UNClientId = @"clientId";
NSString *const UNSequenceId = @"sequenceId";
NSString *const UNAccessToken = @"accessToken";
NSString *const UNSign = @"sign";
NSString *const kUNUserID = @"kUNUserID";

+ (UPNetWorkHeader *)defaultHeader
{
    static UPNetWorkHeader *sharedInstance = nil;
    static dispatch_once_t oncePredicate;

    dispatch_once(&oncePredicate, ^{
      sharedInstance = [[UPNetWorkHeader alloc] init];
    });

    return sharedInstance;
}

- (id)init
{
    self = [super init];
    if (self) {
        self.clientId = [UPNetworkSettings sharedSettings].clientID;
        //        self.accessToken = [EncryptUserDefault valueForKey:UNAccessToken];
        _userID = [EncryptUserDefault valueForKey:kUNUserID];
    }

    return self;
}

- (NSString *)accessToken
{
    if (![UpUserDomainHolder instance].userDomain.oauthData.uhome_access_token) {
        return @"";
    }
    return [UpUserDomainHolder instance].userDomain.oauthData.uhome_access_token;
}


- (void)setUserID:(NSString *)userID
{
    NSString *userIDStorage = [userID isKindOfClass:[NSString class]] ? userID : @"";
    if ([_userID isEqualToString:userIDStorage]) {
        return;
    }
    _userID = userIDStorage;
    [EncryptUserDefault setObject:_userID forKey:kUNUserID];
}

- (NSString *)sequenceId
{
    NSDate *nowDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyyMMddHHmmssSSS";
    NSString *dateString = [dateFormatter stringFromDate:nowDate];
    NSString *sequenceidStr = [NSString stringWithFormat:@"%@000001", dateString];
    return sequenceidStr;
}

+ (NSString *)languageCode
{
    NSString *languageCode = [[NSUserDefaults standardUserDefaults] objectForKey:@"langeuageset"];
    if (languageCode) {
        return languageCode;
    }

    return @"en";
}

+ (NSDictionary *)getOpenApiHttpHeader:(NSDictionary *)bodyDictionary
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *sign = [UPCommonServerHeader signResultWithBody:bodyDictionary timestamp:header.timestamp].sign;

    return @{ UNAppId : header.appId,
              UNSequenceId : header.sequenceId,
              UNClientId : header.clientId,
              UNAppKey : header.appKey,
              UNAccessToken : header.accessToken,
              UNAppVersion : header.appVersion,
              UNSign : sign,
              @"timestamp" : header.timestamp,
              @"language" : [self languageCode],
              @"timezone" : @"1" };
}

#pragma mark 新版需要验证的app server header
+ (NSDictionary *)getInternationalAPPserverHttpHeader:(NSString *)url bodyDictionary:(NSDictionary *)bodyDictionary
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *sign = [UPCommonServerHeader signResultWithBody:bodyDictionary UrlString:url timestamp:header.timestamp].sign;

    return @{ UNAppId : header.appId,
              @"timestamp" : header.timestamp,
              UNClientId : header.clientId,
              UNAppKey : header.appKey,
              UNAccessToken : header.accessToken,
              UNAppVersion : header.appVersion,
              UNSign : sign,
              @"Accept-Language" : [self languageCode]
    };
}

+ (NSDictionary *)getUWSAppServerHttpHeaderWithUrl:(NSString *)url bodyDictionary:(NSDictionary *)bodyDictionary
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *appId = header.appId;
    NSString *sequenceId = header.sequenceId;
    NSString *clientId = header.clientId;
    NSString *accessToken = header.accessToken;
    NSString *appversion = header.appVersion;
    NSString *language = @"zh-cn";
    NSString *timeZone = @"8";
    NSString *timeStamp = header.timestamp;
    NSString *sign = [UPCommonServerHeader signResultWithBody:bodyDictionary UrlString:url timestamp:timeStamp].sign;

    return @{ UNAppId : appId,
              UNClientId : clientId,
              UNAppVersion : appversion,
              UNAccessToken : accessToken,
              @"timestamp" : timeStamp,
              @"timezone" : timeZone,
              @"language" : language,
              UNSequenceId : sequenceId,
              UNSign : sign };
}

+ (NSDictionary *)getAPPserverHttpHeader
{
    UPCommonServerHeader *header = UPCommonServerHeader.new;

    NSString *appId = header.appId;
    NSString *sequenceId = header.sequenceId;
    NSString *clientId = header.clientId;
    NSString *appKey = header.appKey;
    NSString *accessToken = header.accessToken;
    NSString *appversion = header.appVersion;

    return @{UNAppId : appId,
             UNSequenceId : sequenceId,
             UNClientId : clientId,
             UNAppKey : appKey,
             UNAccessToken : accessToken,
             UNAppVersion : appversion};
}

+ (NSDictionary *)getNewAPPserverHttpHeader:(NSDictionary *)bodyDictionary
{
    return [UPCommonServerHeader signHeaderWithBody:bodyDictionary];
    ;
}

@end
