//
//  UPServiceConstant.h
//  UplusSpecial
//
//  Created by 路标 on 2023/12/27.
//

#ifndef UPServiceConstant_h
#define UPServiceConstant_h

extern NSString *const XY_MODULE_NAME;

#define SERVICE_STORAGE_KEY @"up_float_online_service_param"

//冒泡开关
#define SERVICE_BUBBLE_STORAGE_KEY @"key_uplus_xiaou_smart_helper_open"
#define SERVICE_BUBBLE_STORAGE_OBSERVE_KEY @"native_bubble"
#define SERVICE_BUBBLE_CONTENT_KEY @"bubbleContent"
#define SERVICE_BUBBLE_DURATION_KEY @"bubbleTime"
#define SERVICE_BUBBLE_CARD_TYPE_KEY @"cardType"
// 耗材冒泡消息
#define SERVICE_BUBBLE_CARD_CONSUMABLE @"NEWXIAOU_CONSUMABLE_MSG"
// 故障冒泡消息
#define SERVICE_BUBBLE_CARD_FAULT @"NEWXIAOU_FAULT_MSG"

//虚拟人开关
#define SERVICE_VIRTUAL_STORAGE_KEY @"_virtualman/switch"

#define SERVICE_MAIN_PAGE_SCROLL_KEY @"main_tab_page_scrolling"
#define SERVICE_MAIN_TAB_SWITCH_KEY @"flutter_package_maintab_switch"
#define SERVICE_MOURNING_ENABLE_KEY @"uplus_mourning_mode_status"
#define SERVICE_XIAOU_ALERT_KEY @"xiaou_alert_showing"
#define SERVICE_XIAOU_BADGE_KEY @"xiaou_badge_value"
#define SERVICE_XIAOU_THEME_KEY @"currentTheme"

#define SCREEN_SCALE_375 ([UIScreen mainScreen].bounds.size.width / 375)

// 小优图标和冒泡距离屏幕边缘距离
#define XY_FLOAT_SCREEN_EDGE (12 * SCREEN_SCALE_375)

// 主页面Tab高度 + 小优下边缘与Tab上边缘的间距
#define XY_MAIN_TABBAR_HEIGHT ((49 + 10) * SCREEN_SCALE_375)

// 小优图标大小
extern CGFloat XY_FLOAT_SIZE;

// 冒泡文案label的最大高度
extern CGFloat XY_CONTENT_HEIGHT;

// 冒泡内边距
#define XY_BACKBUTTON_MARGIN (12 * SCREEN_SCALE_375)

// 冒泡label和小优图标的间距
#define XY_CONTENT_MARGIN (8 * SCREEN_SCALE_375)

// 小红点宽高
#define XY_BADGE_VIEW_SIZE (20 * SCREEN_SCALE_375)

typedef NS_ENUM(NSInteger, UPServiceTheme) {
    UPServiceThemeDefault, // 默认
    UPServiceThemeNewYear = 3, // 新年主题
};

#endif /* UPServiceConstant_h */
