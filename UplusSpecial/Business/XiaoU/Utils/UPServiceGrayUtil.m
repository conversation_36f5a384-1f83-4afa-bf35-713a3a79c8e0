//
//  UPServiceGrayUtil.m
//  UplusSpecial
//
//  Created by 路标 on 2024/1/5.
//

#import "UPServiceGrayUtil.h"
#import <UPStorage/UPStorage.h>
#import "UPServiceConstant.h"

NSString *const XIAOU_BUNDLE = @"UplusSpecial.bundle";

@implementation UIImage (ServiceGray)

+ (UIImage *)serviceImageNamed:(NSString *)name
{
    NSString *imgName = [NSString stringWithFormat:@"%@/%@", XIAOU_BUNDLE, name];
    UIImage *img = [UIImage imageNamed:imgName];
    //    BOOL isGray = [UPStorage getBooleanValue:SERVICE_MOURNING_ENABLE_KEY defaultValue:false];
    //    if (isGray) {
    //        img = [img grayImage];
    //    }
    return img;
}

//- (UIImage *)grayImage
//{
//    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceGray();
//    CGImageRef cgImg = self.CGImage;
//    CGFloat width = CGImageGetWidth(cgImg);
//    CGFloat height = CGImageGetHeight(cgImg);
//    CGContextRef context = CGBitmapContextCreate(NULL, width, height, 8, 0, colorSpace, CGImageGetBitmapInfo(cgImg));
//    if (!context) {
//        return self;
//    }
//    CGContextDrawImage(context, CGRectMake(0, 0, width, height), cgImg);
//    cgImg = CGBitmapContextCreateImage(context);
//    if (!cgImg) {
//        return self;
//    }
//    return [UIImage imageWithCGImage:cgImg scale:self.scale orientation:self.imageOrientation];
//}

@end


@implementation UIColor (ServiceGray)

UIColor *serviceColorWithRGBA(NSInteger red, NSInteger green, NSInteger blue, CGFloat alpha)
{
    CGFloat r = red / 255.0;
    CGFloat g = green / 255.0;
    CGFloat b = blue / 255.0;
    //    BOOL isGray = [UPStorage getBooleanValue:SERVICE_MOURNING_ENABLE_KEY defaultValue:false];
    //    if (isGray) {
    //        CGFloat gray = (0.299 * r + 0.587 * g + 0.114 * b);
    //        return [UIColor colorWithRed:gray green:gray blue:gray alpha:alpha];
    //    }
    return [UIColor colorWithRed:r green:g blue:b alpha:alpha];
}

UIColor *serviceColorWithRGB(NSInteger red, NSInteger green, NSInteger blue)
{
    return serviceColorWithRGBA(red, green, blue, 1.0);
}

@end
