//
//  UPServicePageUtil.swift
//  UplusSpecial
//
//  Created by 路标 on 2024/1/3.
//

import Foundation
import UIKit
import UPVDN
import AFNetworking
import UPTools
import UPUserDomain
import UPCore
import UpVdnModule
//import AVFoundation
import Dispatch
//import UpPermissionManager
import uplog

@objc public class UPServicePageUtil: NSObject {
    /// 小优助手URL
    @objc public static let serviceBaseURL = "flutter://voiceAssistantPage"

    private static var currentController: UIViewController {
        UpVdnUtils.getCurrentViewController()
    }
    
    /// 判断App当前是否处于主Tab页面(一级页面)
//    @objc public static var isCurrentMainPage: Bool {
//        currentController.originalURL?.starts(with: "flutter://index.html") ?? false
//    }

    /// 获取跳转到小优的URL。
    /// - Returns: 打开Flutter小优透明容器的URL
    ///
    /// 该方法会根据预置的`HtmlPageParameters.plist`和`NativePageParameters.plist`
    /// 文件中的配置将参数拼接到URL中。如果当前在网器详情页,会将deviceId拼接到URL中。
    @objc public static func xiaouServiceURL() -> URL {
        let params = getServicePresetParams()
        guard var components = URLComponents(string: serviceBaseURL) else {
            return URL(string: serviceBaseURL)!
        }

        if !params.isEmpty && components.queryItems == nil {
            components.queryItems = []
        }

        params.forEach { (key: String, value: String) in
            components.queryItems?.append(
                URLQueryItem(name: key, value: value)
            )
        }
        return components.url!
    }
    
    /// 当前页面的打点URL。点击小优入口时打点信息的"页面来源（value）"的值,不带参数和fragment.
    ///
    /// 如果originalURL不为空,值为originalURL去掉参数和fragment的结果;
    /// 否则从realURL中截取包名并拼接:mpaas://packageName
    @objc public static var trackURL: String {
        let controller = currentController
        var url: String
        if let orgURL = controller.originalURL, !orgURL.isEmpty {
            url = orgURL
        } else if let realUrl = controller.realURL, !realUrl.isEmpty {
            url = realUrl
        } else {
            url = NSStringFromClass(controller.classForCoder)
            return "native://\(url)"
        }
        
        guard let components = URLComponents(string: url) else { return url }
        
        var trackValue = url
        if components.scheme != "file" {
            trackValue = "\(components.scheme!)://\(components.host ?? "")\(components.path)"
            return trackValue
        }
        
        do {
            let regex = try NSRegularExpression(pattern: "/([^/]+)@", options: .caseInsensitive)
            let matchRange = regex.rangeOfFirstMatch(in: url, options: [], range: NSRange(location: 0, length: url.utf16.count))
            if matchRange.location == NSNotFound {
                return url
            }
            
            // 匹配以"/"开始以"@"结尾 删除/和@
            var capturedGroup = (url as NSString).substring(with: matchRange)
            capturedGroup = capturedGroup.replacingOccurrences(of: "/", with: "")
            capturedGroup = capturedGroup.replacingOccurrences(of: "@", with: "")
            trackValue = "mpaas://\(capturedGroup)"
            return trackValue

        } catch {
            uplog.UPPrintError(moduleName: "UplusSpecial", message: "[xiaou]|parse trackURL error: \(error.localizedDescription)")
            return url
        }
    }
    
    private static func getServicePresetParams() -> [String: String] {
        let controller = currentController
        guard let originalURL = controller.originalURL else {
            return [:]
        }
        
        var params: [String: String] = [:]
//        if let htmlParams = getHtmlParams(for: originalURL) {
//            params = htmlParams
//        } else if let nativeParams = getNativeParams(for: originalURL) {
//            params = nativeParams
//        }
        
        // 如果当前在网器详情页,拼接deviceId给Flutter小优
        if UPServiceEnableManager.isDeviceDetailPage(originalURL, params: controller.parameters) {
            params["deviceId"] = (controller.parameters["deviceId"] as! String)
        }
        return params
    }
    
//    private static func getHtmlParams(for url: String) -> [String: String]? {
//        return getPresetParams(for: url, resourcePath: "UplusSpecial.bundle/HtmlPageParameters")
//    }
//    
//    private static func getNativeParams(for url: String) -> [String: String]? {
//        return getPresetParams(for: url, resourcePath: "UplusSpecial.bundle/NativePageParameters")
//    }
//    
//    private static func getPresetParams(for url: String, resourcePath: String) -> [String: String]? {
//        guard let plistURL = Bundle.main.url(forResource: resourcePath, withExtension: "plist") else {
//            return nil
//        }
//        guard let data = try? Data(contentsOf: plistURL) else { return nil }
//        
//        let presetInfo: [String: Any] = (try? PropertyListSerialization.propertyList(from: data, format: nil) as? [String: Any]) ?? [:]
//        
//        for pageURL in presetInfo.keys {
//            if url.contains(pageURL) {
//                return presetInfo[pageURL] as? [String : String]
//            }
//        }
//        return nil
//    }
    
    /// 判断当前是否有网络。
    /// - Returns: 有网返回true, 没网返回false
    @objc public static func isReachable() -> Bool {
        return AFNetworkReachabilityManager.shared().isReachable
    }
    
    /// 检查用户登录状态。
    /// - Returns: 已登录返回true, 否则返回false
    ///
    /// 如果未登录,会自动跳转登录页。
    @objc public static func checkLogin() -> Bool {
        if UpUserDomainHolder.instance().userDomain.state() != .didLogin {
            gotoLogin()
            return false
        }
        
        return true
    }
    
    /// 检查网络状态。
    /// - Returns: 有网返回true, 没网返回false
    ///
    /// 如果没网, 会自动弹出toast提示"网络不可用"。
    @objc public static func checkNetwork() -> Bool {
        let isReachable = isReachable()
        if !isReachable {
            KVNProgressShow.showText("网络不可用")
        }
        return isReachable
    }
    
    /// 拉起登录页面
    @objc public static func gotoLogin() {
        let vdn = UPCore.sharedInstance().createService(UpVdnModuleServiceProtocol.self) as? UpVdnModuleServiceProtocol
        let params = ["hidesBottomBarWhenPushed": "1", "checkGuestMode": "1"];
        vdn?.go(toPage: "mpaas://usercenter", flag: .present, parameters: params, complete: { dict in
        }, error: { error in
        })
    }
    
    /// 点击悬浮按钮和冒泡跳转页面前检查网络和登录状态
    /// - Returns: 有网&已登录状态返回true,否则返回false
    ///
    /// 先检查网络,如果没网,弹出Toast提示,并返回false.
    /// 然后检查登录状态,如果未登录,会拉起登录页面并返回false.
    /// 在拉起登录页时会自动检查隐私协议,如果没有同意隐私协议,会先弹出隐私协议弹窗,同意后跳转登录页面
    /// 有网并且已登录时返回true.
    @objc public static func checkServiceValidStatus() -> Bool {
        return checkNetwork() && checkLogin()
    }
    
    /// 申请麦克风权限, 并在取得权限后执行闭包(回调)
    /// - Parameters:
    ///     - operation: 取得麦克风权限后要执行的闭包(回调)
    ///
    /// 如果已获得授权，会直接执行闭包;如果权限之前已被拒绝或权限受限,会弹窗引导到设置页面;
    /// 如果没授权过,会弹出权限申请弹框,并在授权后执行闭包, 如果拒绝权限则什么也不做
//    @objc public static func requestMicrophoneAuthThenDo(_ operation: ((Bool) -> Void)?) {
//        // swift 无法使用OC的宏定义: `#define UpPermissionMicrophone @"microphone"` @UpPermissionMacro.h
//        UpPermissionPluginManager.sharedInstance().requestPermisssions(["microphone"], immediate: true) { isAllowed, _, _ in
//            // 同意权限后,有权限弹窗消失的动画,此时App还不是active状态,
//            // 由于FlutterBoost的限制,非active状态不会更新绘制Flutter UI,
//            // 导致Flutter页面onPageShow等生命周期方法不会执行。
//            // 系统动画大多数时长为0.35s,实测延时400ms后Flutter页面生命周期正常
//            DispatchQueue.main.asyncAfter(deadline: .now() + DispatchTimeInterval.milliseconds(400)) {
//                operation?(isAllowed)
//            }
//        }
//    }
}
