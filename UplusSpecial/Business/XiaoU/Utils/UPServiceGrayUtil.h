//
//  UPServiceGrayUtil.h
//  UplusSpecial
//
//  Created by 路标 on 2024/1/5.
//

#import <Foundation/Foundation.h>
#import "UIKit/UIKit.h"

NS_ASSUME_NONNULL_BEGIN

@interface UIImage (ServiceGray)

/// 根据当前Bundle中图片名称创建UIImage对象
/// - Parameters:
///     - name: 图片文件名称
/// - Returns: 当前Bundle中图片文件名称为name的UIImage实例, 如果App开启了哀悼模式, 会自动转为灰度图返回
///
/// 用法同系统的 +[UIImage imageNamed:] 方法, 无需传Bundle名称和后缀名。该方法会根据哀悼模式开关返回原图或灰度图。
+ (UIImage *)serviceImageNamed:(NSString *)name;

@end

@interface UIColor (ServiceGray)
/// 根据RGB值创建UIColor对象
/// - Parameters:
///     - red: 红色色值, 取值范围[0, 255]
///     - green: 绿色色值, 取值范围[0, 255]
///     - blue: 蓝色色值, 取值范围[0, 255]
/// - Returns: 对应RGB颜色的UIColor实例, alpha = 1, 如果App开启了哀悼模式, 会自动转换为灰色
UIColor *serviceColorWithRGB(NSInteger red, NSInteger green, NSInteger blue);

/// 根据RGBA值创建UIColor对象
/// - Parameters:
///     - red: 红色色值, 取值范围[0, 255]
///     - green: 绿色色值, 取值范围[0, 255]
///     - blue: 蓝色色值, 取值范围[0, 255]
///     - alpha: 透明度, 取值范围[0, 1]
/// - Returns: 对应RGB颜色的UIColor实例, 如果App开启了哀悼模式, 会自动转换为灰色
UIColor *serviceColorWithRGBA(NSInteger red, NSInteger green, NSInteger blue, CGFloat alpha);
@end

NS_ASSUME_NONNULL_END
