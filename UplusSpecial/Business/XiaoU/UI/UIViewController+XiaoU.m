//
//  UIViewController+XiaoU.m
//  mainbox
//
//  Created by peng li on 2021/4/16.
//

#import "UIViewController+XiaoU.h"
#import <objc/runtime.h>
#import <UPStorage/UPStorage.h>
#import <UPVDN/UIViewController+Vdn.h>
#import <UPVDN/UpVdnUtils.h>
#import "UPServiceConstant.h"
#import "UPServiceFloatViewManager.h"
#import "UplusSpecial-Swift.h"

static NSMutableArray *showIconArray = nil;

static NSMutableArray *showNativeArray = nil;

@implementation UIViewController (XiaoU)

#pragma mark - Swizzing
+ (void)load
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      Method didLoadFrom = class_getInstanceMethod([self class], @selector(viewDidLoad));
      Method didLoadTo = class_getInstanceMethod([self class], @selector(swizzingViewDidLoad));
      if (!class_addMethod([self class], @selector(swizzingViewDidLoad), method_getImplementation(didLoadTo), method_getTypeEncoding(didLoadTo))) {
          method_exchangeImplementations(didLoadFrom, didLoadTo);
      }

      Method fromMethod = class_getInstanceMethod([self class], @selector(viewWillAppear:));
      Method toMethod = class_getInstanceMethod([self class], @selector(swizzingViewWillAppear:));
      if (!class_addMethod([self class], @selector(swizzingViewWillAppear:), method_getImplementation(toMethod), method_getTypeEncoding(toMethod))) {
          method_exchangeImplementations(fromMethod, toMethod);
      }

      Method didAppearFromMethod = class_getInstanceMethod([self class], @selector(viewDidAppear:));
      Method didAppearToMethod = class_getInstanceMethod([self class], @selector(swizzingViewDidAppear:));
      if (!class_addMethod([self class], @selector(swizzingViewDidAppear:), method_getImplementation(didAppearToMethod), method_getTypeEncoding(didAppearToMethod))) {
          method_exchangeImplementations(didAppearFromMethod, didAppearToMethod);
      }

      Method fromMethodOne = class_getInstanceMethod([self class], @selector(viewWillDisappear:));
      Method toMethodOne = class_getInstanceMethod([self class], @selector(swizzingViewWillDisappear:));
      if (!class_addMethod([self class], @selector(swizzingViewWillDisappear:), method_getImplementation(toMethodOne), method_getTypeEncoding(toMethodOne))) {
          method_exchangeImplementations(fromMethodOne, toMethodOne);
      }
    });
}

- (void)swizzingViewDidLoad
{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];
    [UPServiceEnableManager updateXiaouEnableConfig:self.originalURL params:self.parameters];
    [self swizzingViewDidLoad];
}

- (void)swizzingViewWillAppear:(BOOL)animate
{
    if ([self isAlert]) {
        // 如果是alert,不处理小优的显示/或隐藏,保持现状
        [self swizzingViewWillAppear:animate];
        return;
    }
    [self updateXiaouFloatView];
    [self swizzingViewWillAppear:animate];
}

- (void)swizzingViewDidAppear:(BOOL)animate
{
    [self swizzingViewDidAppear:animate];
}

- (void)swizzingViewWillDisappear:(BOOL)animate
{
    if ([self isAlert]) {
        // 如果是alert,不处理小优的显示/或隐藏,保持现状
        [self swizzingViewWillDisappear:animate];
        return;
    }

    NSDictionary *presentingInfo = [self presentingControllerInfo];
    BOOL isShow = NO;
    if (presentingInfo) {
        // 模态页面dissmiss时,底下controller生命周期不走,需要提前判断底下页面是否显示小优
        isShow = [self isShowFloatView:presentingInfo[@"originalURL"]
                               realURL:presentingInfo[@"realURL"]
                                params:presentingInfo[@"parameters"]
                             className:presentingInfo[@"className"]];
        /// 如果是模态页面在push/pop,该逻辑走完之后会再走被push/pop页面的生命周期
        /// 会再判断一次被push/pop的页面是否显示小优入口
        [UPServiceFloatViewManager.shareManager setFloatViewHidden:!isShow];
    }
    else if ([self isShowFloatView:self.originalURL
                           realURL:self.realURL
                            params:self.parameters
                         className:NSStringFromClass([self class])]) {
        [UPServiceFloatViewManager.shareManager setFloatViewHidden:YES];
        // 离开页面时，清除当前页面的语料
        [UPStorage deleteMemoryString:SERVICE_STORAGE_KEY];
    }
    [self swizzingViewWillDisappear:animate];
}

/// 刷新小优入口显示/隐藏
///
/// - Parameters:
///
///     - appear: YES-viewWillApper, NO-viewWillDisappear
///
///     - forceUpdate: 强制刷新
///
/// 根据页面配置信息刷新小优入口的显示/隐藏状态。如果当前是Alert,则按照Alert底下的页面配置信息刷新。
//- (void)updateXiaouEntranceShown:(BOOL)appear forceUpdate:(BOOL)forceUpdate {
//    /// From: Uplus 8.5.0, UplusSpecial: >=0.9.1.2024041901
//    /// - viewWillAppear:
//    ///     - 1. 正常的页面跳转，判断当前页面是否显示小优，更新显示/隐藏状态
//    ///     - 2. 弹出模态页面，例如自发现弹窗,系统Alert等。
//    ///         - 对于App页面根据配置刷新显示/隐藏状态(和8.5.0之前的版本表现略有不同,老版本仅保证需要显示时显示,不关心是否隐藏)
//    ///         - 对于Alert,不刷新,保持原状态
//    /// - viewWillDisappear:
//    ///     - 1. 正常的页面跳转,不处理,由即将显示的页面viewWillAppear:判断并刷新显示/隐藏状态
//    ///     - 2. 模态页面消失, 不会走底下ViewController的viewWillAppear方法,需要提前判断并刷新
//    ///         - 对于模态App页面的关闭,需要判断关闭后的页面是否显示并刷新
//    ///         - 对于Alert,不刷新,保持原状态
//    /// - enterForeground: 回到前台时需要刷新小优显示/隐藏
//    ///     - 1. Alert状态下退到后台再回来依然显示Alert且不走任何生命周期,按预期应该刷新底下ViewController小优的显示/隐藏
//    ///     - 2. App内的业务页面(包括模态页面),当前页面的是自己,根据配置刷新
//}

- (void)updateXiaouFloatView
{
    if ([self isShowFloatView:self.originalURL
                      realURL:self.realURL
                       params:self.parameters
                    className:NSStringFromClass([self class])]) {
        [UPServiceFloatViewManager.shareManager addXiaoUFloatViewTo:self.navigationController.view];
        [UPServiceFloatViewManager.shareManager setFloatViewHidden:NO];
    }
    else {
        if ([self isEqual:[UpVdnUtils getCurrentViewController]]) {
            [UPServiceFloatViewManager.shareManager setFloatViewHidden:YES];
        }
    }
}

- (void)onAppEnterForeground:(NSNotification *)notify
{
    if ([self isEqual:[UpVdnUtils getCurrentViewController]] && ![self isAlert]) {
        [self updateXiaouFloatView];
    }
}

- (BOOL)isShowFloatView:(NSString *)originalURL
                realURL:(NSString *)realURL
                 params:(NSDictionary *)params
              className:(NSString *)className
{
#if DEBUG
    if ([self isKindOfClass:NSClassFromString(@"DebuggerConfigViewController")]) {
        return YES;
    }
#endif
    if ([UPServiceEnableManager isDeviceDetailPage:originalURL
                                            params:params]) {
        return [UPServiceEnableManager isXiaouEnabled:originalURL
                                              realURL:realURL
                                               params:params];
    }

    //    BOOL isContainer = [self isContainListUrl:originalURL] || [self isNativeShowUrl:className];
    //    return isContainer;
    return NO;
}

//- (BOOL)isNativeShowUrl:(NSString *)className
//{
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//      NSString *htmlPath = [[NSBundle mainBundle] pathForResource:@"UplusSpecial.bundle/NativePageShow.plist" ofType:@""];
//      NSArray *htmlArray = [NSArray arrayWithContentsOfFile:htmlPath];
//      showNativeArray = [NSMutableArray arrayWithArray:htmlArray];
//    });
//    BOOL isContainer = [showNativeArray containsObject:className];
//    return isContainer;
//}
//
//- (BOOL)isContainListUrl:(NSString *)originUrl
//{
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//      NSString *htmlPath = [[NSBundle mainBundle] pathForResource:@"UplusSpecial.bundle/HtmlPageShow.plist" ofType:@""];
//      NSArray *htmlArray = [NSArray arrayWithContentsOfFile:htmlPath];
//      showIconArray = [NSMutableArray arrayWithArray:htmlArray];
//    });
//    __block BOOL isContainer = NO;
//    [showIconArray enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
//      NSString *url = (NSString *)obj;
//      if ([originUrl containsString:url]) {
//          isContainer = YES;
//          *stop = YES;
//      }
//    }];
//    return isContainer;
//}

- (BOOL)isAlert
{
    if ([self isKindOfClass:[UIAlertController class]]) {
        return true;
    }

    if ([self isKindOfClass:NSClassFromString(@"UIApplicationRotationFollowingController")]) {
        return true;
    }
    return false;
}

- (NSDictionary *)presentingControllerInfo
{
    if (!self.presentingViewController || [self isKindOfClass:[UINavigationController class]]) {
        return nil;
    }

    /// Flutter透明容器dismiss时,如果底下的controller是Flutter容器,
    /// 会手动调用底下controller的生命周期方法:
    /// @see Flutter vdnPlugin/PlatformRouterImp.m
    BOOL isFlutterTransContainer = [self.navigationController isKindOfClass:NSClassFromString(@"FlutterTranslucentNavController")];

    UIViewController *controller = self.presentingViewController;
    if ([controller isKindOfClass:[UINavigationController class]]) {
        controller = ((UINavigationController *)controller).viewControllers.lastObject;
    }

    if (isFlutterTransContainer && [controller.originalURL hasPrefix:@"flutter://"]) {
        return nil;
    }

    NSMutableDictionary *info = [NSMutableDictionary dictionary];
    info[@"originalURL"] = controller.originalURL ?: @"";
    info[@"realURL"] = controller.realURL ?: @"";
    info[@"parameters"] = controller.parameters ?: @{};
    info[@"className"] = NSStringFromClass([controller class]);
    return info;
}

@end
