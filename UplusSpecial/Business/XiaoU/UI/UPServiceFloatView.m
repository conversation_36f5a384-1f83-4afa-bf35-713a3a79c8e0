//
//  UPServiceFloatView.m
//  mainbox
//
//  Created by ha<PERSON> on 2021/4/19.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPServiceFloatView.h"
#import "UPServiceFloatViewManager.h"
#import <UPStorage/UPStorage.h>
//#import "UPServiceFloatViewManager+Badge.h"
#import "UPServiceGrayUtil.h"
#import "UPServiceFloatBubbleView.h"
//#import "UplusSpecial-Swift.h"

@interface UPServiceFloatView ()

@property (nonatomic, strong) UIImageView *imageView; //图片

//@property (nonatomic, strong, readwrite) UPServiceBadgeView *badgeView;

@end

@implementation UPServiceFloatView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, frame.size.width, frame.size.height)];

        //        _badgeView = [[UPServiceBadgeView alloc] initWithFrame:CGRectMake(0, 0, XY_BADGE_VIEW_SIZE, XY_BADGE_VIEW_SIZE)];
        ;
        [self updateTheme:[[UPServiceFloatViewManager shareManager] getThemeType]];

        [self addSubview:_imageView];
        //        [self addSubview:_badgeView];
    }
    return self;
}

- (void)setFrame:(CGRect)frame
{
    [super setFrame:frame];
    if (!self.superview) {
        return;
    }
    UIView *bubbleView = UPServiceFloatViewManager.shareManager.bubbleView;
    CGFloat midX = CGRectGetWidth(self.superview.frame) / 2;
    CGFloat bubbleX = CGRectGetMinX(frame);
    BOOL isRightSide = CGRectGetMidX(frame) > midX;
    UPServiceFloatViewManager.shareManager.isFloatingRightSide = isRightSide;
    if (isRightSide) {
        // 悬浮按钮在右边
        bubbleX = CGRectGetMaxX(frame) - CGRectGetWidth(bubbleView.frame);
    }
    bubbleView.frame = CGRectMake(bubbleX,
                                  CGRectGetMinY(frame),
                                  CGRectGetWidth(bubbleView.frame),
                                  CGRectGetHeight(bubbleView.frame));
    //    [self updateBadgeView];
}

//- (void)didMoveToSuperview
//{
//    [super didMoveToSuperview];
//    if (!self.superview) {
//        return;
//    }
//
//    [self updateBadgeView];
//}

//- (void)updateBadgeView
//{
//    CGFloat x = -(4 * SCREEN_SCALE_375);
//    if (UPServiceFloatViewManager.shareManager.isFloatingRightSide) {
//        x = CGRectGetWidth(self.frame) + (4 * SCREEN_SCALE_375) - XY_BADGE_VIEW_SIZE;
//    }
//    CGFloat y = -(3 * SCREEN_SCALE_375);
//    _badgeView.frame = CGRectMake(x, y,
//                                  XY_BADGE_VIEW_SIZE,
//                                  XY_BADGE_VIEW_SIZE);
//}

- (void)setHidden:(BOOL)hidden
{
    [super setHidden:hidden];
    UPServiceFloatViewManager.shareManager.bubbleView.hidden = hidden;
    //    self.badgeView.hidden = hidden;
}

- (void)updateTheme:(UPServiceTheme)themeType
{
    //    NSString *imgName = themeType == UPServiceThemeNewYear ? @"custom_service_festival" : @"custom_service";
    NSString *imgName = @"custom_service";
    self.imageView.image = [UIImage serviceImageNamed:imgName];
    //    self.badgeView.backgroundColor = serviceColorWithRGB(237, 40, 86);
}

//- (BOOL)isDragging
//{
//    //更新冒泡View的位置（判断当前小优入口图标是不是在被拖动中）
//    UIGestureRecognizer *gesture = nil;
//    for (UIGestureRecognizer *oneGesture in self.gestureRecognizers) {
//        if ([oneGesture isKindOfClass:[UIPanGestureRecognizer class]]) {
//            gesture = oneGesture;
//        }
//    }
//
//    if (gesture) {
//        // 当处于拖动中时，不显示冒泡信息
//        if (gesture.state == UIGestureRecognizerStateBegan || gesture.state == UIGestureRecognizerStateChanged) {
//            return YES;
//        }
//    }
//    return NO;
//}

@end
