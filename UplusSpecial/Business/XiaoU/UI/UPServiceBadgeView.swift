////
////  UPServiceBadgeView.swift
////  UplusSpecial
////
////  Created by 路标 on 2024/1/16.
////
//
//import UIKit
//
//@objc public class UPServiceBadgeView: UIView {
//    private let badgeLabel = UILabel()
//    
//    public override init(frame: CGRect) {
//        super.init(frame: frame)
//        layer.cornerRadius = frame.width / 2;
//        layer.masksToBounds = true
//        
//        let factor = UIScreen.main.bounds.width / 375;
//        badgeLabel.frame = CGRect(origin: .zero, size: frame.size)
//        badgeLabel.font = UIFont(name: "PingFangSC-Regular", size: 12 * factor)
//        badgeLabel.textAlignment = .center
//        badgeLabel.textColor = .white
//        addSubview(badgeLabel)
//    }
//    
//    @objc public var badgeValue: Int = 0 {
//        didSet {
//            isHidden = badgeValue < 1
//            if !isHidden {
//                badgeLabel.text = badgeValue > 99 ? "99" : "\(badgeValue)"
//            }
//        }
//    }
//    
//    required init?(coder: NSCoder) {
//        fatalError("init(coder:) has not been implemented")
//    }
//}
