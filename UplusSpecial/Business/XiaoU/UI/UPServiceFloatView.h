//
//  UPServiceFloatView.h
//  mainbox
//
//  Created by ha<PERSON> on 2021/4/19.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPServiceConstant.h"

NS_ASSUME_NONNULL_BEGIN

//@class UPServiceBadgeView;

@interface UPServiceFloatView : UIView

//@property (nonatomic, strong, readonly) UPServiceBadgeView *badgeView;

/// 更新主题UI(节日主题 & 哀悼模式), 如果节日主题和哀悼模式同时开启, 会使用节日图片并转灰度图
- (void)updateTheme:(UPServiceTheme)themeType;

//@property (nonatomic, readonly, getter=isDragging) BOOL dragging;

@end

NS_ASSUME_NONNULL_END
