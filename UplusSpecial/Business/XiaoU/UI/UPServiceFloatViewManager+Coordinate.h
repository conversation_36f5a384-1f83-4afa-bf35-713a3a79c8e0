//
//  UPServiceFloatViewManager+Coordinate.h
//  UplusSpecial
//
//  Created by 路标 on 2023/12/27.
//

#import "UPServiceFloatViewManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPServiceFloatViewManager (Coordinate)

/// 初始化一些坐标常量, 必须在初始化UI之前调用
- (void)initCoordinateConstants;

/// 获取小优悬浮按钮的初始位置frame
- (CGRect)floatViewInitialFrame;

/// 处理小优拖拽手势
- (void)onFloatViewPanGesture:(UIPanGestureRecognizer *)gesture;

///// 处理主Tab页面滚动
//- (void)onMainTabPageScrolling;
//
///// 处理主Tab切换
//- (void)onMainTabSwitchNotification:(NSNotification *)notify;
//
///// 收起小优图标(页面滚动时屏幕上只显示图标的一半)
//- (void)moveFloatViewOut;
//
///// 显示小优图标(页面停止滚动2s后显示整个图标)
//- (void)moveFloatViewIn;

/// 自动吸边。拖拽手势结束后自动靠近屏幕边缘
- (void)attractToEdgeAutomatically:(BOOL)animate;

@end

NS_ASSUME_NONNULL_END
