////
////  UPServiceFloatViewManager+Bubble.m
////  UplusSpecial
////
////  Created by 路标 on 2023/12/28.
////
//
//#import "UPServiceFloatViewManager+Bubble.h"
//#import <UPLog/UPLog.h>
//#import <UPStorage/UPStorage.h>
//#import "UPServiceConstant.h"
//#import "UplusSpecial-Swift.h"
//#import <UpTrace/UPEventTrace.h>
//#import <UpVdnModule/UpVdnModuleService.h>
//#import <UpUserDomain/UpUserDomainHolder.h>
//#import "UPServiceFloatView.h"
//#import "UPServiceFloatBubbleView.h"
//
//@implementation UPServiceFloatViewManager (Bubble)
//- (BOOL)isBubbleShowing
//{
//    return CGRectGetWidth(self.bubbleView.frame) > CGRectGetWidth(self.floatView.frame);
//}
//
//- (void)onBubbleContentChange
//{
//    // 判断当前状态是否可以展示
//    if (![self shouldShowBubble]) {
//        return;
//    }
//
//    NSDictionary *info = [self getBubbleInfo];
//    if (!info) {
//        return;
//    }
//
//    NSString *bubbleContent = info[SERVICE_BUBBLE_CONTENT_KEY];
//    if (![bubbleContent isKindOfClass:NSString.class] || bubbleContent.length < 1) {
//        return;
//    }
//
//    [self.bubbleView updateAndShow:info];
//
//    NSDictionary *variable = [self getBubbleTrackVariable];
//    [[UPEventTrace getInstance] trace:@"MB35771" withVariable:variable];
//}
//
//- (NSDictionary *_Nullable)getBubbleInfo
//{
//    NSString *json = [UPStorage getMemoryString:SERVICE_BUBBLE_STORAGE_OBSERVE_KEY defaultValue:@""];
//    UPLogDebug(@"UplusSpecial", @"native_bubble json: %@", json);
//    if (![json isKindOfClass:NSString.class] || json.length < 1) {
//        return nil;
//    }
//
//    NSError *error = nil;
//    NSData *data = [json dataUsingEncoding:NSUTF8StringEncoding];
//    NSDictionary *info = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:&error];
//    if (error) {
//        UPLogError(@"UplusSpecial", @"parse native_bubble json error: %@", error.localizedDescription);
//        return nil;
//    }
//    UPLogDebug(@"UplusSpecial", @"native_bubble info: %@", info);
//    return info;
//}
//
//- (void)onBubbleTapped:(UITapGestureRecognizer *)gesture
//{
//    if (gesture.state != UIGestureRecognizerStateEnded) {
//        return;
//    }
//
//    [[UPEventTrace getInstance] trace:@"MB35772" withVariable:[self getBubbleTrackVariable]];
//
//    if (![UPServicePageUtil checkServiceValidStatus]) {
//        return;
//    }
//
//    [self.bubbleView dismiss];
//
//    NSString *targetPage = [self getBubbleTargetPageURL];
//    if (!targetPage || [targetPage hasPrefix:UPServicePageUtil.serviceBaseURL]) {
//        // 冒泡中没有带跳转链接,默认跳转小优语音助手
//        // 冒泡中有跳转链接并且跳转的是小优语音助手
//        // 先申请麦克风权限再跳转
//        [UPServicePageUtil requestMicrophoneAuthThenDo:^(BOOL isAllowed) {
//          if (isAllowed) {
//              [self gotoBubbleTargetPage:targetPage];
//          }
//        }];
//    }
//    else {
//        // 跳转其他页面,由业务方按需申请权限
//        [self gotoBubbleTargetPage:targetPage];
//    }
//}
//
//- (void)gotoBubbleTargetPage:(NSString *)targetUrl
//{
//    if (self.isShowingTargetPage) {
//        return;
//    }
//    self.isShowingTargetPage = YES;
//
//    NSString *targetPage = targetUrl;
//    VdnPageFlag pageFlag = VdnPageFlagPush;
//    NSDictionary *params = @{ @"hidesBottomBarWhenPushed" : @"1" };
//    if (!targetPage) {
//        targetPage = [UPServicePageUtil xiaouServiceURL].absoluteString;
//        pageFlag = VdnPageFlagPresent;
//        params = @{ @"isTranslucent" : @"1" };
//        NSURLComponents *components = [NSURLComponents componentsWithString:targetPage];
//        NSString *bubbleKeywords = self.bubbleView.bubbleInfo[@"keywords"];
//        if (bubbleKeywords.length) {
//            NSMutableArray<NSURLQueryItem *> *queries = [NSMutableArray arrayWithArray:components.queryItems];
//            [queries addObject:[NSURLQueryItem queryItemWithName:@"bubbleText" value:bubbleKeywords]];
//            components.queryItems = queries;
//        }
//        targetPage = components.string;
//    }
//
//    UPLogInfo(@"UplusSpecial", @"native_bubble tap jumpUrl = %@", targetPage);
//    [UpVdn goToPage:targetPage
//               flag:pageFlag
//         parameters:params
//           complete:nil
//              error:nil];
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//      self.isShowingTargetPage = NO;
//    });
//}
//
//- (NSString *)getBubbleTargetPageURL
//{
//    NSString *targetPage = self.bubbleView.bubbleInfo[@"jumpUri"];
//    if (![targetPage isKindOfClass:NSString.class] || targetPage.length < 1) {
//        UPLogInfo(@"UplusSpecial", @"native_bubble invalid value for key `jumpUri`: %@", targetPage);
//        return nil;
//    }
//    NSURLComponents *components = [NSURLComponents componentsWithString:targetPage];
//    if (components.scheme.length < 1) {
//        UPLogError(@"UplusSpecial", @"native_bubble invalid scheme in jumpUri: %@, scheme: %@", targetPage, components.scheme);
//        return nil;
//    }
//
//    if (components.host.length < 1) {
//        UPLogError(@"UplusSpecial", @"native_bubble invalid host in jumpUri: %@, host: %@, path: %@", targetPage, components.host, components.path);
//        return nil;
//    }
//    return targetPage;
//}
//
//- (BOOL)shouldShowBubble
//{
//    if (self.floatView.isHidden) {
//        // 当前页面不显示小优悬浮按钮
//        return NO;
//    }
//
//    if (self.isBubbleShowing) {
//        // 已经在展示冒泡, 忽略本次冒泡
//        return NO;
//    }
//
//    if (self.floatView.isDragging) {
//        // 拖动中,不显示冒泡
//        return NO;
//    }
//
//    if (CGRectGetMinX(self.floatView.frame) < 0 ||
//        CGRectGetMaxX(self.floatView.frame) > CGRectGetWidth(self.floatView.superview.frame)) {
//        // 主Tab滚动中,小优图标收起(隐藏一半)时不展示冒泡
//        return NO;
//    }
//
//    // 两个开关: 耗材/故障开关; 智能客服开关
//    NSString *bubbleType = self.bubbleView.bubbleInfo[SERVICE_BUBBLE_CARD_TYPE_KEY];
//    if ([bubbleType isEqualToString:SERVICE_BUBBLE_CARD_CONSUMABLE] || [bubbleType isEqualToString:SERVICE_BUBBLE_CARD_FAULT]) {
//        /// 耗材开关逻辑由业务方控制,如果开关关闭则不会请求冒泡数据接口,
//        /// 逻辑能走到这里, 说明耗材开关是打开的并且有冒泡数据
//        return YES;
//    }
//
//    /// 智能客服开关, 已登录状态默认是开。未登录状态默认是关
//    BOOL showBubble = (UpUserDomainHolder.instance.userDomain.state == UpUserDomainStateDidLogin);
//
//    NSString *isShowString = [UPStorage getStringValue:SERVICE_BUBBLE_STORAGE_KEY defaultValue:showBubble ? @"true" : @"false"];
//    return [isShowString boolValue];
//}
//
//- (NSDictionary *)getBubbleTrackVariable
//{
//    NSString *msgType = self.bubbleView.bubbleInfo[SERVICE_BUBBLE_CARD_TYPE_KEY];
//    NSString *trackValue = @"智能客服";
//    if (msgType) {
//        if ([msgType isEqualToString:SERVICE_BUBBLE_CARD_CONSUMABLE]) {
//            trackValue = @"耗材";
//        }
//        else if ([msgType isEqualToString:SERVICE_BUBBLE_CARD_FAULT]) {
//            trackValue = @"故障";
//        }
//    }
//    return @{ @"value" : trackValue };
//}
//
//@end
