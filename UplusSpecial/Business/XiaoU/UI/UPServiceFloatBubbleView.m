//
//  UPServiceFloatBubbleView.m
//  mainbox
//
//  Created by whenwe on 2/7/22.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPServiceFloatBubbleView.h"
//#import "UPServiceFloatView.h"
//#import <QuartzCore/CAAnimation.h>
#import "UPServiceFloatViewManager.h"
#import "UPServiceGrayUtil.h"
//#import "UPServiceFloatViewManager+Coordinate.h"

CGFloat const XY_BUBBLE_ANIMATION_TIME = 0.5;

@interface UPServiceFloatBubbleView ()

//@property (nonatomic, copy, readwrite) NSDictionary *bubbleInfo;

//@property (nonatomic, strong) UILabel *bubbleLabel; //label

//@property (nonatomic, assign) NSInteger bubbleTime; //气泡显示时间

/// 返回按钮
//@property (nonatomic, strong) UIButton *backButton;

//@property (nonatomic, weak) UIPanGestureRecognizer *panGesture;

@end

@implementation UPServiceFloatBubbleView

+ (Class)layerClass
{
    return [CAGradientLayer class];
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        CAGradientLayer *layer = (CAGradientLayer *)self.layer;
        layer.cornerRadius = XY_FLOAT_SIZE / 2;
        layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.3].CGColor;
        layer.shadowOffset = CGSizeMake(-1, 6);
        layer.shadowRadius = 20;
        layer.shadowOpacity = 1.0;
        [self updateLayerColors:[[UPServiceFloatViewManager shareManager] getThemeType]];

        //        UIPanGestureRecognizer *gesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(onBubblePanGesture:)];
        //        [self addGestureRecognizer:gesture];
        //        gesture.enabled = NO;
        //        self.panGesture = gesture;
    }
    return self;
}

//- (void)updateAndShow:(NSDictionary *)bubbleInfo
//{
//    self.bubbleInfo = bubbleInfo;
//    UIView *floatView = UPServiceFloatViewManager.shareManager.floatView;
//    CGFloat totalWidth = CGRectGetWidth(self.superview.frame);
//    BOOL isRightSide = UPServiceFloatViewManager.shareManager.isFloatingRightSide;
//
//    NSString *imgName = isRightSide ? @"bubble_close_right" : @"bubble_close_left";
//    UIImage *backImg = [UIImage serviceImageNamed:imgName];
//    [self.backButton setImage:backImg forState:UIControlStateNormal];
//
//    CGFloat bubbleMaxWidth = totalWidth - XY_FLOAT_SCREEN_EDGE * 2;
//    // 文字内容最大宽度, label和小优图标的间距为8
//    CGFloat cotentMaxWidth = bubbleMaxWidth - CGRectGetWidth(floatView.frame) - XY_BACKBUTTON_MARGIN * 2 - backImg.size.width - 8 * SCREEN_SCALE_375;
//
//    NSString *content = bubbleInfo[SERVICE_BUBBLE_CONTENT_KEY];
//    CGSize maxSize = CGSizeMake(CGFLOAT_MAX, XY_CONTENT_HEIGHT);
//    NSStringDrawingOptions options = NSStringDrawingUsesFontLeading | NSStringDrawingUsesLineFragmentOrigin;
//    NSDictionary *attr = @{NSFontAttributeName : self.bubbleLabel.font};
//    CGSize size = [content boundingRectWithSize:maxSize
//                                        options:options
//                                     attributes:attr
//                                        context:nil]
//                      .size;
//    // 文字实际展示的宽度
//    CGFloat contentWidth = size.width > cotentMaxWidth ? cotentMaxWidth : size.width;
//    // 气泡实际展示的宽度
//    CGFloat bubbleWidth = (XY_BACKBUTTON_MARGIN * 2) + backImg.size.width + contentWidth + XY_CONTENT_MARGIN + CGRectGetWidth(floatView.frame);
//
//    CGRect backButtonTargetFrame;
//    CGRect labelTargetFrame;
//    CGRect bubbleTargetFrame;
//    CGFloat backButtonY = (CGRectGetHeight(floatView.frame) - backImg.size.height) / 2;
//    CGFloat labelY = (CGRectGetHeight(floatView.frame) - XY_CONTENT_HEIGHT) / 2;
//    if (isRightSide) {
//        self.backButton.frame = CGRectMake(XY_BACKBUTTON_MARGIN, backButtonY, backImg.size.width, backImg.size.height);
//
//        CGFloat labelX = CGRectGetMaxX(self.backButton.frame) + XY_BACKBUTTON_MARGIN;
//        self.bubbleLabel.frame = CGRectMake(labelX, labelY, 0, XY_CONTENT_HEIGHT);
//        backButtonTargetFrame = self.backButton.frame;
//        labelTargetFrame = CGRectMake(labelX, labelY, contentWidth, XY_CONTENT_HEIGHT);
//        bubbleTargetFrame = CGRectMake(totalWidth - XY_FLOAT_SCREEN_EDGE - bubbleWidth, CGRectGetMinY(floatView.frame), bubbleWidth, CGRectGetHeight(floatView.frame));
//    }
//    else {
//        self.backButton.frame = CGRectMake(CGRectGetWidth(self.frame) - XY_BACKBUTTON_MARGIN - backImg.size.width, backButtonY, backImg.size.width, backImg.size.width);
//        backButtonTargetFrame = CGRectMake(bubbleWidth - XY_BACKBUTTON_MARGIN - backImg.size.width, backButtonY, backImg.size.width, backImg.size.height);
//
//        CGFloat labelX = CGRectGetWidth(floatView.frame) + XY_CONTENT_MARGIN;
//        self.bubbleLabel.frame = CGRectMake(labelX, labelY, 0, XY_CONTENT_HEIGHT);
//        labelTargetFrame = CGRectMake(labelX, labelY, contentWidth, XY_CONTENT_HEIGHT);
//        bubbleTargetFrame = CGRectMake(XY_FLOAT_SCREEN_EDGE, CGRectGetMinY(floatView.frame), bubbleWidth, CGRectGetHeight(floatView.frame));
//    }
//    self.bubbleLabel.text = content;
//    self.frame = floatView.frame;
//
//    [self updateBubbleGradient:isRightSide];
//
//    self.backButton.alpha = 0;
//    self.bubbleLabel.alpha = 0;
//    [UIView animateWithDuration:XY_BUBBLE_ANIMATION_TIME
//        animations:^{
//          self.frame = bubbleTargetFrame;
//          self.backButton.frame = backButtonTargetFrame;
//          self.backButton.alpha = 1.0;
//          self.bubbleLabel.frame = labelTargetFrame;
//          self.bubbleLabel.alpha = 1.0;
//        }
//        completion:^(BOOL finished) {
//          self.panGesture.enabled = YES;
//        }];
//
//    NSTimeInterval bubbleTime = [bubbleInfo[SERVICE_BUBBLE_DURATION_KEY] doubleValue];
//    if (bubbleTime <= 0) {
//        bubbleTime = 5;
//    }
//    self.bubbleTime = bubbleTime;
//
//    // 重新刷新气泡显示时间
//    [self refreshBubbleShowTime];
//}

- (void)updateBubbleGradient:(BOOL)isRightSide
{
    CAGradientLayer *layer = (CAGradientLayer *)self.layer;
    if (isRightSide) {
        layer.startPoint = CGPointMake(0, 0.5);
        layer.endPoint = CGPointMake(1, 0.5);
    }
    else {
        layer.startPoint = CGPointMake(1, 0.5);
        layer.endPoint = CGPointMake(0, 0.5);
    }
}

//- (void)pauseBubbleShowTime
//{
//    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(dismiss) object:nil];
//}
//
//- (void)refreshBubbleShowTime
//{
//    //五秒后隐藏（先取消之前的延时执行操作，再重新延时执行）
//    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(dismiss) object:nil];
//    [self performSelector:@selector(dismiss) withObject:nil afterDelay:self.bubbleTime];
//}
//
//- (void)dismiss
//{
//    self.panGesture.enabled = NO;
//    UIView *floatView = UPServiceFloatViewManager.shareManager.floatView;
//    BOOL isRightSide = UPServiceFloatViewManager.shareManager.isFloatingRightSide;
//    CGFloat labelX = CGRectGetWidth(floatView.frame) + XY_CONTENT_MARGIN;
//    CGFloat buttonX = CGRectGetWidth(floatView.frame) + XY_BACKBUTTON_MARGIN;
//    if (isRightSide) {
//        buttonX = XY_BACKBUTTON_MARGIN;
//        labelX = CGRectGetWidth(self.backButton.frame) + XY_BACKBUTTON_MARGIN * 2;
//    }
//    CGRect buttonFrame = CGRectMake(buttonX, CGRectGetMinY(self.backButton.frame), CGRectGetWidth(self.backButton.frame), CGRectGetHeight(self.backButton.frame));
//    CGRect labelFrame = CGRectMake(labelX, CGRectGetMinY(self.bubbleLabel.frame), 0, XY_CONTENT_HEIGHT);
//    void (^block)(void) = ^{
//      self.backButton.frame = buttonFrame;
//      self.backButton.alpha = 0;
//      self.bubbleLabel.frame = labelFrame;
//      self.bubbleLabel.alpha = 0;
//      self.frame = floatView.frame;
//    };
//    if (self.isHidden) {
//        block();
//    }
//    else {
//        [UIView animateWithDuration:XY_BUBBLE_ANIMATION_TIME
//                         animations:^{
//                           block();
//                         }];
//    }
//}
//
//- (UILabel *)bubbleLabel
//{
//    if (!_bubbleLabel) {
//        _bubbleLabel = [[UILabel alloc] init];
//        _bubbleLabel.textColor = [UIColor colorWithRed:4.0 / 255 green:25.0 / 255 blue:51.0 / 255 alpha:1];
//        _bubbleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12 * SCREEN_SCALE_375];
//        _bubbleLabel.numberOfLines = 0;
//        _bubbleLabel.textAlignment = NSTextAlignmentCenter;
//        _bubbleLabel.layer.masksToBounds = YES;
//        [self addSubview:_bubbleLabel];
//    }
//    return _bubbleLabel;
//}
//
//- (UIButton *)backButton
//{
//    if (_backButton == nil) {
//        _backButton = [UIButton buttonWithType:UIButtonTypeCustom];
//        [self addSubview:_backButton];
//        [_backButton addTarget:self action:@selector(closeBubbleButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
//    }
//    return _backButton;
//}
//
//- (void)closeBubbleButtonTouched:(UIButton *)button
//{
//    [self dismiss];
//}

- (void)updateLayerColors:(UPServiceTheme)themeType
{
    CAGradientLayer *layer = (CAGradientLayer *)self.layer;
    NSArray *colors;
    if (themeType == UPServiceThemeNewYear) {
        CGColorRef start = serviceColorWithRGB(254, 160, 152).CGColor;
        CGColorRef mid = serviceColorWithRGB(254, 207, 203).CGColor;
        CGColorRef end = serviceColorWithRGB(254, 234, 229).CGColor;
        colors = @[ (__bridge id)start, (__bridge id)mid, (__bridge id)end ];
    }
    else {
        CGColorRef start = serviceColorWithRGB(204, 213, 255).CGColor;
        CGColorRef end = serviceColorWithRGB(224, 237, 255).CGColor;
        colors = @[ (__bridge id)start, (__bridge id)end ];
    }
    layer.colors = colors;
}

//- (void)updateTheme:(UPServiceTheme)themeType
//{
//    [self updateLayerColors:themeType];
//    //    BOOL isRightSide = UPServiceFloatViewManager.shareManager.isFloatingRightSide;
//    //    NSString *imgName = isRightSide ? @"bubble_close_right" : @"bubble_close_left";
//    //    UIImage *backImg = [UIImage serviceImageNamed:imgName];
//    //    [self.backButton setImage:backImg forState:UIControlStateNormal];
//    [self.layer setNeedsDisplay];
//}

//- (void)onBubblePanGesture:(UIPanGestureRecognizer *)gesture
//{
//    [[UPServiceFloatViewManager shareManager] onFloatViewPanGesture:gesture];
//}

@end
