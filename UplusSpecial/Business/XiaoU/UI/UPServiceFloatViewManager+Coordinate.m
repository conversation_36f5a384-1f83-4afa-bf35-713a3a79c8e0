//
//  UPServiceFloatViewManager+Coordinate.m
//  UplusSpecial
//
//  Created by 路标 on 2023/12/27.
//

#import "UPServiceFloatViewManager+Coordinate.h"
#import <UPStorage/UPStorage.h>
#import <UpTrace/UPEventTrace.h>
#import "UPServiceFloatView.h"
#import "UPServiceFloatBubbleView.h"
#import "UPServiceConstant.h"
//#import "UplusSpecial-Swift.h"

CGFloat XY_FLOAT_SIZE;
CGFloat XY_CONTENT_HEIGHT;

#define XY_FLOAT_POSITION @"xiaou_position_factor"

@implementation UPServiceFloatViewManager (Coordinate)

- (void)initCoordinateConstants
{
    UIImage *xiaouIcon = [UIImage imageNamed:@"UplusSpecial.bundle/custom_service"];
    XY_FLOAT_SIZE = xiaouIcon.size.width * SCREEN_SCALE_375;
    XY_CONTENT_HEIGHT = XY_FLOAT_SIZE - 2 * (10 * SCREEN_SCALE_375);
}

- (CGRect)floatViewInitialFrame
{
    UIWindow *window = UIApplication.sharedApplication.keyWindow;
    CGFloat windowWidth = CGRectGetWidth(window.frame);
    CGFloat windowHeight = CGRectGetHeight(window.frame);
    // 默认的右下角位置
    CGFloat x = windowWidth - XY_FLOAT_SCREEN_EDGE - XY_FLOAT_SIZE;
    CGFloat y = windowHeight - window.safeAreaInsets.bottom - XY_MAIN_TABBAR_HEIGHT - XY_FLOAT_SIZE;
    CGFloat xFactor = x / windowWidth;
    CGFloat yFactor = y / windowHeight;

    NSString *defaultPosition = [NSString stringWithFormat:@"{%f, %f}", xFactor, yFactor];
    NSString *position = [UPStorage getStringValue:XY_FLOAT_POSITION defaultValue:defaultPosition];
    CGPoint positionFactor = CGPointFromString(position);

    x = MIN(MAX(positionFactor.x * windowWidth, XY_FLOAT_SCREEN_EDGE),
            x);
    y = MIN(MAX(positionFactor.y * windowHeight, window.safeAreaInsets.top),
            y);
    return CGRectMake(x, y, XY_FLOAT_SIZE, XY_FLOAT_SIZE);
}

#pragma mark -
#pragma mark - 拖拽手势
- (void)onFloatViewPanGesture:(UIPanGestureRecognizer *)gesture
{
    UIView *floatSuperView = gesture.view.superview;
    if (gesture.state == UIGestureRecognizerStateEnded ||
        gesture.state == UIGestureRecognizerStateCancelled) {

        [self attractToEdgeAutomatically:YES];
        //        [self.bubbleView refreshBubbleShowTime];
        [gesture setTranslation:CGPointZero inView:floatSuperView];

        if (gesture.state == UIGestureRecognizerStateEnded) {
            [[UPEventTrace getInstance] trace:@"MB35569"];
        }
        return;
    }

    if (gesture.state == UIGestureRecognizerStateBegan) {
        //        [self.bubbleView pauseBubbleShowTime];
        [gesture setTranslation:CGPointZero inView:floatSuperView];
        return;
    }

    CGPoint translation = [gesture translationInView:floatSuperView];
    [gesture setTranslation:CGPointZero inView:floatSuperView];
    CGRect frame = self.floatView.frame;
    CGFloat y = CGRectGetMinY(frame) + translation.y;
    CGFloat minY = floatSuperView.safeAreaInsets.top;
    // 在首页不遮挡底部Tab
    //    CGFloat tabHeight = [UPServicePageUtil isCurrentMainPage] ? XY_MAIN_TABBAR_HEIGHT : 0;
    CGFloat tabHeight = 0;
    CGFloat maxY = CGRectGetHeight(floatSuperView.frame) - floatSuperView.safeAreaInsets.bottom - tabHeight - CGRectGetHeight(frame);
    y = MIN(MAX(y, minY), maxY);

    //    if (self.isBubbleShowing) {
    //        // 气泡展示时只能上下移动
    //        self.floatView.frame = CGRectMake(
    //            CGRectGetMinX(frame),
    //            y,
    //            CGRectGetWidth(frame),
    //            CGRectGetHeight(frame));
    //        return;
    //    }

    CGFloat x = CGRectGetMinX(frame) + translation.x;
    CGFloat minx = XY_FLOAT_SCREEN_EDGE;
    CGFloat maxX = CGRectGetWidth(floatSuperView.frame) - XY_FLOAT_SCREEN_EDGE - CGRectGetWidth(frame);
    x = MIN(MAX(x, minx), maxX);
    self.floatView.frame = CGRectMake(x, y,
                                      CGRectGetWidth(frame),
                                      CGRectGetHeight(frame));
}

- (void)attractToEdgeAutomatically:(BOOL)animate
{
    CGRect frame = self.floatView.frame;
    UIView *superView = self.floatView.superview;
    CGFloat totalWidth = CGRectGetWidth(superView.frame);
    CGFloat x = XY_FLOAT_SCREEN_EDGE;
    if (self.isFloatingRightSide) {
        x = totalWidth - XY_FLOAT_SCREEN_EDGE - CGRectGetWidth(frame);
    }
    frame = CGRectMake(x,
                       CGRectGetMinY(frame),
                       CGRectGetWidth(frame),
                       CGRectGetHeight(frame));
    if (animate) {
        [UIView animateWithDuration:0.5
                         animations:^{
                           self.floatView.frame = frame;
                         }];
    }
    else {
        self.floatView.frame = frame;
    }

    [self.bubbleView updateBubbleGradient:self.isFloatingRightSide];

    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      CGPoint position = CGPointMake(CGRectGetMinX(frame) / CGRectGetWidth(superView.frame), CGRectGetMinY(frame) / CGRectGetHeight(superView.frame));
      NSString *positionString = NSStringFromCGPoint(position);
      [UPStorage putStringValue:positionString name:XY_FLOAT_POSITION];
    });
}

//- (void)onMainTabPageScrolling
//{
//    static BOOL _isScrolling = false;
//    BOOL isNowScrolling = [UPStorage getMemoryString:SERVICE_MAIN_PAGE_SCROLL_KEY defaultValue:@"false"].boolValue;
//    if (isNowScrolling && !_isScrolling) {
//        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(moveFloatViewIn) object:nil];
//        [self moveFloatViewOut];
//    }
//    _isScrolling = isNowScrolling;
//}

//- (void)onMainTabSwitchNotification:(NSNotification *)notify
//{
//    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(moveFloatViewIn) object:nil];
//    [self moveFloatViewIn];
//}

//- (void)moveFloatViewOut
//{
//    if (self.isBubbleShowing) {
//        return;
//    }
//
//    if (![UPServicePageUtil isCurrentMainPage]) {
//        return;
//    }
//
//    CGFloat x = -(XY_FLOAT_SIZE / 2);
//    if (self.isFloatingRightSide) {
//        x += CGRectGetWidth(self.floatView.superview.frame);
//    }
//    [self moveFloatViewTo:CGRectMake(x, CGRectGetMinY(self.floatView.frame), XY_FLOAT_SIZE, XY_FLOAT_SIZE)];
//    [self performSelector:@selector(moveFloatViewIn) withObject:nil afterDelay:2.5];
//}
//
//- (void)moveFloatViewIn
//{
//    CGFloat x = XY_FLOAT_SCREEN_EDGE;
//    if (self.isFloatingRightSide) {
//        x = CGRectGetWidth(self.floatView.superview.frame) - XY_FLOAT_SCREEN_EDGE - XY_FLOAT_SIZE;
//    }
//    [self moveFloatViewTo:CGRectMake(x, CGRectGetMinY(self.floatView.frame), XY_FLOAT_SIZE, XY_FLOAT_SIZE)];
//}

//- (void)moveFloatViewTo:(CGRect)frame
//{
//    [UIView animateWithDuration:0.5
//                     animations:^{
//                       self.floatView.frame = frame;
//                     }];
//}

@end
