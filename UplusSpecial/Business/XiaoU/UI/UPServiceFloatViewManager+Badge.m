////
////  UPServiceFloatViewManager+Badge.m
////  UplusSpecial
////
////  Created by 路标 on 2024/1/2.
////
//
//#import "UPServiceFloatViewManager+Badge.h"
//#import <UPStorage/UPStorage.h>
//#import "UPServiceFloatView.h"
//#import "UPServiceConstant.h"
//#import "UplusSpecial-Swift.h"
//
//@implementation UPServiceFloatViewManager (Badge)
//
//- (void)onBadgeValueChanged
//{
//    NSInteger badgeValue = [[UPStorage getMemoryString:SERVICE_XIAOU_BADGE_KEY defaultValue:@"0"] integerValue];
//    self.floatView.badgeView.badgeValue = badgeValue;
//}
//
//@end
