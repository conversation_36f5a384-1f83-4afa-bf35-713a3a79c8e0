//
//  UPServiceFloatBubbleView.h
//  mainbox
//
//  Created by whenwe on 2/7/22.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPServiceConstant.h"

NS_ASSUME_NONNULL_BEGIN

@class UPServiceFloatView;

/// 小优助手入口冒泡弹窗
@interface UPServiceFloatBubbleView : UIView

/// 当接收到监听的UpStorage数据发生变化后，要不要做出响应
/// 在viewWillDisappear时设为不做响应，viewWillAppear时设为可响应
/// 此处理是为了避免:在controller跳转时接收到数据更新，导致冒泡展示到了跳转后的页面上
//@property (nonatomic, assign) BOOL canShow;

/**
 {
     "native_bubble": {
         "bubbleContent": "您有n条耗材缺失，请及时处理","您有n条故障报警，请及时处理",  //必填
         "keywords": "", //选填
         "jumpUri": "", //选填
         "bubbleTime": ""，//选填 默认5秒,
         "currentTime": "", //当前时间戳
         "clickEventId": "MB35772"，// 点击冒泡点位
         "showEventId": "MB35771"，// 展示冒泡点位
         "cardType": "NEWXIAOU_CONSUMABLE_MSG" //（耗材）,"NEWXIAOU_FAULT_MSG"（故障）// 业务类型
     }
 }
 */

/// 冒泡数据
//@property (nonatomic, copy, readonly) NSDictionary *bubbleInfo;

/// 更新气泡内容并显示气泡动画
//- (void)updateAndShow:(NSDictionary *)bubbleInfo;

/// 更新冒泡View的渐变色(小优图标在左右两端时渐变方向不同)
- (void)updateBubbleGradient:(BOOL)isRightSide;

/// 停止气泡计时(计时结束后不隐藏)
//- (void)pauseBubbleShowTime;

/// 冒泡时间重新计时
//- (void)refreshBubbleShowTime;

/// 使冒泡消失
//- (void)dismiss;

/// 更新主题UI(新年主题/哀悼模式灰色滤镜)
//- (void)updateTheme:(UPServiceTheme)themeType;

@end

NS_ASSUME_NONNULL_END
