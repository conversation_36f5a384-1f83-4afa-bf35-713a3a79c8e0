//
//  UPServiceEnableRequest.swift
//  UplusSpecial
//
//  Created by 路标 on 2024/3/28.
//

import UIKit
import UPNetwork
import UPCore

class UPServiceEnableRequest: UPRequest {
    let packageName: String
    let deviceId: String
    
    init(_ packageName: String, _ deviceId: String) {
        self.packageName = packageName
        self.deviceId = deviceId
        super.init()
    }
    
    private override init() {
        fatalError("Must call `init(packageName:deviceId:)` initializer")
    }
    
    override var baseURL: String {
        if UPContext.sharedInstance().env == .acceptance {
            return "https://zj-yanshou.haier.net"
        }
        return "https://zj.haier.net"
    }
    
    override var path: String {
        "/api-gw/zjBaseServer/aiAssistant/switch"
    }
    
    override var method: UPRequestMethod {
        .POST
    }
    
    override var requestBody: NSObject? {
        ["deviceId": deviceId, "packageName": packageName] as NSObject
    }
    
    override var requestHeaders: [String : String] {
        UPCommonServerHeader.uwsHeader(withUrlString: path, body: requestBody as! [AnyHashable : Any]) as! [String : String]
    }
    
    override var timeoutInterval: TimeInterval { 15 }
    
    override var retryTimes: UInt { 0 }
    
    override var retryDelay: TimeInterval { 0 }
}
