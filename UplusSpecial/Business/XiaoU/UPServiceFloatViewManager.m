//
//  UPServiceFloatViewManager.m
//  mainbox
//
//  Created by haier on 2021/4/21.
//

#import "UPServiceFloatViewManager.h"
#import <UPlog/UPLog.h>
//#import <UPVDN/Page.h>
//#import <UPVDN/UIViewController+Vdn.h>
//#import <UPStorage/UPStorage+Observer.h>
//#import <upuserdomain/UpUserDomainHolder.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <UPTrace/UPEventTrace.h>
#import <UPStorage/UPStorage.h>
//#import <AVFoundation/AVFoundation.h>
#import <UPTools/KVNProgressShow.h>
//#import <AFNetworking/AFNetworkReachabilityManager.h>
#import "UPServiceConstant.h"
#import "UPServiceFloatViewManager+Coordinate.h"
//#import "UPServiceFloatViewManager+Bubble.h"
//#import "UPServiceFloatViewManager+Badge.h"
#import "UplusSpecial-Swift.h"
#import "UPServiceFloatView.h"
#import "UPServiceFloatBubbleView.h"

NSString *const XY_MODULE_NAME = @"UplusSpecial";

static UPServiceFloatViewManager *manager = nil;

@interface UPServiceFloatViewManager () /* <UpUserDomainObserver, UPStorageNodeChangeListener>*/

@property (nonatomic, strong, readwrite) UPServiceFloatView *floatView;

@property (nonatomic, strong, readwrite) UPServiceFloatBubbleView *bubbleView;

@end

@implementation UPServiceFloatViewManager

+ (instancetype)shareManager
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      manager = UPServiceFloatViewManager.new;
    });
    return manager;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self initCoordinateConstants];
        //        [self registerEventListener];
    }
    return self;
}

//- (void)registerEventListener
//{
// 气泡内容 -- memory string
//    [UPStorage registerDataChangeWithName:SERVICE_BUBBLE_STORAGE_OBSERVE_KEY listener:self];
// 页面滚动 -- memory string
//    [UPStorage registerDataChangeWithName:SERVICE_MAIN_PAGE_SCROLL_KEY listener:self];
// 小红点 -- memory string
//    [UPStorage registerDataChangeWithName:SERVICE_XIAOU_BADGE_KEY listener:self];
// 小优告警弹窗 -- memory string
//    [UPStorage registerDataChangeWithName:SERVICE_XIAOU_ALERT_KEY listener:self];
// 哀悼模式开关 -- storage node booleanValue
//    [UPStorage registerNodeChangeWithName:SERVICE_MOURNING_ENABLE_KEY listener:self];
// 主题换肤 storage node stringValue
//    [UPStorage registerNodeChangeWithName:SERVICE_XIAOU_THEME_KEY listener:self];

// 主Tab切换 -- message plugin, 广播通知
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onMainTabSwitchNotification:) name:SERVICE_MAIN_TAB_SWITCH_KEY object:nil];
//}

- (void)addXiaoUFloatViewTo:(UIView *)superView
{
    if ([superView.subviews containsObject:self.floatView]) {
        [superView bringSubviewToFront:self.bubbleView];
        [superView bringSubviewToFront:self.floatView];
    }
    else {
        [superView addSubview:self.bubbleView];
        [superView addSubview:self.floatView];
    }

    // 如果在二级页面把小优图标拖到底部,返回主Tab页面时Tab会被遮挡
    //    if ([UPServicePageUtil isCurrentMainPage]) {
    //        CGRect frame = self.floatView.frame;
    //        CGFloat maxY = CGRectGetHeight(superView.frame) - superView.safeAreaInsets.bottom - XY_MAIN_TABBAR_HEIGHT - XY_FLOAT_SIZE;
    //        if (CGRectGetMaxY(frame) > maxY) {
    //            self.floatView.frame = CGRectMake(CGRectGetMinX(frame), maxY, XY_FLOAT_SIZE, XY_FLOAT_SIZE);
    //        }
    //    }

    CGFloat midX = CGRectGetWidth(superView.frame) / 2;
    self.isFloatingRightSide = CGRectGetMidX(self.floatView.frame) > midX;
    [self.bubbleView updateBubbleGradient:self.isFloatingRightSide];
}

- (void)setFloatViewHidden:(BOOL)isHidden
{
    self.floatView.hidden = isHidden;
    //    BOOL shouldShow = !isHidden;
    //    if (shouldShow) {
    //        // 根据配置表需要显示小优悬浮按钮入口的页面
    //        // 如果展示了小优告警弹窗,则隐藏入口
    //        if ([self isShowingXiaoUAlert] && UPServicePageUtil.isCurrentMainPage) {
    //            shouldShow = NO;
    //        }
    //    }
    //
    //    self.floatView.hidden = !shouldShow;
    //    if (shouldShow) {
    //        // 显示时刷新小红点
    //        [self onBadgeValueChanged];
    //    }
    //    else {
    //        [self.bubbleView dismiss];
    //    }
}

- (UPServiceFloatView *)floatView
{
    if (!_floatView) {
        CGRect frame = [self floatViewInitialFrame];
        _floatView = [[UPServiceFloatView alloc] initWithFrame:frame];
        UIPanGestureRecognizer *gesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(onFloatViewPanGesture:)];
        [_floatView addGestureRecognizer:gesture];

        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onFloatViewTapped:)];
        [_floatView addGestureRecognizer:tap];
    }
    return _floatView;
}

- (void)onFloatViewTapped:(UITapGestureRecognizer *)gestrue
{
    NSString *msgCount = [UPStorage getMemoryString:SERVICE_XIAOU_BADGE_KEY defaultValue:@"0"];
    NSString *trackURL = UPServicePageUtil.trackURL;
    [[UPEventTrace getInstance] trace:@"MB35767"
                         withVariable:@{ @"status" : msgCount,
                                         @"value" : trackURL }];

    if (![UPServicePageUtil checkServiceValidStatus]) {
        return;
    }

    if (self.isShowingTargetPage) {
        return;
    }

    self.isShowingTargetPage = YES;

    //    NSString *CameraCard = [UPStorage getMemoryString:@"isNeedHookFlutterDisplay" defaultValue:@"false"];
    //
    //    if (CameraCard.length && [CameraCard isEqual:@"true"]) {
    //        [NSNotificationCenter.defaultCenter postNotificationName:@"SendMessage2Flutter" object:self userInfo:@{ @"messageName" : @"startXiaoU" }];
    //        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(300 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
    //          [UPServicePageUtil requestMicrophoneAuthThenDo:^(BOOL isAllowed) {
    //            if (isAllowed) {
    //                [self openXiaouAssistant];
    //            }
    //            else {
    //                self.isShowingTargetPage = NO;
    //            }
    //          }];
    //        });
    //    }
    //    else {
    //        [UPServicePageUtil requestMicrophoneAuthThenDo:^(BOOL isAllowed) {
    //          if (isAllowed) {
    //              [self openXiaouAssistant];
    //          }
    //          else {
    //              self.isShowingTargetPage = NO;
    //          }
    //        }];
    //    }
    [self openXiaouAssistant];
}

/// 打开小优语音助手被动服务页面(Flutter透明容器)
- (void)openXiaouAssistant
{
    NSURL *targetURL = [UPServicePageUtil xiaouServiceURL];
    NSString *url = targetURL.absoluteString;
    //拼接语料
    NSString *jsonString = [UPStorage getMemoryString:SERVICE_STORAGE_KEY defaultValue:@""];
    NSError *error;
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *corpusDic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:&error];
    if (!error) {
        if ([url containsString:@"?"]) {
            url = [NSString stringWithFormat:@"%@&corpus=%@", url, [corpusDic valueForKey:@"xiaoYCorpus"]];
        }
        else {
            url = [NSString stringWithFormat:@"%@?corpus=%@", url, [corpusDic valueForKey:@"xiaoYCorpus"]];
        }
    }
    UPLogInfo(XY_MODULE_NAME, @"floatView jumpUrl = %@", url);
    NSDictionary *params = @{
        @"isTranslucent" : @"1",
        @"page_anim" : @"anim_pop"
    };
    [UpVdn goToPage:url flag:VdnPageFlagPresent parameters:params complete:nil error:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      self.isShowingTargetPage = NO;
    });
}

/// 是否正在展示小优告警弹窗
//- (BOOL)isShowingXiaoUAlert
//{
//    return NO;
//    //    return [UPStorage getMemoryString:SERVICE_XIAOU_ALERT_KEY defaultValue:@"false"].boolValue;
//}

//- (void)onXiaoUAlertDialogue
//{
//    //    BOOL isAlert = [self isShowingXiaoUAlert];
//    //    [self setFloatViewHidden:isAlert];
//    //    if (isAlert) {
//    //        [self.floatView.layer removeAllAnimations];
//    //        [self.bubbleView.layer removeAllAnimations];
//    //        [self attractToEdgeAutomatically:NO];
//    //    }
//}

- (UPServiceTheme)getThemeType
{
    UPServiceTheme themeType = UPServiceThemeDefault;
    //    NSString *themeJson = [UPStorage getStringValue:SERVICE_XIAOU_THEME_KEY defaultValue:@"{}"];
    //    NSError *error;
    //    NSDictionary *themeInfo = [NSJSONSerialization JSONObjectWithData:[themeJson dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingAllowFragments error:&error];
    //    if (!error) {
    //        themeType = [themeInfo[@"themeType"] integerValue];
    //    }
    return themeType;
}

#pragma mark - 冒泡
- (UPServiceFloatBubbleView *)bubbleView
{
    if (!_bubbleView) {
        _bubbleView = [[UPServiceFloatBubbleView alloc] initWithFrame:self.floatView.frame];
        //        [_bubbleView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onBubbleTapped:)]];
    }
    return _bubbleView;
}

#pragma mark - UPStorageNodeChangeListener
//- (void)onNodeChange:(NSString *)name action:(NSString *)action
//{
//    //    if ([name isEqualToString:SERVICE_BUBBLE_STORAGE_OBSERVE_KEY] && ![action isEqualToString:UPStorageNodeChangeActionDelete]) {
//    //        [self onBubbleContentChange];
//    //        return;
//    //    }
//
//    //    if ([name isEqualToString:SERVICE_XIAOU_BADGE_KEY]) {
//    //        [self onBadgeValueChanged];
//    //        return;
//    //    }
//
//    //    if ([name isEqualToString:SERVICE_MAIN_PAGE_SCROLL_KEY]) {
//    //        [self onMainTabPageScrolling];
//    //        return;
//    //    }
//
//    //    if ([name isEqualToString:SERVICE_XIAOU_ALERT_KEY]) {
//    //        [self onXiaoUAlertDialogue];
//    //        return;
//    //    }
//
//    if ([name isEqualToString:SERVICE_XIAOU_THEME_KEY] || [name isEqualToString:SERVICE_MOURNING_ENABLE_KEY]) {
//        UPServiceTheme theme = [self getThemeType];
//        [self.floatView updateTheme:theme];
//        [self.bubbleView updateTheme:theme];
//        return;
//    }
//}

@end
