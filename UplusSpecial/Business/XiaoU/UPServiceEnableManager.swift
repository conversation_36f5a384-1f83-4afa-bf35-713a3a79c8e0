//
//  UPServiceEnableManager.swift
//  UplusSpecial
//
//  Created by 路标 on 2024/3/28.
//

import Foundation
import UPStorage
import uplog

public class UPServiceEnableManager: NSObject {
    
    /// 判断是否是网器详情页
    /// - Parameters:
    ///
    ///     - originalURL: 目标页面原始URL
    ///     - params: 目标页面参数
    ///
    /// - Returns: 网器详情页返回true, 否则返回false
    @objc static public func isDeviceDetailPage(_ originalURL: String?, params: [AnyHashable: Any]?) -> Bool {
        let isMpaas = originalURL?.starts(with: "mpaas://") ?? false
        if !isMpaas {
            return false
        }

        if let isDevicePage = params?["isDeviceResource"] as? Bool {
            return isDevicePage
        }
        let isDevicePage = (params?["isDeviceResource"] as? String == "1")
        return isDevicePage
    }
    
    /// 判断设备详情页面是否显示小优入口
    /// - Parameters:
    ///
    ///     - originalURL: 设备详情页原始URL
    ///     - realURL: 打开的资源包真实URL(本地下载安装好的资源包路径)
    ///     - params: 设备详情页参数
    ///
    /// - Returns: 显示小优入口返回true, 否则返回false
    @objc static public func isXiaouEnabled(_ originalURL: String?, realURL: String?, params: [String: Any]?) -> Bool {
        let devicePageInfo = getDevicePageInfo(originalURL, params: params)
        
        guard let deviceId = devicePageInfo.deviceId else {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId is invalid")
            return false
        }
        
        guard let packageName = devicePageInfo.pkgName else {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|parse packageName error. originalURL = \(String(describing: originalURL))")
            return false
        }

        return isXiaouEnabledForDevicePage(deviceId, 
                                           packageName: packageName,
                                           realURL: realURL)
    }
    
    private static func getDevicePageInfo(_ originalURL: String?, params: [String: Any]?) -> (deviceId: String?, pkgName: String?) {
        let deviceId = params?["deviceId"] as? String
        
        guard let originalURL = originalURL else {
            return (deviceId, nil)
        }
        let components = URLComponents(string: originalURL)
        let packageName = components?.host
        return (deviceId, packageName)
    }
    
    private static func isXiaouEnabledForDevicePage(_ deviceId: String, packageName: String, realURL: String?) -> Bool {
        let storageKey = "is_xiaou_enabled_\(deviceId)"
        let storageValue = UPStorage.getStringValue(storageKey, defaultValue: "")!

        if storageValue.isEmpty {
            uplog.UPPrintInfo(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|No local cache in storage")
            return false
        }
        
        guard let data = storageValue.data(using: .utf8) else {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|encode storage string value into data error")
            UPStorage.deleteNode(storageKey)
            return false
        }
        
        do {
            let info = try JSONSerialization.jsonObject(with: data, options: .fragmentsAllowed) as? [String: Any]
            let enabled = info?["enable"] as? Bool ?? false
            if !enabled {
                uplog.UPPrintInfo(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|disabled xiaou entrance")
                return false
            }
            
            guard let minVersion = info?["packageVersion"] as? String else {
                uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|encode storage string value into data error")
                UPStorage.deleteNode(storageKey)
                return false
            }
            
            let localVersion = getPackageLocalVersion(realURL, packageName)
            
            let comparisonResult = localVersion.compare(minVersion, options: .numeric)
            let isEnabled = comparisonResult == .orderedDescending || comparisonResult == .orderedSame
            
            uplog.UPPrintInfo(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|installed package: \(packageName)@\(localVersion), configured minVersion = \(minVersion), enabled = \(isEnabled)")
            
            return isEnabled
            
        } catch {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|parse storage value to JSON error")
            UPStorage.deleteNode(storageKey)
            return false
        }
    }
    
    private static func getPackageLocalVersion(_ realURL: String?, _ packageName: String) -> String {
        guard let realURL = realURL else {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|package:\(packageName)|realURL is nil")
            return "0"
        }
        
        guard let components = URLComponents(string: realURL) else {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|package:\(packageName)|parse realURL to URLComponents error. realURL = \(realURL)")
            return "0"
        }
        
        guard let version = components.path.components(separatedBy: "\(packageName)@").last?.components(separatedBy: "/").first else {
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|package:\(packageName)|split version from realURL error. realURL = \(realURL)")
            return "0"
        }
        
        return version
    }
    
    private override init() {
        super.init()
    }
}

// MARK: - Request
extension UPServiceEnableManager {
    @objc public static func updateXiaouEnableConfig(_ originalURL: String?, params: [String: Any]?) {
        if !isDeviceDetailPage(originalURL, params: params) {
            return
        }
        
        let devicePageInfo = getDevicePageInfo(originalURL, params: params)
        
        guard let deviceId = devicePageInfo.deviceId else { return }
        guard let packageName = devicePageInfo.pkgName else { return }
        
        let request = UPServiceEnableRequest(packageName, deviceId)
        request.start { response in
            guard let respInfo = response as? [AnyHashable: Any] else {
                uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package:\(packageName)|request response is not dictionary(response: \(response.description)")
                return
            }
            
            let respCode = respInfo["retCode"] as! String
            if respCode == "00000" {
                let config = respInfo["data"] as? [String: Any]
                uplog.UPPrintInfo(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package: \(packageName)|response data: \(String(describing: config))")
                saveConfig(respInfo["data"] as? [String: Any])
            } else {
                uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package: \(packageName)|request failed: \(respInfo["retInfo"] as! String)")
            }
        } failure: { error, _ in
            uplog.UPPrintError(moduleName: XY_MODULE_NAME, message: "[xiaou]|deviceId: \(deviceId), package: \(packageName)|request failed: \(error.localizedDescription)")
        }
    }
    
    private static func saveConfig(_ configInfo: [String: Any]?) {
        guard let config = configInfo else { return }
        let deviceId = config["deviceId"] as? String ?? ""
        let pkgVersion = config["packageVersion"] as? String ?? "0"
        let enabled = config["enable"] as? Bool ?? false
        
        let enableInfo: [String : Any] = [
            "enable": enabled,
            "packageVersion": pkgVersion
        ]
        
        guard let data = try? JSONSerialization.data(withJSONObject: enableInfo, options: .fragmentsAllowed) else { return }
        guard let storageValue = String(data: data, encoding: .utf8) else { return }
        
        let storageKey = "is_xiaou_enabled_\(deviceId)"
        UPStorage.putStringValue(storageValue, name: storageKey)
    }
}
