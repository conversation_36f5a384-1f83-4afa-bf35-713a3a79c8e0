//
//  UPServiceFloatViewManager.h
//  mainbox
//
//  Created by haier on 2021/4/21.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "UPServiceConstant.h"

NS_ASSUME_NONNULL_BEGIN

@class UPServiceFloatView, UPServiceFloatBubbleView;

@interface UPServiceFloatViewManager : NSObject

+ (instancetype)shareManager;

@property (nonatomic, strong, readonly) UPServiceFloatView *floatView;

/// 标记是否打开小优语音助手(智能客服)页面
@property (nonatomic) BOOL isShowingTargetPage;

/**
 *  标记小优悬浮按钮是否在屏幕右侧。
 *  悬浮按钮中点x坐标 > (superview.width/2)为true, 否则为false。
 *  在 -[UPServiceFloatView setFrame:]方法中实时更新。
 */
@property (nonatomic) BOOL isFloatingRightSide;

/// 冒泡View
@property (nonatomic, strong, readonly) UPServiceFloatBubbleView *bubbleView;

/// 是否正在显示冒泡。每次获取都会实时计算,冒泡宽度大于悬浮按钮宽度为true,否则为false
//@property (nonatomic, readonly) BOOL isBubbleShowing;

- (void)setFloatViewHidden:(BOOL)isHidden;

- (void)addXiaoUFloatViewTo:(UIView *)superView;

/// 获取主题类型
- (UPServiceTheme)getThemeType;

@end

NS_ASSUME_NONNULL_END
