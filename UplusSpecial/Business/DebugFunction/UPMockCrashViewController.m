//
//  UPMockCrashViewController.m
//  UplusSpecial
//
//  Created by YaoSixu on 2023/1/31.
//

#import "UPMockCrashViewController.h"
#import <UPCrashDefend/UPCrashDefendManage.h>

@interface UPMockCrashViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *headView;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UISwitch *statusSwitch;
@property (nonatomic, copy) NSString *titleInfo;

@property (nonatomic, copy) NSArray *crashInfos;

@end

@implementation UPMockCrashViewController {
    NSString *cellIdentifier;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    cellIdentifier = @"cellIdentifier";
    self.crashInfos = @[ @"模拟未实现方法崩溃:mockUnrecognizedCrash" ];

    if ([UPCrashDefendManage sharedInstance].defendWhenDebug) {
        self.titleInfo = @"崩溃拦截已关闭";
    }
    else {
        self.titleInfo = @"崩溃拦截已开启";
    }

    [self addSubView];
}


- (void)addSubView
{
    CGRect headViewBounds = CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, 50);
    self.headView = [[UIView alloc] initWithFrame:headViewBounds];
    self.statusLabel = [[UILabel alloc] initWithFrame:CGRectMake(10, 15, 200, 30)];

    self.statusLabel.text = self.titleInfo;
    self.statusSwitch = [[UISwitch alloc] initWithFrame:CGRectMake(UIScreen.mainScreen.bounds.size.width - 61, 10, 51, 31)];
    [self.statusSwitch setOn:[UPCrashDefendManage sharedInstance].defendWhenDebug];
    [self.statusSwitch addTarget:self action:@selector(changeDefendStatus) forControlEvents:UIControlEventValueChanged];

    [self.headView addSubview:self.statusLabel];
    [self.headView addSubview:self.statusSwitch];

    self.tableView = [[UITableView alloc] initWithFrame:UIScreen.mainScreen.bounds];
    self.tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.view addSubview:self.tableView];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:cellIdentifier];
    self.tableView.tableHeaderView = self.headView;
}

//MARK: - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.crashInfos.count;
}


- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIdentifier forIndexPath:indexPath];
    cell.textLabel.text = self.crashInfos[indexPath.row];
    cell.textLabel.numberOfLines = 0;
    return cell;
}
//MARK: - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    switch (indexPath.section) {
        case 0:
            [self responseUnknowSelector];
            break;
        default:
            break;
    }
}


//MARK: - Custom Action
- (void)changeDefendStatus
{
    [UPCrashDefendManage sharedInstance].defendWhenDebug = self.statusSwitch.isOn;
    if (self.statusSwitch.isOn) {
        self.titleInfo = @"崩溃拦截已关闭";
    }
    else {
        self.titleInfo = @"崩溃拦截已开启";
    }
}

//MARK: 方法相关
- (void)responseUnknowSelector
{
    [self.view performSelector:@selector(mockUnrecognizedCrash)];
}


//MARK: - Getter&Setter
- (void)setTitleInfo:(NSString *)titleInfo
{
    _titleInfo = titleInfo;
    self.statusLabel.text = titleInfo;
}

@end
