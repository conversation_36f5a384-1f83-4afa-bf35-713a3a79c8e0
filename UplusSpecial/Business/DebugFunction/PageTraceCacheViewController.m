//
//  PageTraceCacheViewController.m
//  mainbox
//
//  Created by <PERSON> on 2019/8/27.
//

#import "PageTraceCacheViewController.h"
#import <QuickLook/QuickLook.h>

@interface PageTraceCacheViewController () <QLPreviewControllerDelegate, QLPreviewControllerDataSource>
@property (nonatomic, strong) NSArray *fileNames;
@property (nonatomic, assign) NSInteger selectedIndex;
- (void)initializeTableItems;
@end

@implementation PageTraceCacheViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    [self initializeTableItems];
}

- (void)initializeTableItems
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *contents = [fileManager contentsOfDirectoryAtPath:self.path error:NULL];
    if (![contents isKindOfClass:[NSArray class]]) {
        return;
    }
    self.fileNames = [contents sortedArrayUsingComparator:^NSComparisonResult(id _Nonnull obj1, id _Nonnull obj2) {
      return [obj2 compare:obj1];
    }];
    ;
}

#pragma mark - Table view data source
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.fileNames.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"PageTraceCell"];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"PageTraceCell"];
    }
    cell.textLabel.text = self.fileNames[indexPath.row];
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    _selectedIndex = indexPath.row;
    QLPreviewController *previewController = [[QLPreviewController alloc] init];
    previewController.dataSource = self;
    previewController.delegate = self;
    [self.navigationController pushViewController:previewController animated:YES];
}

#pragma mark - QLPreviewControllerDataSource
// Returns the number of items that the preview controller should preview
- (NSInteger)numberOfPreviewItemsInPreviewController:(QLPreviewController *)previewController
{
    return 1;
}

- (void)previewControllerDidDismiss:(QLPreviewController *)controller
{
    // if the preview dismissed (done button touched), use this method to post-process previews
}

// returns the item that the preview controller should preview
- (id<QLPreviewItem>)previewController:(QLPreviewController *)previewController previewItemAtIndex:(NSInteger)idx
{
    NSString *fileName = self.fileNames[_selectedIndex];
    NSString *filePath = [self.path stringByAppendingPathComponent:fileName];
    NSURL *fileURL = [NSURL fileURLWithPath:filePath];
    return fileURL;
}


@end
