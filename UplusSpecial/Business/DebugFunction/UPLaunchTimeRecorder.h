//
//  UPLaunchTimeRecorder.h
//  UPCore
//
//  Created by 张虎 on 2020/4/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern void recordStartPointOfSnippet(const char *snippetName);
extern void recordEndPointOfSnippet(const char *snippetName);

/// App 启动时间记录器
@interface UPLaunchTimeRecorder : NSObject

@property (nonatomic, readonly, class) UPLaunchTimeRecorder *sharedInstance;
@property (nonatomic, readonly) NSMutableArray<NSString *> *records;

//- (void)recordStageStartPoint:(NSString *)stageName;

- (void)recordStartPointOfSnippet:(NSString *)snippetName;
- (void)recordEndPointOfSnippet:(NSString *)snippetName;

- (void)stopRecord;

@end

NS_ASSUME_NONNULL_END
