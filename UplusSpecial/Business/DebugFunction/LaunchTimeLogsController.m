//
//  LaunchTimeLogsController.m
//  mainbox
//
//  Created by 张虎 on 2020/4/28.
//

#import "LaunchTimeLogsController.h"
#import "UPLaunchTimeRecorder.h"

@implementation LaunchTimeLogsController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"Launch Time Logs";
    [self.tableView registerClass:UITableViewCell.class forCellReuseIdentifier:@"Cell"];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return UPLaunchTimeRecorder.sharedInstance.records.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"Cell" forIndexPath:indexPath];
    cell.textLabel.numberOfLines = 0;
    cell.textLabel.text = UPLaunchTimeRecorder.sharedInstance.records[indexPath.row];
    return cell;
}

@end
