//
//  DebuggerConfigViewController.m
//  mainbox
//
//  Created by <PERSON> on 2018/10/11.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DebuggerConfigViewController.h"
#import <UPCore/UPContext.h>
#import <UPLog/UPLog.h>
#import <UPVDN/Page.h>
#import <upnetwork/UPNetwork.h>
#import <UPTools/UPMacros.h>
#import <uplog/UPLogUploadLogModel.h>
#import <uplog/UPPermenantThread.h>
#import <uSDK/uSDKManager.h>
#import <uAnalytics/uAnalytics.h>
#import <UPResource/UPResourceManager.h>
#import "MBMainBoxModule.h"
#import <UPPageTrace/UPPageTraceInjection.h>
#import <UPTools/KVNProgressShow.h>
#import "PageTraceCacheViewController.h"
#import <UPResource/SpecifyResourceVersionPatcher.h>
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <uplog/UPLogInjection.h>
#import <upnetwork/UPNetworkSettings.h>
#import "LaunchTimeLogsController.h"
#import "UPMockCrashViewController.h"
#import <UPStorage/UPStorage.h>
#import <pthread.h>
#import <UPPush/UPPushManager.h>

NSUInteger SWITCH_TAG_DEBUG_MODE = 1;
NSUInteger SWITCH_TAG_DEVICE_TEST = 2;
NSUInteger SWITCH_TAG_Test = 3;
NSUInteger SWITCH_TAG_FULLLOGS = 4;
NSUInteger SWITCH_TAG_WEBCONTENT = 5;
NSUInteger SWITCH_TAG_WEBHTTP = 6;

NSString *const kDEV_MODE_STATE = @"debug/dev_mode_state";
NSString *const WebContentKey = @"isEnableWebContent";
NSString *const WebHttpKey = @"isEnableWebHttp";

#define H5CONTAINERACTIONSHEET 20200306
#define RGBCOLOR(r, g, b)              \
    [UIColor colorWithRed:(r) / 255.0f \
                    green:(g) / 255.0f \
                     blue:(b) / 255.0f \
                    alpha:1]

typedef NS_ENUM(NSInteger, H5Type) {
    H5Type_NONE = -1, //无
    H5Type_Nebula,
    //    H5Type_UPH5Container = 5
};

@interface DebuggerConfigViewController () <UIActionSheetDelegate>

@property (nonatomic, strong) UIScrollView *scrollView; //
@property (nonatomic, strong) UIView *contentView; //

@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) UILabel *h5TitleLabel;
@property (nonatomic, strong) UIButton *h5SelectBtn;
@property (nonatomic, strong) UITextField *h5TextField;
@property (nonatomic, strong) UIButton *h5SkipBtn;
@property (nonatomic, assign) H5Type h5Tag;

@property (nonatomic, copy) NSString *host;
@property (nonatomic, copy) NSString *port;
@property (nonatomic, copy) NSString *path;
@property (nonatomic, strong) NSMutableArray *threadList;

- (void)initializeSwitchViews;
- (IBAction)onSwitch:(UISwitch *)sender;
- (IBAction)exportPageTraceData:(UIButton *)sender;
- (void)setDevLogWithSwithValueChange:(BOOL)on;

@end

@implementation DebuggerConfigViewController

- (UIScrollView *)scrollView
{
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
        _scrollView.scrollEnabled = YES;
        _scrollView.alwaysBounceVertical = YES;
        [self.view addSubview:_scrollView];
    }
    return _scrollView;
}

- (UIView *)contentView
{
    if (!_contentView) {
        _contentView = [[UIView alloc] initWithFrame:self.view.bounds];
    }
    return _contentView;
}

#pragma mark - Non-Public Methods
- (UIView *)setupDebuggerModeView
{
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    label.textAlignment = NSTextAlignmentLeft;
    label.textColor = RGBCOLOR(102, 102, 102);
    label.backgroundColor = [UIColor clearColor];
    label.text = [NSString
        stringWithFormat:@"调试模式(%@)",
                         NSBundle.mainBundle.infoDictionary[@"CFBundleVersion"]];
    [self.contentView addSubview:label];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [label.topAnchor constraintEqualToAnchor:self.contentView.topAnchor],
        [label.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor
                                            constant:15 * SCREEN_SCALE_375],
        [label.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-70 - 15 * SCREEN_SCALE_375],
        [label.heightAnchor constraintEqualToConstant:30]
    ]];

    UISwitch *switchItem = [[UISwitch alloc] initWithFrame:CGRectZero];
    switchItem.tag = SWITCH_TAG_DEBUG_MODE;
    [switchItem setOn:UPContext.sharedInstance.isDebugMode];
    [switchItem addTarget:self
                   action:@selector(onSwitch:)
         forControlEvents:UIControlEventValueChanged];
    [self.contentView addSubview:switchItem];

    switchItem.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [switchItem.centerYAnchor constraintEqualToAnchor:label.centerYAnchor],
        [switchItem.widthAnchor constraintEqualToConstant:70],
        [switchItem.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-10 * SCREEN_SCALE_375],
        [switchItem.heightAnchor constraintEqualToConstant:30]
    ]];
    return label;
}

- (UIView *)setupGrayTestViews:(UIView *)topView
{
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    label.textAlignment = NSTextAlignmentLeft;
    label.textColor = RGBCOLOR(102, 102, 102);
    label.backgroundColor = [UIColor clearColor];
    label.text = @"启用灰度模式";
    [self.contentView addSubview:label];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [label.leadingAnchor constraintEqualToAnchor:topView.leadingAnchor],
        [label.topAnchor constraintEqualToAnchor:topView.bottomAnchor
                                        constant:20],
        [label.trailingAnchor constraintEqualToAnchor:topView.trailingAnchor],
        [label.heightAnchor constraintEqualToConstant:30]
    ]];

    UISwitch *switchItem1 = [[UISwitch alloc] initWithFrame:CGRectZero];
    switchItem1.tag = SWITCH_TAG_DEVICE_TEST;
    [switchItem1 setOn:UPContext.sharedInstance.isGrayscaleMode];
    [switchItem1 addTarget:self
                    action:@selector(onSwitch:)
          forControlEvents:UIControlEventValueChanged];
    [self.contentView addSubview:switchItem1];
    switchItem1.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [switchItem1.centerYAnchor constraintEqualToAnchor:label.centerYAnchor],
        [switchItem1.widthAnchor constraintEqualToConstant:70],
        [switchItem1.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-10 * SCREEN_SCALE_375],
        [switchItem1.heightAnchor constraintEqualToConstant:30]
    ]];
    return label;
}

- (UIView *)setupTestViews:(UIView *)topView
{
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    label.textAlignment = NSTextAlignmentLeft;
    label.textColor = RGBCOLOR(102, 102, 102);
    label.backgroundColor = [UIColor clearColor];
    label.text = @"启用测试模式";
    [self.contentView addSubview:label];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [label.leadingAnchor constraintEqualToAnchor:topView.leadingAnchor],
        [label.topAnchor constraintEqualToAnchor:topView.bottomAnchor
                                        constant:20],
        [label.trailingAnchor constraintEqualToAnchor:topView.trailingAnchor],
        [label.heightAnchor constraintEqualToConstant:30]
    ]];

    UISwitch *switchItem1 = [[UISwitch alloc] initWithFrame:CGRectZero];
    switchItem1.tag = SWITCH_TAG_Test;
    [switchItem1 setOn:UPContext.sharedInstance.isTestMode];
    [switchItem1 addTarget:self
                    action:@selector(onSwitch:)
          forControlEvents:UIControlEventValueChanged];
    [self.contentView addSubview:switchItem1];
    switchItem1.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [switchItem1.centerYAnchor constraintEqualToAnchor:label.centerYAnchor],
        [switchItem1.widthAnchor constraintEqualToConstant:70],
        [switchItem1.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-10 * SCREEN_SCALE_375],
        [switchItem1.heightAnchor constraintEqualToConstant:30]
    ]];
    return label;
}
- (UIView *)setupFullLogViews:(UIView *)topView
{
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    label.textAlignment = NSTextAlignmentLeft;
    label.textColor = RGBCOLOR(102, 102, 102);
    label.backgroundColor = [UIColor clearColor];
    label.text = @"启用全量日志模式";
    [self.contentView addSubview:label];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [label.leadingAnchor constraintEqualToAnchor:topView.leadingAnchor],
        [label.topAnchor constraintEqualToAnchor:topView.bottomAnchor
                                        constant:20],
        [label.trailingAnchor constraintEqualToAnchor:topView.trailingAnchor],
        [label.heightAnchor constraintEqualToConstant:30]
    ]];

    UISwitch *switchItem1 = [[UISwitch alloc] initWithFrame:CGRectZero];
    switchItem1.tag = SWITCH_TAG_FULLLOGS;
    [switchItem1 setOn:UPLogInjection.getInstance.upLog.getFullLogsStatus];
    [switchItem1 addTarget:self
                    action:@selector(onSwitch:)
          forControlEvents:UIControlEventValueChanged];
    [self.contentView addSubview:switchItem1];
    switchItem1.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [switchItem1.centerYAnchor constraintEqualToAnchor:label.centerYAnchor],
        [switchItem1.widthAnchor constraintEqualToConstant:70],
        [switchItem1.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-10 * SCREEN_SCALE_375],
        [switchItem1.heightAnchor constraintEqualToConstant:30]
    ]];
    return label;
}

- (UIView *)setupWebContentViews:(UIView *)topView
{
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    label.textAlignment = NSTextAlignmentLeft;
    label.textColor = RGBCOLOR(102, 102, 102);
    label.backgroundColor = [UIColor clearColor];
    label.text = @"显示新容器标识";
    [self.contentView addSubview:label];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [label.leadingAnchor constraintEqualToAnchor:topView.leadingAnchor],
        [label.topAnchor constraintEqualToAnchor:topView.bottomAnchor
                                        constant:20],
        [label.trailingAnchor constraintEqualToAnchor:topView.trailingAnchor],
        [label.heightAnchor constraintEqualToConstant:30]
    ]];

    UISwitch *switchItem1 = [[UISwitch alloc] initWithFrame:CGRectZero];
    switchItem1.tag = SWITCH_TAG_WEBCONTENT;
    BOOL kIsOn = [[NSUserDefaults standardUserDefaults] boolForKey:WebContentKey];
    [switchItem1 setOn:kIsOn];
    [switchItem1 addTarget:self
                    action:@selector(onSwitch:)
          forControlEvents:UIControlEventValueChanged];
    [self.contentView addSubview:switchItem1];
    switchItem1.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [switchItem1.centerYAnchor constraintEqualToAnchor:label.centerYAnchor],
        [switchItem1.widthAnchor constraintEqualToConstant:70],
        [switchItem1.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-10 * SCREEN_SCALE_375],
        [switchItem1.heightAnchor constraintEqualToConstant:30]
    ]];
    return label;
}

- (UIView *)setupWebHttpViews:(UIView *)topView
{
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    label.textAlignment = NSTextAlignmentLeft;
    label.textColor = RGBCOLOR(102, 102, 102);
    label.backgroundColor = [UIColor clearColor];
    label.text = @"打开容器调试(允许加载http)";
    [self.contentView addSubview:label];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [label.leadingAnchor constraintEqualToAnchor:topView.leadingAnchor],
        [label.topAnchor constraintEqualToAnchor:topView.bottomAnchor
                                        constant:20],
        [label.trailingAnchor constraintEqualToAnchor:topView.trailingAnchor],
        [label.heightAnchor constraintEqualToConstant:30]
    ]];

    UISwitch *switchItem1 = [[UISwitch alloc] initWithFrame:CGRectZero];
    switchItem1.tag = SWITCH_TAG_WEBHTTP;
    BOOL kIsOn = [[NSUserDefaults standardUserDefaults] boolForKey:WebHttpKey];
    [switchItem1 setOn:kIsOn];
    [switchItem1 addTarget:self
                    action:@selector(onSwitch:)
          forControlEvents:UIControlEventValueChanged];
    [self.contentView addSubview:switchItem1];
    switchItem1.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [switchItem1.centerYAnchor constraintEqualToAnchor:label.centerYAnchor],
        [switchItem1.widthAnchor constraintEqualToConstant:70],
        [switchItem1.trailingAnchor
            constraintEqualToAnchor:self.contentView.trailingAnchor
                           constant:-10 * SCREEN_SCALE_375],
        [switchItem1.heightAnchor constraintEqualToConstant:30]
    ]];
    return label;
}

- (void)initializeSwitchViews
{
    UIView *label = [self setupDebuggerModeView];
    UIView *label1 = [self setupGrayTestViews:label];
    UIView *testLabel = [self setupTestViews:label1];
    UIView *fullLogsLabel = [self setupFullLogViews:testLabel];
    UIView *webContentViews = [self setupWebContentViews:fullLogsLabel];
    UIView *webHttpView = [self setupWebHttpViews:webContentViews];

    UIButton *exportButton = [self createActionButton];
    exportButton.frame =
        CGRectMake(10, 284, CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [exportButton setTitle:@"查看打点数据" forState:UIControlStateNormal];
    [exportButton addTarget:self
                     action:@selector(exportPageTraceData:)
           forControlEvents:UIControlEventTouchUpInside];
    //    [self.contentView addSubview:exportButton];

    UIButton *uplusapiQuality = [self createActionButton];
    uplusapiQuality.frame =
        CGRectMake(10, 250 + webHttpView.frame.size.height + 40,
                   CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [uplusapiQuality setTitle:@"模块测试页面" forState:UIControlStateNormal];
    uplusapiQuality.tag = 1000;
    [uplusapiQuality addTarget:self
                        action:@selector(containerChoice:)
              forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:uplusapiQuality];

    UIButton *uplusTest = [self createActionButton];
    uplusTest.frame = CGRectMake(10, CGRectGetMaxY(uplusapiQuality.frame) + 20,
                                 CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    uplusTest.tag = 1001;
    [uplusTest setTitle:@"容器测试页面" forState:UIControlStateNormal];
    [uplusTest addTarget:self
                  action:@selector(containerChoice:)
        forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:uplusTest];

    CGFloat pushBtnWidth = (CGRectGetWidth(self.view.frame) - 10 * 3) / 2;
    UIButton *mpaasDeviceIdBtn = [self createActionButton];
    mpaasDeviceIdBtn.frame =
        CGRectMake(10, CGRectGetMaxY(uplusTest.frame) + 20, pushBtnWidth, 30);
    [mpaasDeviceIdBtn setTitle:@"ClientID" forState:UIControlStateNormal];
    [mpaasDeviceIdBtn addTarget:self
                         action:@selector(showMpaasDeviceId)
               forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:mpaasDeviceIdBtn];

    UIButton *pushIdBtn = [self createActionButton];
    pushIdBtn.frame = CGRectMake(
        pushBtnWidth + 20, CGRectGetMaxY(uplusTest.frame) + 20, pushBtnWidth, 30);
    [pushIdBtn setTitle:@"PushID" forState:UIControlStateNormal];
    [pushIdBtn addTarget:self
                  action:@selector(showDevicePushId)
        forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:pushIdBtn];

    UIButton *uplusApiTestBtn = [self createActionButton];
    uplusApiTestBtn.frame =
        CGRectMake(10, CGRectGetMaxY(mpaasDeviceIdBtn.frame) + 20,
                   CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [uplusApiTestBtn setTitle:@"UplusAPI Test" forState:UIControlStateNormal];
    [uplusApiTestBtn addTarget:self
                        action:@selector(openUplusAPITest)
              forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:uplusApiTestBtn];

    UIButton *uploadBtn = [self createActionButton];
    uploadBtn.frame = CGRectMake(10, CGRectGetMaxY(uplusApiTestBtn.frame) + 20,
                                 CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [uploadBtn setTitle:@"上传日志" forState:UIControlStateNormal];
    [uploadBtn addTarget:self
                  action:@selector(uploadLog:)
        forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:uploadBtn];

    UIButton *flutterBtn = [self createActionButton];
    flutterBtn.frame = CGRectMake(10, CGRectGetMaxY(uploadBtn.frame) + 20,
                                  CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [flutterBtn setTitle:@"跳转flutter" forState:UIControlStateNormal];
    [flutterBtn addTarget:self
                   action:@selector(goFlutter:)
         forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:flutterBtn];

    NSString *crashPlatform = [UPStorage getMemoryString:@"CrashReportPlatform" defaultValue:@"Unkown"];
    NSString *title = [NSString stringWithFormat:@"mockCrash(%@)", crashPlatform];

    UIButton *crashBtn = [self createActionButton];
    [crashBtn setTitle:title forState:UIControlStateNormal];
    [crashBtn addTarget:self
                  action:@selector(mockCrash:)
        forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:crashBtn];

    if ([crashPlatform isEqualToString:@"Bugly"]) {
        crashBtn.titleLabel.font = [UIFont systemFontOfSize:14 * SCREEN_SCALE_375];
        crashBtn.frame = CGRectMake(10, CGRectGetMaxY(flutterBtn.frame) + 20, pushBtnWidth, 30);

        UIButton *anrButton = [self createActionButton];
        anrButton.frame = CGRectMake(pushBtnWidth + 20, CGRectGetMaxY(flutterBtn.frame) + 20, pushBtnWidth, 30);
        anrButton.titleLabel.font = [UIFont systemFontOfSize:14 * SCREEN_SCALE_375];
        [anrButton setTitle:@"mockANR(watchdog)" forState:UIControlStateNormal];
        [anrButton addTarget:self action:@selector(mockANR:) forControlEvents:UIControlEventTouchUpInside];
        [self.contentView addSubview:anrButton];
    }
    else {
        crashBtn.frame = CGRectMake(10, CGRectGetMaxY(flutterBtn.frame) + 20,
                                    CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    }

    UIButton *exitCrash = [self createActionButton];
    exitCrash.frame = CGRectMake(10, CGRectGetMaxY(crashBtn.frame) + 20,
                                 CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [exitCrash setTitle:@"exitCrash" forState:UIControlStateNormal];
    [exitCrash addTarget:self
                  action:@selector(exitCrash)
        forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:exitCrash];

    UIButton *mockDefendCrashBtn = [self createActionButton];
    mockDefendCrashBtn.frame =
        CGRectMake(10, CGRectGetMaxY(exitCrash.frame) + 20,
                   CGRectGetWidth(self.view.frame) - 10 * 2, 30);
    [mockDefendCrashBtn setTitle:@"崩溃拦截" forState:UIControlStateNormal];
    [mockDefendCrashBtn addTarget:self
                           action:@selector(gotoMockCrashDefend:)
                 forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:mockDefendCrashBtn];

    [self addH5InputURL:mockDefendCrashBtn];

    self.navigationItem.rightBarButtonItem =
        [[UIBarButtonItem alloc] initWithTitle:@"Launch logs"
                                         style:UIBarButtonItemStylePlain
                                        target:self
                                        action:@selector(openLaunchTimeLogs)];
}

- (void)addH5InputURL:(UIView *)supView
{
    UIView *bgView = UIView.new;
    bgView.frame =
        CGRectMake(0, CGRectGetMaxY(supView.frame) + 20 * SCREEN_SCALE_375,
                   CGRectGetWidth(self.view.frame), 80 * SCREEN_SCALE_375);
    [self.contentView addSubview:bgView];

    self.h5TitleLabel.frame = CGRectMake(
        10 * SCREEN_SCALE_375, 0, 150 * SCREEN_SCALE_375, 30 * SCREEN_SCALE_375);
    [bgView addSubview:self.h5TitleLabel];

    self.h5SelectBtn.frame =
        CGRectMake(CGRectGetMaxX(self.h5TitleLabel.frame) + 10, 0,
                   150 * SCREEN_SCALE_375, 30 * SCREEN_SCALE_375);
    [bgView addSubview:self.h5SelectBtn];

    self.h5TextField.frame = CGRectMake(
        10 * SCREEN_SCALE_375, CGRectGetMaxY(self.h5TitleLabel.frame) + 10,
        SCREEN_WIDTH - 100 * SCREEN_SCALE_375, 30 * SCREEN_SCALE_375);
    [bgView addSubview:self.h5TextField];

    self.h5SkipBtn.frame =
        CGRectMake(CGRectGetMaxX(self.h5TextField.frame) + 10,
                   CGRectGetMaxY(self.h5TitleLabel.frame) + 10,
                   70 * SCREEN_SCALE_375, 30 * SCREEN_SCALE_375);
    [bgView addSubview:self.h5SkipBtn];

    self.contentView.frame = CGRectMake(
        self.contentView.frame.origin.x, self.contentView.frame.origin.y,
        self.contentView.frame.size.width, CGRectGetMaxY(bgView.frame) + 30);
    self.scrollView.contentSize = self.contentView.frame.size;
    [self.scrollView addSubview:self.contentView];
}

- (UIAlertController *)createUplusapiHostAlert:(NSString *)hostId
                                        portId:(NSString *)portId
                                        pathId:(NSString *)pathId
{
    UIAlertController *alert =
        [UIAlertController alertControllerWithTitle:@"UplusApi Test"
                                            message:@""
                                     preferredStyle:UIAlertControllerStyleAlert];
    [alert
        addTextFieldWithConfigurationHandler:^(UITextField *_Nonnull textField) {
          textField.placeholder = hostId;
          textField.keyboardType = UIKeyboardTypeNumbersAndPunctuation;
          textField.text = self.host;
        }];
    [alert
        addTextFieldWithConfigurationHandler:^(UITextField *_Nonnull textField) {
          textField.placeholder = portId;
          textField.keyboardType = UIKeyboardTypeNumberPad;
          textField.text = self.port;
        }];
    [alert
        addTextFieldWithConfigurationHandler:^(UITextField *_Nonnull textField) {
          textField.placeholder = pathId;
          textField.keyboardType = UIKeyboardTypeURL;
          textField.text = self.path;
        }];
    return alert;
}

- (NSString *)getUplusapiTestUrl
{
    if (!self.host.length || !self.port.length) {
        return nil;
    }
    NSURLComponents *urlComp = NSURLComponents.new;
    urlComp.scheme = @"http";
    urlComp.host = self.host;
    urlComp.port = @([self.port integerValue]);
    if (self.path.length) {
        urlComp.path = self.path;
    }
    return urlComp.URL.absoluteString;
}

- (void)updateAlertMessage:(UIAlertController *)alert
{
    NSString *urlStr = [self getUplusapiTestUrl];
    if (urlStr) {
        alert.message = urlStr;
    }
    else {
        alert.message = @"Invalid URL";
    }
}

- (id)addAlertObserverToken:(UIAlertController *)alert
                     hostId:(NSString *)hostId
                     portId:(NSString *)portId
                     pathId:(NSString *)pathId
{
    __weak typeof(self) weakself = self;
    id token = [[NSNotificationCenter defaultCenter]
        addObserverForName:UITextFieldTextDidChangeNotification
                    object:nil
                     queue:NSOperationQueue.mainQueue
                usingBlock:^(NSNotification *_Nonnull note) {
                  UITextField *textField = note.object;
                  if (hostId && [textField.placeholder isEqualToString:hostId]) {
                      weakself.host = textField.text;
                  }
                  else if (portId &&
                           [textField.placeholder isEqualToString:portId]) {
                      weakself.port = textField.text;
                  }
                  else if (pathId &&
                           [textField.placeholder isEqualToString:pathId]) {
                      weakself.path = textField.text;
                  }
                  [weakself updateAlertMessage:alert];
                }];
    return token;
}

- (void)openUrlWithContainerType:(NSString *)containerType
                   observerToken:(id)token
{
    [[NSNotificationCenter defaultCenter] removeObserver:token];
    [UpVdn goToPage:[self getUplusapiTestUrl]
        flag:VdnPageFlagPush
        parameters:@{
            @"containerType" : containerType
        }
        complete:^(NSDictionary *data) {
        }
        error:^(NSError *error){
        }];
}

- (void)addOpenActions:(UIAlertController *)alert observerToken:(id)token
{
    __weak typeof(self) weakself = self;
    [alert addAction:[UIAlertAction
                         actionWithTitle:@"Open with Nebula"
                                   style:UIAlertActionStyleDefault
                                 handler:^(UIAlertAction *_Nonnull action) {
                                   [weakself openUrlWithContainerType:@"3"
                                                        observerToken:token];
                                 }]];

    [alert addAction:[UIAlertAction actionWithTitle:@"Cancel"
                                              style:UIAlertActionStyleCancel
                                            handler:nil]];
}

- (void)openUplusAPITest
{
    NSString *hostId = @"Host", *portId = @"Port", *pathId = @"Path(Optional)";
    UIAlertController *alert =
        [self createUplusapiHostAlert:hostId
                               portId:portId
                               pathId:pathId];

    [self updateAlertMessage:alert];

    id token = [self addAlertObserverToken:alert
                                    hostId:hostId
                                    portId:portId
                                    pathId:pathId];
    [self addOpenActions:alert observerToken:token];
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)uploadLog:(id)sender
{
    [KVNProgressShow showCustomLoading];
    [[UPLogInjection getInstance]
            .upLog uploadLogFileProgressBlock:^(float progress) {

    }
        finishBlock:^(UPLogUploadLogModel *result) {
          [KVNProgressShow showSuccess:result.retInfo];
        }];
}

- (void)goFlutter:(id)sender
{
    [UpVdn goToPage:@"flutter://TestPage"
               flag:VdnPageFlagPush
         parameters:nil
           complete:nil
              error:nil];
}

static void unsafe_signal_handler(int signo)
{
    /* Try to fetch thread names with the pthread API */
    char name[512];
    NSLog(@"Trying to use the pthread API from a signal handler. Is a deadlock "
          @"coming?");
    pthread_getname_np(pthread_self(), name, sizeof(name));

    // We'll never reach this point. The process will stop here until the OS
    // watchdog
    // kills it in 20+ seconds, or the user force quits it. No crash report (or a
    // partial corrupt
    // one) will be written.
    NSLog(@"We'll never reach this point.");

    exit(1);
}

static void *enable_threading(void *ctx)
{
    return NULL;
}

- (void)mockCrash:(id)sender
{
    // ensure when app crashes there is threads more than 100
    [self createEightyThread];

    /* Remove this line to test your own crash reporter */
    signal(SIGSEGV, unsafe_signal_handler);

    /* We have to use pthread_create() to enable locking in malloc/pthreads/etc --
   * this
       * would happen by default in any real application, as the standard
   * frameworks
       * (such as dispatch) will trigger similar calls into the pthread APIs. */
    pthread_t thr;
    pthread_create(&thr, NULL, enable_threading, NULL);

    /* This is the actual code that triggers a reproducible deadlock; include this
       * in your own app to test a different crash reporter's behavior.
       *
       * While this is a simple test case to reliably trigger a deadlock, it's
   * not necessary
       * to crash inside of a pthread call to trigger this bug. Any thread
   * sitting inside of
       * pthread() at the time a crash occurs would trigger the same deadlock.
   */
    pthread_getname_np(pthread_self(), (char *)0x1, 1);
}

- (void)mockANR:(UIButton *)button
{
    CGFloat imgWidth = 90;
    CGFloat imgHeight = 60;
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.frame = CGRectMake(self.view.bounds.size.width - 10 - imgWidth, CGRectGetMinY(button.frame) - 10 - imgHeight, imgWidth, imgHeight);
    imageView.layer.cornerRadius = 10;
    imageView.layer.masksToBounds = YES;
    imageView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    [self.contentView addSubview:imageView];

    __weak UIImageView *imgView = imageView;
    dispatch_async(dispatch_get_main_queue(), ^{
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(30 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [imgView removeFromSuperview];
      });
      NSURL *url = [NSURL URLWithString:@"https://d.ifengimg.com/q100/img1.ugc.ifeng.com/newugc/20190119/10/wemedia/abbab6554fa54232bec645b46e6e7bb3f0e4cc5b_size2326_w3000_h2000.JPG"];
      NSData *imgData = [NSData dataWithContentsOfURL:url];
      imgView.image = [UIImage imageWithData:imgData];
    });
}

- (void)exitCrash
{
    [[NSNotificationCenter defaultCenter]
        postNotificationName:@"UplusUserExitMessage"
                      object:nil];
    [self mockCrash:nil];
}

- (void)gotoMockCrashDefend:(id)sender
{
    UPMockCrashViewController *mockCrashVC =
        [[UPMockCrashViewController alloc] init];
    [self.navigationController pushViewController:mockCrashVC animated:YES];
}

- (void)createEightyThread
{
    for (int index = 0; index < 80; index++) {
        UPPermenantThread *kUPPermenantThread = [[UPPermenantThread alloc] init];
        [self.threadList addObject:kUPPermenantThread];
    }
}

- (void)openLaunchTimeLogs
{
    LaunchTimeLogsController *controller =
        [[LaunchTimeLogsController alloc] initWithStyle:UITableViewStylePlain];
    [self.navigationController pushViewController:controller animated:YES];
}

- (void)openTsTestHtml
{
    NSString *str = self.textView.text;
    if (!str) {
        return;
    }
    str = [str stringByReplacingOccurrencesOfString:@" " withString:@""];
    if ([str isEqualToString:@""]) {
        return;
    }

    [UpVdn goToPage:str
               flag:VdnPageFlagPush
         parameters:@{
             @"hidesBottomBarWhenPushed" : @"1"
         }
           complete:nil
              error:nil];
}

- (UIButton *)createActionButton
{
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.titleLabel.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE];
    [button setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    button.layer.borderColor = [UIColor grayColor].CGColor;
    button.layer.cornerRadius = 10.0;
    button.layer.borderWidth = 1.0;
    return button;
}
// 展示pushId
- (void)showDevicePushId
{

    UIAlertController *vc = [UIAlertController
        alertControllerWithTitle:@"pushId"
                         message:[UPPushManager instance].push.pushID
                  preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *action = [UIAlertAction
        actionWithTitle:@"复制"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) {
                  NSString *pushId = [UPPushManager instance].push.pushID;
                  if (pushId) {
                      UIPasteboard *pb = [UIPasteboard generalPasteboard];
                      pb.string = pushId;
                  }
                }];
    [vc addAction:action];
    [self presentViewController:vc animated:YES completion:nil];
}

- (void)showMpaasDeviceId
{
    UIAlertController *vc = [UIAlertController
        alertControllerWithTitle:@"clientId"
                         message:[UPNetworkSettings sharedSettings].clientID
                  preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *action = [UIAlertAction
        actionWithTitle:@"复制"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) {
                  UIPasteboard *pb = [UIPasteboard generalPasteboard];
                  pb.string = [UPNetworkSettings sharedSettings].clientID;
                }];
    [vc addAction:action];
    [self presentViewController:vc animated:YES completion:nil];
}
- (IBAction)onSwitch:(UISwitch *)sender
{
    if (sender.tag == SWITCH_TAG_DEBUG_MODE) {
        UPContext.sharedInstance.isDebugMode = sender.isOn;
        NSString *on = UPContext.sharedInstance.isDebugMode ? UPDevLogModeStateON : UPDevLogModeStateOFF;
        [UPStorage putStringValue:on name:kDEV_MODE_STATE];
        [self setDevLogWithSwithValueChange:sender.isOn];
    }
    else if (sender.tag == SWITCH_TAG_DEVICE_TEST) {
        UPContext.sharedInstance.isGrayscaleMode = sender.isOn;
    }
    else if (sender.tag == SWITCH_TAG_Test) {
        UPContext.sharedInstance.isTestMode = sender.isOn;
    }
    else if (sender.tag == SWITCH_TAG_FULLLOGS) {
        [[UPLogInjection getInstance].upLog enableFullLogs:sender.isOn];
        NSString *tost = [NSString
            stringWithFormat:@"全量日志模式已%@，App即将退出",
                             [UPLogInjection getInstance].upLog.getFullLogsStatus ? @"开启" : @"关闭"];
        [KVNProgressShow showInfo:tost];
        dispatch_after(
            dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)),
            dispatch_get_main_queue(), ^{
              exit(0);
            });
    }
    else if (sender.tag == SWITCH_TAG_WEBCONTENT) {
        [[NSUserDefaults standardUserDefaults] setObject:@(sender.isOn)
                                                  forKey:WebContentKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        NSString *tost =
            [NSString stringWithFormat:@"显示Hainer容器标识已%@",
                                       sender.on ? @"开启" : @"关闭"];
        [KVNProgressShow showInfo:tost];
    }
    else if (sender.tag == SWITCH_TAG_WEBHTTP) {
        [[NSUserDefaults standardUserDefaults] setObject:@(sender.isOn)
                                                  forKey:WebHttpKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        NSString *tost =
            [NSString stringWithFormat:@"已%@加载http页面",
                                       sender.on ? @"允许" : @"禁止"];
        [KVNProgressShow showInfo:tost];
    }
}

- (IBAction)exportPageTraceData:(UIButton *)sender
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *exportFolderPath =
        [[paths objectAtIndex:0] stringByAppendingPathComponent:@"PTDataExport"];
    NSError *err = NULL;
    [[UPPageTraceInjection getInstance]
            .pageTraceManager exportTraceDataOfSession:nil
                                                toPath:exportFolderPath
                                                 error:&err];
    if (err) {
        NSString *errMsg =
            [NSString stringWithFormat:@"打点数据导出失败！error:%@",
                                       err.localizedDescription];
        [KVNProgressShow showError:errMsg];
        return;
    }
    PageTraceCacheViewController *controller =
        [[PageTraceCacheViewController alloc] init];
    controller.path = exportFolderPath;
    [self.navigationController pushViewController:controller animated:YES];
}

- (IBAction)containerChoice:(UIButton *)sender
{
    if (sender.tag == 1000) { //模块测试页面增加nebula入口
        UIActionSheet *actionSheet =
            [[UIActionSheet alloc] initWithTitle:nil
                                        delegate:self
                               cancelButtonTitle:@"Cancel"
                          destructiveButtonTitle:nil
                               otherButtonTitles:@"nebula", nil];
        actionSheet.tag = sender.tag;
        [actionSheet showInView:self.view];
        return;
    }
    UIActionSheet *actionSheet =
        [[UIActionSheet alloc] initWithTitle:nil
                                    delegate:self
                           cancelButtonTitle:@"Cancel"
                      destructiveButtonTitle:nil
                           otherButtonTitles:@"nebula", nil];
    actionSheet.tag = sender.tag;
    [actionSheet showInView:self.view];
}

- (void)handleH5ContainerActionSheet:(NSInteger)index
{
    self.h5Tag = index;
    if (index == 0) {
        [self.h5SelectBtn setTitle:@"Nebula" forState:UIControlStateNormal];
    }
}

- (NSString *)getUplusapiQualityTestUrl:(NSInteger)actionTag buttonIndex:(NSInteger)index
{
    NSString *url = @"uplusapiTestForAPP";
    NSString *domainUrl = @"mpaas://";
    if (actionTag == 1000) {
        url = @"uplusapiTestForAPP";
    }
    else {
        url = @"UPlusTestTool";
    }
    return [NSString stringWithFormat:@"%@%@", domainUrl, url];
}

- (void)actionSheet:(UIActionSheet *)actionSheet
    clickedButtonAtIndex:(NSInteger)buttonIndex
{
    if (actionSheet.tag == H5CONTAINERACTIONSHEET) {
        [self handleH5ContainerActionSheet:buttonIndex];
        return;
    }

    NSString *url =
        [self getUplusapiQualityTestUrl:actionSheet.tag
                            buttonIndex:buttonIndex];
    if (nil == url) {
        return;
    }

    [UpVdn goToPage:url
               flag:VdnPageFlagPush
         parameters:@{
             @"hidesBottomBarWhenPushed" : @"1",
             @"containerType" : @"3"
         }
           complete:nil
              error:nil];
}

- (void)setDevLogWithSwithValueChange:(BOOL)on
{
    uSDKLogLevelConst uSDKLogLevel = on ? USDK_LOG_DEBUG : USDK_LOG_NONE;
    UALogLevel uAnalyticsLogLevel = on ? UALogLevelDebug : UALogLevelNone;

    [[UPLogInjection getInstance]
            .upLog setLoglevel:on ? UPLogLevelDebug : UPLogLevelInfo];
    [[UPLogInjection getInstance].upLog enableConsoleLog:on];
    [[uSDKManager defaultManager] setLogWithLevel:uSDKLogLevel
        isWriteToFile:NO
        success:^{
        }
        failure:^(NSError *error){
        }];
    [uAnalytics setLogLevel:uAnalyticsLogLevel];
    [KVNProgressShow showInfo:@"部"
                              @"分日志修改需要重启App才能生效！"];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.title = @"日志设置";
    self.h5Tag = H5Type_NONE;
    self.threadList = [NSMutableArray array];
    [self initializeSwitchViews];
}

- (UILabel *)h5TitleLabel
{
    if (!_h5TitleLabel) {
        _h5TitleLabel = [UILabel new];
        _h5TitleLabel.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE_375];
        _h5TitleLabel.textColor = RGBCOLOR(102, 102, 102);
        _h5TitleLabel.text = @"远程h5测试：";
    }
    return _h5TitleLabel;
}

- (UIButton *)h5SelectBtn
{
    if (!_h5SelectBtn) {
        _h5SelectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _h5SelectBtn.titleLabel.font =
            [UIFont systemFontOfSize:15 * SCREEN_SCALE_375];
        [_h5SelectBtn setTitleColor:RGBCOLOR(102, 102, 102)
                           forState:UIControlStateNormal];
        [_h5SelectBtn setTitle:@"无" forState:UIControlStateNormal];
        _h5SelectBtn.layer.borderWidth = .5;
        [_h5SelectBtn addTarget:self
                         action:@selector(clickSelectDiffContainer)
               forControlEvents:UIControlEventTouchUpInside];
    }
    return _h5SelectBtn;
}

- (void)clickSelectDiffContainer
{
    UIActionSheet *actionSheet =
        [[UIActionSheet alloc] initWithTitle:nil
                                    delegate:self
                           cancelButtonTitle:@"Cancel"
                      destructiveButtonTitle:nil
                           otherButtonTitles:@"Nebula", nil];
    actionSheet.tag = H5CONTAINERACTIONSHEET;
    [actionSheet showInView:self.view];
}

- (UITextField *)h5TextField
{
    if (!_h5TextField) {
        _h5TextField = [[UITextField alloc] init];
        _h5TextField.font = [UIFont systemFontOfSize:16 * SCREEN_SCALE_375];
        _h5TextField.textColor = RGBCOLOR(102, 102, 102);
        _h5TextField.placeholder = @"请输入URL，例如：https://www.baidu.com";
        _h5TextField.layer.borderWidth = .5;
    }
    return _h5TextField;
}

- (UIButton *)h5SkipBtn
{
    if (!_h5SkipBtn) {
        _h5SkipBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _h5SkipBtn.titleLabel.font =
            [UIFont systemFontOfSize:15 * SCREEN_SCALE_375];
        [_h5SkipBtn setTitleColor:RGBCOLOR(102, 102, 102)
                         forState:UIControlStateNormal];
        [_h5SkipBtn setTitle:@"跳转" forState:UIControlStateNormal];
        _h5SkipBtn.layer.cornerRadius = 5;
        _h5SkipBtn.layer.borderWidth = .5;
        [_h5SkipBtn addTarget:self
                       action:@selector(goToH5Url)
             forControlEvents:UIControlEventTouchUpInside];
    }
    return _h5SkipBtn;
}

- (void)goToH5Url
{
    NSLog(@"%@", self.h5TextField.text);
    if (self.h5Tag >= 0) {
        NSString *url = self.h5TextField.text;
        [UpVdn goToPage:url
                   flag:VdnPageFlagPush
             parameters:@{
                 @"containerType" :
                     [NSString stringWithFormat:@"%ld", (long)self.h5Tag]
             }
               complete:nil
                  error:nil];
    }
    else {
        [UpVdn goToPage:self.h5TextField.text
                   flag:VdnPageFlagPush
             parameters:nil
               complete:nil
                  error:nil];
    }
}

@end
