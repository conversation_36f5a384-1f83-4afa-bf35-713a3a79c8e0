//
//  UPLaunchTimeRecorder.m
//  UPCore
//
//  Created by 张虎 on 2020/4/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPLaunchTimeRecorder.h"

void recordStartPointOfSnippet(const char *snippetName)
{
    [UPLaunchTimeRecorder.sharedInstance recordStartPointOfSnippet:[NSString stringWithCString:snippetName encoding:NSUTF8StringEncoding]];
}

void recordEndPointOfSnippet(const char *snippetName)
{
    [UPLaunchTimeRecorder.sharedInstance recordEndPointOfSnippet:[NSString stringWithCString:snippetName encoding:NSUTF8StringEncoding]];
}

@interface UPLaunchTimeRecorder ()

@property (nonatomic) BOOL stopped;

@property (nonatomic) NSMutableDictionary<NSString *, NSDate *> *startDateBySnippet;
@property (nonatomic) NSMutableArray<NSString *> *records;

@end

@implementation UPLaunchTimeRecorder

+ (UPLaunchTimeRecorder *)sharedInstance
{
    static UPLaunchTimeRecorder *_instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      _instance = [self new];
    });
    return _instance;
}

- (void)recordStageStartPoint:(NSString *)stageName
{
    if (self.stopped) {
        return;
    }
}

- (void)recordStartPointOfSnippet:(NSString *)snippetName
{
    if (self.stopped) {
        return;
    }
    self.startDateBySnippet[snippetName] = NSDate.date;
}

- (void)recordEndPointOfSnippet:(NSString *)snippetName
{
    if (self.stopped) {
        return;
    }

    NSDate *start = self.startDateBySnippet[snippetName];
    NSTimeInterval result = 0;
    if (start) {
        result = ([NSDate.date timeIntervalSinceDate:start] * 1000) / 1000.;
    }
    else {
        result = -1;
    }
    self.startDateBySnippet[snippetName] = nil;

    [self.records addObject:[NSString stringWithFormat:@"%.2lf s -> %@", result, snippetName]];
}

- (void)stopRecord
{
    self.stopped = YES;
}

- (NSMutableDictionary<NSString *, NSDate *> *)startDateBySnippet
{
    if (!_startDateBySnippet) {
        _startDateBySnippet = [NSMutableDictionary dictionary];
    }
    return _startDateBySnippet;
}

- (NSMutableArray<NSString *> *)records
{
    if (!_records) {
        _records = [NSMutableArray array];
    }
    return _records;
}

@end
