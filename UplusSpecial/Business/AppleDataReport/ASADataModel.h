//
//  ASADataModel.h
//  mainbox
//
//  Created by haier on 2022/5/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ASADataModel : NSObject

@property (copy, nonatomic) NSString *idfaCode;
@property (copy, nonatomic) NSString *iadAttribution;
@property (copy, nonatomic) NSString *iadOrgId;
@property (copy, nonatomic) NSString *iadOrgName;
@property (copy, nonatomic) NSString *iadCampaignId;
@property (copy, nonatomic) NSString *iadCampaignName;
@property (copy, nonatomic) NSString *iadPurchaseDate;
@property (copy, nonatomic) NSString *iadConversionDate;
@property (copy, nonatomic) NSString *iadConversionType;
@property (copy, nonatomic) NSString *iadClickDate;
@property (copy, nonatomic) NSString *iadAdGroupId;
@property (copy, nonatomic) NSString *iadAdGroupName;
@property (copy, nonatomic) NSString *iadCountryOrRegion;
@property (copy, nonatomic) NSString *iadKeyword;
@property (copy, nonatomic) NSString *iadKeywordId;
@property (copy, nonatomic) NSString *iadKeywordMatchType;
@property (copy, nonatomic) NSString *iadAdId;
@property (copy, nonatomic) NSString *iadCreativeSetId;
@property (copy, nonatomic) NSString *iadCreativeSetName;

- (instancetype)initIadWithDiction:(NSDictionary *)dic;
- (instancetype)initASAServiceWithDiction:(NSDictionary *)dic;

@end

NS_ASSUME_NONNULL_END
