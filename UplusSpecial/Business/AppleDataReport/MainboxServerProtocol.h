//
//  MainboxServerProtocol.h
//  AFNetworking
//
//  Created by <PERSON><PERSON><PERSON> on 2020/2/5.
//

#import <Foundation/Foundation.h>
#import "HTTPRequestManager.h"
#import "AFHTTPRequestOperation.h"

NS_ASSUME_NONNULL_BEGIN

@interface MainboxServerProtocol : NSObject
/**
 *  @brief IDFA 收集
 *  @param OnOperation  请求操作回调
 *  @param completion   结果回调
 *  @since ver 2.0.0
 */
+ (void)idfaCollectWithOnOperation:(nullable void (^)(AFHTTPRequestOperation *_Nullable))OnOperation completion:(nullable void (^)(BOOL success))completion;

/**
 ASA归因接口
 */
+ (void)ASARequestwithDic:(NSMutableDictionary *)dic withCompletion:(nullable void (^)(BOOL success))completion;
@end

NS_ASSUME_NONNULL_END
