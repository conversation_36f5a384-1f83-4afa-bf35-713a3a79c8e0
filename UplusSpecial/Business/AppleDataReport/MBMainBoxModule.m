//
//  MBMainBoxModule.m
//  Uplus
//
//  Created by <PERSON> on 13/05/2017.
//  Copyright © 2017 北京海尔广科数字技术有限公司. All rights reserved.
//

#import "MBMainBoxModule.h"
#import <uplog/UPLog.h>
#import <uSDK/uSDKManager.h>
#import <uAnalytics/uAnalytics.h>
#import "MainboxServerProtocol.h"
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <AdSupport/AdSupport.h>
#import <Adservices/Adservices.h>
#import "ASADataModel.h"
#import <MJExtension/MJExtension.h>
#import <upuserdomain/UpUserDomainHolder.h>

NSString *const kUPDevLogModeState = @"devmodestate";
NSString *const UPDevLogModeStateON = @"ON";
NSString *const UPDevLogModeStateOFF = @"OFF";

NSString *const kUPDeviceTestState = @"kUPDeviceTestState";
NSString *const kUPTestState = @"kUPTestState";

#define IS_UPLOAD_ASA_DATA @"isUploadASAData"

@interface MBMainBoxModule () <UpWorkflowTask, UpUserDomainObserver>

@end

@implementation MBMainBoxModule

- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    return stage == UpLaunchStageBeforePrivacy ? self : nil;
}

- (void)onRefreshTokenSuccess:(id<UDAuthDataDelegate>)oauthData
{
    if (@available(iOS 14, *)) {
        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(
                               ATTrackingManagerAuthorizationStatus status) {
          UPLogInfo(@"MainBox",
                    @"requestTrackingAuthorizationWithCompletionHandler:%lu",
                    status);
          if (status == ATTrackingManagerAuthorizationStatusAuthorized) {
              [self idfaCollection];
          }
          else {
          }
        }];
    }
    else {
        [self idfaCollection];
    };
    [self uploadASAData];
}
#pragma mark - UPModuleProtocol
- (void)run
{
    [[UpUserDomainHolder instance].userDomain addObserver:self];
}

- (void)onAppActive
{
    NSString *devmodestate =
        [[NSUserDefaults standardUserDefaults] valueForKey:kUPDevLogModeState];
    if (devmodestate == nil || devmodestate.length == 0 ||
        [devmodestate
            isEqualToString:UPDevLogModeStateOFF]) { //默认 日志应该是关闭的
        //关闭日志
        [[uSDKManager defaultManager] setLogWithLevel:USDK_LOG_NONE
            isWriteToFile:NO
            success:^{
            }
            failure:^(NSError *error){
            }]; // uSDK日志
        [uAnalytics setLogLevel:UALogLevelNone]; //行为统计分析
        [[NSUserDefaults standardUserDefaults] setValue:UPDevLogModeStateOFF
                                                 forKey:kUPDevLogModeState];
    }
    else if ([devmodestate isEqualToString:UPDevLogModeStateON]) {
        //开启日志
        [[uSDKManager defaultManager] setLogWithLevel:USDK_LOG_DEBUG
            isWriteToFile:NO
            success:^{
            }
            failure:^(NSError *error){
            }]; // uSDK日志
        [uAnalytics setLogLevel:UALogLevelDebug]; //行为统计分析
        [[NSUserDefaults standardUserDefaults] setValue:UPDevLogModeStateON
                                                 forKey:kUPDevLogModeState];
    }
    [[NSUserDefaults standardUserDefaults] synchronize];
}
- (void)idfaCollection
{

    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *success = [userDefaults objectForKey:@"IdfaCollection"];
    if ([success isEqualToString:@"success"]) {
        return;
    }
    [MainboxServerProtocol idfaCollectWithOnOperation:nil
                                           completion:^(BOOL success) {
                                             if (success == YES) {
                                                 [userDefaults
                                                     setObject:@"success"
                                                        forKey:@"IdfaCollection"];
                                             }
                                           }];
}

- (void)uploadASAData
{
    BOOL isUploadASAData =
        [[NSUserDefaults standardUserDefaults] boolForKey:IS_UPLOAD_ASA_DATA];
    if (isUploadASAData) {
        //如果上传过，返回yes
        return;
    }
    if (@available(iOS 14, *)) {
        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(
                               ATTrackingManagerAuthorizationStatus status) {
          UPLogInfo(@"MainBox",
                    @"requestTrackingAuthorizationWithCompletionHandler:%lu",
                    status);
          [self uploadASADataByiOS14_3];
        }];
    }
}

- (void)uploadASADataByiOS14_3
{
    if (@available(iOS 14.3, *)) {
        NSError *error;
        NSString *token = [AAAttribution attributionTokenWithError:&error];
        if (token) {
            NSString *url = [NSString
                stringWithFormat:@"https://api-adservices.apple.com/api/v1/"];
            NSMutableURLRequest *request =
                [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:url]];
            request.HTTPMethod = @"POST";
            [request addValue:@"text/plain" forHTTPHeaderField:@"Content-Type"];
            NSData *postData = [token dataUsingEncoding:NSUTF8StringEncoding];
            [request setHTTPBody:postData];
            NSURLSession *session = [NSURLSession sharedSession];

            NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request
                                                        completionHandler:^(NSData *_Nullable data, NSURLResponse *_Nullable response, NSError *_Nullable error) {
                                                          if (error) {
                                                              //请求失败
                                                              UPLogInfo(@"mainbox", @"adservices request failed :%@", error);
                                                          }
                                                          else {
                                                              // 请求成功
                                                              NSError *resError;
                                                              NSMutableDictionary *resDic = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&resError];
                                                              UPLogInfo(@"mainbox", @"adservices request success :%@", resDic);
                                                              ASADataModel *model = [[ASADataModel alloc] initASAServiceWithDiction:resDic];
                                                              [self reuqestUrlWithbody:model];
                                                          }
                                                        }];

            [dataTask resume];
        }
    }
}


- (void)reuqestUrlWithbody:(ASADataModel *)model
{
    model.idfaCode =
        [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString];
    NSMutableDictionary *body = model.mj_keyValues;
    [MainboxServerProtocol ASARequestwithDic:body
                              withCompletion:^(BOOL success) {
                                if (success) {
                                    [[NSUserDefaults standardUserDefaults]
                                        setBool:YES
                                         forKey:IS_UPLOAD_ASA_DATA];
                                }
                              }];
}

@end
