//
//  MainboxServerProtocol.m
//  AFNetworking
//
//  Created by <PERSON><PERSON><PERSON> on 2020/2/5.
//

#import "MainboxServerProtocol.h"
#import <uplog/UPLog.h>
#import "UPMD5.h"
#import "UPNetWorkHeader.h"
#import <UIKit/UIKit.h>
#import <UPCore/UPContext.h>

#define OH_ALL_HOME_HOST @"https://zj.haier.net" //全屋数据zj.haier.net/emuplus
#define OH_IDFA_COLLECTION @"/omsappapi/idfa/v1/collect" //idfa 收集 for 6.8.0
#define OH_IDFA_ASA @"/api-gw/zjBaseServer/app/download/IOS/ASA" //ASA数据接口

@implementation MainboxServerProtocol
+ (void)idfaCollectWithOnOperation:(nullable void (^)(AFHTTPRequestOperation *_Nullable))OnOperation completion:(nullable void (^)(BOOL success))completion
{
    NSString *urlString = [NSString stringWithFormat:@"%@%@", OH_ALL_HOME_HOST, OH_IDFA_COLLECTION];
    UPLogInfo(@"MainBox", @"AppServer url = %@, line number = %d", urlString, __LINE__);
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    NSString *appid = @"982191521"; //推⼴广应⽤用在itunes中的ID
    NSString *idfaStr = [UPContext sharedInstance].appIdfa ?: @""; //设备IDFA，包含“-”
    NSString *sysver = [[UIDevice currentDevice] systemVersion]; //设备系统版本
    NSString *model = [[UIDevice currentDevice] model]; //设备型号
    [body setValue:appid forKey:@"appid"];
    [body setValue:idfaStr forKey:@"Idfa"];
    [body setValue:sysver forKey:@"sysver"];
    [body setValue:model forKey:@"model"];
    UPLogInfo(@"Mainbox", @"idfaCollectWithOnOperation ---url:%@---body:%@", urlString, body);
    NSDictionary *header = [UPNetWorkHeader getUWSAppServerHttpHeaderWithUrl:OH_IDFA_COLLECTION bodyDictionary:body];
    [HTTPRequestManager postHTTPSRequestWithUrl:urlString
        headers:header
        body:body
        OnOperation:^(AFHTTPRequestOperation *onOperation) {
          if (OnOperation) {
              OnOperation(onOperation);
          }
        }
        completion:^(NSDictionary *responseDic) {
          if (!responseDic) {
              completion(NO);
              UPLogError(@"MainBox", @"AppServer-%s-返回数据为空", __FUNCTION__);
              return;
          }
          if ([responseDic[@"retCode"] isEqualToString:@"00000"]) {
              if ([responseDic[@"data"] isKindOfClass:[NSDictionary class]]) {
                  completion(YES);
              }
              else {
                  completion(NO);
              }
          }
          else {
              completion(NO);
          }
        }
        error:^(NSError *connectError, long responseStatusCode) {
          if (connectError) {
              completion(NO);
              UPLogError(@"MainBox", @"AppServer-%s-返回错误码 %ld", __FUNCTION__, responseStatusCode);
          }

        }];
}

+ (void)ASARequestwithDic:(NSMutableDictionary *)dic withCompletion:(nullable void (^)(BOOL success))completion
{

    NSString *urlString = [NSString stringWithFormat:@"%@%@", OH_ALL_HOME_HOST, OH_IDFA_ASA];

    NSDictionary *header = [UPNetWorkHeader getUWSAppServerHttpHeaderWithUrl:OH_IDFA_ASA bodyDictionary:dic];
    NSMutableDictionary *mutlHeader = [NSMutableDictionary dictionaryWithDictionary:header];
    mutlHeader[@"Content-Type"] = @"application/json";
    [HTTPRequestManager postHTTPSRequestWithUrl:urlString
        headers:mutlHeader
        body:dic
        OnOperation:^(AFHTTPRequestOperation *onOperation) {
        }
        completion:^(NSDictionary *responseDic) {
          if (!responseDic) {
              completion(NO);
              UPLogInfo(@"MainBox", @"AppServer-%s-返回数据为空", __FUNCTION__);
              return;
          }
          UPLogInfo(@"MainBox", @"AppServer :%@", responseDic);
          if ([responseDic[@"retCode"] isEqualToString:@"00000"]) {
              completion(YES);
          }
          else {
              completion(NO);
          }
        }
        error:^(NSError *connectError, long responseStatusCode) {
          if (connectError) {
              completion(NO);
              UPLogInfo(@"MainBox", @"AppServer-%s-返回错误码 %ld", __FUNCTION__, responseStatusCode);
          }

        }];
}
@end
