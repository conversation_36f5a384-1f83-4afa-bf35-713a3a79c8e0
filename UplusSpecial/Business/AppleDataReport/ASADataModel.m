//
//  ASADataModel.m
//  mainbox
//
//  Created by ha<PERSON> on 2022/5/9.
//

#import "ASADataModel.h"

@implementation ASADataModel

- (instancetype)initIadWithDiction:(NSDictionary *)dic
{
    if (self = [super init]) {
        self.iadAttribution = [dic[@"iad-attribution"] boolValue] ? @"1" : @"0";
        self.iadOrgId = dic[@"iad-org-id"];
        self.iadOrgName = dic[@"iad-org-name"];
        self.iadCampaignId = dic[@"iad-campaign-id"];
        self.iadCampaignName = dic[@"iad-campaign-name"];
        self.iadPurchaseDate = dic[@"iad-purchase-date"];
        self.iadConversionDate = dic[@"iad-conversion-date"];
        self.iadConversionType = dic[@"iad-conversion-type"];
        self.iadClickDate = dic[@"iad-click-date"];
        self.iadAdGroupId = dic[@"iad-adgroup-id"];
        self.iadAdGroupName = dic[@"iad-adgroup-name"];
        self.iadCountryOrRegion = dic[@"iad-country-or-region"];
        self.iadKeyword = dic[@"iad-keyword"];
        self.iadKeywordId = dic[@"iad-keyword-id"];
        self.iadKeywordMatchType = dic[@"iad-keyword-matchtype"];
        self.iadAdId = dic[@"iad-ad-id"];
        self.iadCreativeSetId = dic[@"iad-creativeset-id"];
        self.iadCreativeSetName = dic[@"iad-creativeset-name"];
    }
    return self;
}

- (instancetype)initASAServiceWithDiction:(NSDictionary *)dic
{
    if (self = [super init]) {
        self.iadAttribution = [dic[@"attribution"] boolValue] ? @"1" : @"0";
        self.iadOrgId = dic[@"orgId"];
        self.iadCampaignId = dic[@"campaignId"];
        self.iadAdGroupId = dic[@"adGroupId"];
        self.iadKeywordId = dic[@"keywordId"];
        self.iadCreativeSetId = dic[@"creativeSetId"];
        self.iadConversionType = dic[@"conversionType"];
        self.iadCountryOrRegion = dic[@"countryOrRegion"];
        self.iadClickDate = dic[@"clickDate"];
    }
    return self;
}
@end
