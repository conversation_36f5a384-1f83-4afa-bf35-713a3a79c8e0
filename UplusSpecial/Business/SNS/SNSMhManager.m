//
//  SNSMhManager.m
//  Uplus
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/5/17.
//  Copyright © 2017年 北京海尔广科数字技术有限公司. All rights reserved.
//

#import "SNSMhManager.h"

#if __IPHONE_OS_VERSION_MAX_ALLOWED >= 100000
#import <UserNotifications/UserNotifications.h>
#endif

#import <UPCore/UPCore.h>
#import <UPPush/UPPushMessage.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <uplog/UPLog.h>
#import <UPPush/UPPushGIOTrack.h>
#import <UPTrace/UPEventTrace.h>
#import "UpVdnModuleServiceProtocol.h"
#import <UPStorage/UPStorage.h>
#import <UPTools/KVNProgressShow.h>
#import "UplusSpecial-Swift.h"
#import "AlertControllerTool.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPTools/UPToast.h>
//#import <os/log.h>

#define FAMILY_DATA_CHANGE @"FAMILY_DATA_CHANGE"
#define UPFAMILY_CREATE_FAMILY @"CREATE_FAMILY" //创建家庭消息

#define UPFAMILY_DELETE_FAMILY @"DELETE_FAMILY" //管理员删除家庭

#define UPFAMILY_UPDATE_FAMILY_INFO @"UPDATE_FAMILY_INFO" //	修改家庭信息

#define UPFAMILY_DELETE_FAMILY_MEMBER @"DELETE_FAMILY_MEMBER" //删除家庭成员

#define UPFAMILY_UPDATE_FAMILY_MEMBER @"UPDATE_FAMILY_MEMBER" //修改家庭成员信息

#define UPFAMILY_INVITE_USER_JOIN_FAMILY \
    @"INVITE_USER_JOIN_FAMILY" //邀请用户加入家庭

#define UPFAMILY_USER_REFUSE_JOIN_FAMILY \
    @"USER_REFUSE_JOIN_FAMILY" //用户拒绝加入家庭
#define UPFAMILY_MEMBER_LEAVE_FAMILY @"MEMBER_LEAVE_FAMILY" //用户离开家庭

#define UPFAMILY_MANAGER_UPDATE @"FAMILY_MANAGER_UPDATE" //管理员变更

#define UPFAMILY_APPLY_JOIN_FAMILY \
    @"APPLY_FOR_JOIN_FAMILY" //申请加入家庭(管理员收到通知)

#define PUSH_GIO_MANAGER_AGREE_JOIN_FAMILY \
    @"MB14549" //管理员同意用户申请加入家庭
#define PUSH_GIO_MANAGER_REFUSE_JOIN_FAMILY \
    @"MB14550" //管理员拒绝用户申请加入家庭

#define UPFAMILY_DEVICE_SHARE @"deviceShare" //设备分享消息

#define SYSTEM_VERSION_GREATER_THAN(v) \
    ([[[UIDevice currentDevice] systemVersion] floatValue] >= v)

static NSString *kMessageCenterPageUrl =
    @"mpaas://messageCenter?needAuthLogin=1";

@interface SNSMhManager () //*<GPushMessageHandler>
//@property (nonatomic, strong) GPushManager *getuiManager;
@property (nonatomic, strong) UPPushMessage *upnMessage;

@end

@implementation SNSMhManager

+ (instancetype)instance
{
    static SNSMhManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      manager = [[SNSMhManager alloc] init];
    });
    return manager;
}

- (void)addObserver
{
    [[NSNotificationCenter defaultCenter]
        addObserver:self
           selector:@selector(processMyHomeMessage:)
               name:@"FAMILY_DATA_CHANGE"
             object:nil];
    // 设备共享消息
    [[NSNotificationCenter defaultCenter]
        addObserver:self
           selector:@selector(processMyHomeMessage:)
               name:@"devShareApi"
             object:nil];
}

- (void)processMyHomeMessage:(NSNotification *)notification
{
    UPPushMessage *message = notification.object;
    [self insertUnreadMessage:message];
}

- (void)insertUnreadMessage:(UPPushMessage *)message
{
    NSString *apiType = message.messageBody.extraData.api.apiType;
    if ([apiType isEqualToString:UPFAMILY_INVITE_USER_JOIN_FAMILY]) {
        [self inviteUserJoinWithMessage:message];
    }
    else if ([apiType isEqualToString:UPFAMILY_DELETE_FAMILY_MEMBER]) {
        [self deleteFamilyMemberWithMessage:message];
    }
    else if ([apiType isEqualToString:UPFAMILY_MEMBER_LEAVE_FAMILY]) {
        [self userLeaveFamilyWithMessage:message];
    }
    else if ([apiType isEqualToString:UPFAMILY_DEVICE_SHARE]) {
        [self handleDeviceShareMessage:message];
    }
    else {
        [self judgeUPMessageFamily:message];
    }
}

- (void)judgeUPMessageFamily:(UPPushMessage *)message
{
    NSString *apiType = message.messageBody.extraData.api.apiType;
    if ([apiType isEqualToString:UPFAMILY_UPDATE_FAMILY_INFO]) {
        [self updateFamilyInfoWithMessage:message];
    }
    else if ([apiType isEqualToString:UPFAMILY_UPDATE_FAMILY_MEMBER]) {
        [self updateFamilyMemberWithMessage:message];
    }
    else if ([apiType
                 isEqualToString:UPFAMILY_DELETE_FAMILY]) { //管理员删除家庭]
        [self managerDissolveFamily:message];
    }
    else if ([apiType isEqualToString:UPFAMILY_MANAGER_UPDATE]) { //管理员变更
        [self managerChangeNewManager:message];
    }
    else if ([apiType
                 isEqualToString:
                     UPFAMILY_APPLY_JOIN_FAMILY]) { //管理员收到加入家庭申请
        [self processApplicationForJoinFamilyWithMessage:message];
    }
}

- (void)showOtherMessage:(UPPushMessage *)message
{
    if (message.messageBody.messageUI.content.length > 0 ||
        message.messageBody.messageUI.content.length > 0) {
        [self showAlert:message.messageBody.messageUI.content ?: @""
                  Title:message.messageBody.messageUI.title ?: @""
              messageId:message.messageID];
    }
}
- (void)managerChangeNewManager:(UPPushMessage *)message
{
    NSString *alertMessage = message.messageBody.messageUI.content;
    [self reloadFamilyMemebers];
    [self showAlert:alertMessage Title:@"家庭通知" messageId:message.messageID];
}

- (void)managerDissolveFamily:(UPPushMessage *)message
{
    NSString *familyName =
        message.messageBody.extraData.api.params[@"familyName"];
    NSString *alertMessage =
        [NSString stringWithFormat:@"%@已解散。", familyName];
    [self reloadFamilyMemebers];
    [self showAlert:alertMessage Title:@"家庭通知" messageId:message.messageID];
}

- (void)inviteUserJoinWithMessage:(UPPushMessage *)message
{
    UpFamilyInviteView *inviteView =
        [[UpFamilyInviteView alloc] initWithMessage:message];
    inviteView.resultHandler =
        ^(UserDomainSampleResult *_Nonnull result, NSString *_Nonnull familyId) {
          if (!result.success) {
              return;
          }
          id<UDUserDelegate> user = [UpUserDomainHolder instance].userDomain.user;
          [user refreshFamilyList:^(UserDomainSampleResult *_Nonnull rst) {
            if (rst.success) {
                id<UDFamilyDelegate> family = [user getFamilyById:familyId];
                if (family) {
                    [user setCurrentFamily:family];
                }
            }
          }
              failure:^(UserDomainSampleResult *_Nonnull result) {
                UPLogError(@"UplusSpecial",
                           @"refreshFamilyList failed after join family: %@",
                           familyId);
              }];
        };
    [inviteView show];
}

- (void)deleteFamilyMemberWithMessage:(UPPushMessage *)message
{
    //删除家庭成员
    NSString *memberId = message.messageBody.extraData.api.params[@"memberId"];
    NSString *memberName =
        message.messageBody.extraData.api.params[@"memberName"];

    NSString *messageStr;
    [self reloadFamilyMemebers];
    if ([memberId isEqualToString:UpUserDomainHolder.instance.userDomain.oauthData
                                      .uhome_user_id]) {
        //自己被踢出家庭
        messageStr = [NSString stringWithFormat:@"%@退出家庭。", memberName];
        [self showAlert:messageStr Title:@"家庭通知" messageId:message.messageID];
        return;
    }
    //用户主动退出家庭
    messageStr = [NSString stringWithFormat:@"%@退出家庭。", memberName];
    [self reloadFamilyMemebers];
    [self showAlert:messageStr Title:@"家庭通知" messageId:message.messageID];
}

- (void)userLeaveFamilyWithMessage:(UPPushMessage *)message
{
    //家庭成员主动离开家庭
    NSString *memberId = message.messageBody.extraData.api.params[@"memberId"];
    NSString *memberName =
        message.messageBody.extraData.api.params[@"memberName"];
    [self reloadFamilyMemebers];
    NSString *messageStr;
    if ([memberId isEqualToString:UpUserDomainHolder.instance.userDomain.oauthData
                                      .uhome_user_id]) {
        //用户自己离开刷新数据
        messageStr = [NSString stringWithFormat:@"%@退出家庭。", memberName];
        [self showAlert:messageStr Title:@"家庭通知" messageId:message.messageID];
        return;
    }
    messageStr = [NSString stringWithFormat:@"%@退出家庭。", memberName];
    [self showAlert:messageStr Title:@"家庭通知" messageId:message.messageID];
}

- (void)processApplicationForJoinFamilyWithMessage:(UPPushMessage *)message
{
    //弹窗提示  允许/拒绝 用户加入家庭
    NSString *content = message.messageBody.messageUI.content; //通知内容
    NSString *familyId = message.messageBody.extraData.api
                             .params[@"familyInfo"][@"familyId"]; //家庭id
    if (![familyId isKindOfClass:[NSString class]]) {
        familyId = [NSString stringWithFormat:@"%@", familyId];
    }

    UIViewController *controller = [UpVdnUtils getCurrentViewController];
    UIAlertController *alertControl =
        [UIAlertController alertControllerWithTitle:@"家庭通知"
                                            message:content
                                     preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction
        actionWithTitle:@"拒绝"
                  style:UIAlertActionStyleCancel
                handler:^(UIAlertAction *action) {
                  [self mangerAllowUserToJoinFamilyWithMessage:message agree:NO];
                  [[UPEventTrace getInstance]
                             trace:PUSH_GIO_MANAGER_REFUSE_JOIN_FAMILY
                      withVariable:@{
                          @"FamilyID" : familyId
                      }];
                }];
    [alertControl addAction:cancelAction];
    UIAlertAction *okAction = [UIAlertAction
        actionWithTitle:@"同意"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *action) {
                  [[UPEventTrace getInstance]
                             trace:PUSH_GIO_MANAGER_AGREE_JOIN_FAMILY
                      withVariable:@{
                          @"FamilyID" : familyId
                      }];
                  __weak typeof(self) weakSelf = self;
                  [self showFamilyPrivacyAgreementWithSuccessAction:^{
                    __strong typeof(self) strongSelf = weakSelf;
                    [strongSelf mangerAllowUserToJoinFamilyWithMessage:message
                                                                 agree:YES];
                  }];
                }];
    [alertControl addAction:okAction];
    [controller presentViewController:alertControl animated:YES completion:nil];
}

- (void)updateFamilyInfoWithMessage:(UPPushMessage *)message
{
    //管理员修改家庭信息
    [UpUserDomainHolder.instance.userDomain
        refreshUser:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"MainBox", @"%s", __FUNCTION__);
        }];
}

- (void)updateFamilyMemberWithMessage:(UPPushMessage *)message
{
    //家庭管理员或家庭成员修改家庭成员信息
    [UpUserDomainHolder.instance.userDomain
        refreshUser:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"MainBox", @"%s", __FUNCTION__);
        }];
}
- (NSString *)mobileNum:(NSString *)mobileNum name:(NSString *)nameStr
{
    if (!nameStr || [nameStr isEqualToString:@""]) {
        if (mobileNum.length > 5) {
            NSMutableString *telnum =
                [[NSMutableString alloc] initWithString:mobileNum];
            [telnum replaceCharactersInRange:NSMakeRange(3, 4) withString:@"****"];
            return telnum;
        }
        return @"";
    }
    else {
        return nameStr;
    }
}

//管理员同意/拒绝用户加入家庭
- (void)mangerAllowUserToJoinFamilyWithMessage:(UPPushMessage *)pushMessage
                                         agree:(BOOL)agree
{
    NSString *applicationId =
        pushMessage.messageBody.extraData.api.params[@"applicationId"];
    [UpUserDomainHolder.instance.userDomain.user
        replyJoinFamily:applicationId ?: @""
        agree:agree
        success:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"%@加入家庭成功", agree ? @"同意" : @"拒绝");
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          if ([@"E31108" isEqualToString:result.retCode]) {
              [KVNProgressShow showText:@"家庭已解散，无法加入"];
              return;
          }
          if ([@"E31138" isEqualToString:result.retCode]) {
              [KVNProgressShow showText:@"二维码已失效，无法加入"];
              return;
          }
          NSString *retInfo = ([result.retInfo isKindOfClass:[NSString class]] &&
                               result.retInfo.length) ?
                                  result.retInfo :
                                  @"操作失败，请稍后重试！";
          [KVNProgressShow showText:retInfo];
        }];
}

- (void)showAlert:(NSString *)message
            Title:(NSString *)title
        messageId:(NSString *)msgId
{
    UIViewController *controller = [UpVdnUtils getCurrentViewController];

    UIAlertController *alertControl =
        [UIAlertController alertControllerWithTitle:title
                                            message:message
                                     preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *okAction =
        [UIAlertAction actionWithTitle:@"知道了"
                                 style:UIAlertActionStyleCancel
                               handler:^(UIAlertAction *action) {
                                 [self snsMessageGioTrackWithTitle:title
                                                           content:message
                                                        buttonName:@"知道了"
                                                         messageId:msgId];
                               }];
    [alertControl addAction:okAction];
    [controller presentViewController:alertControl animated:YES completion:nil];
}

- (void)showAlert:(NSString *)message messageId:(NSString *)msgId
{
    UIViewController *controller = [UpVdnUtils getCurrentViewController];

    UIAlertController *alertControl =
        [UIAlertController alertControllerWithTitle:@"提示"
                                            message:message
                                     preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *okAction =
        [UIAlertAction actionWithTitle:@"知道了"
                                 style:UIAlertActionStyleCancel
                               handler:^(UIAlertAction *action) {
                                 [self snsMessageGioTrackWithTitle:@"提示"
                                                           content:message
                                                        buttonName:@"知道了"
                                                         messageId:msgId];
                               }];
    [alertControl addAction:okAction];
    [controller presentViewController:alertControl animated:YES completion:nil];
}

- (void)reloadFamilyMemebers
{
    [UpUserDomainHolder.instance.userDomain.user
        refreshUser:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"%s", __FUNCTION__);
        }];
}

- (void)snsMessageGioTrackWithTitle:(NSString *)title
                            content:(NSString *)content
                         buttonName:(NSString *)buttonName
                          messageId:(NSString *)msgId
{
    [UPPushGIOTrack.instance gioTrackWithEventId:PUSH_GIO_EVENTID
                                           msgID:msgId
                                      buttonName:buttonName
                                       hyperlink:nil
                                    contentTitle:title
                                 contentSubtitle:content];
}

- (void)showFamilyPrivacyAgreementWithSuccessAction:
    (void (^)(void))successAction
{
    //判断是否已同意过家庭隐私协议弹窗
    NSString *isShowKey =
        [NSString stringWithFormat:@"up_family_manager_privacy_agreement_%@",
                                   [UpUserDomainHolder instance]
                                       .userDomain.oauthData.uhome_user_id];
    BOOL haveShowed = [UPStorage getBooleanValue:isShowKey defaultValue:NO];
    if (haveShowed) {
        //若已同意过,直接执行后续操作
        if (successAction) {
            successAction();
        }
        return;
    }
    //若未同意过，则展示弹窗，点击同意后，继续执行后续操作（延迟半秒，是防止alertVC叠加出现alertVC）
    dispatch_after(
        dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)),
        dispatch_get_main_queue(), ^{
          UIViewController *controller = [UpVdnUtils getCurrentViewController];
          UIAlertController *alertControl = [UIAlertController
              alertControllerWithTitle:@"家庭服务功能须知"
                               message:
                                   @"\n您邀请家人对方同意加入后，或者您加入其"
                                   @"他"
                                   @"人"
                                   @"的"
                                   @"家"
                                   @"庭"
                                   @"后，家庭中所有用户都可以控制"
                                   @"家"
                                   @"庭"
                                   @"中"
                                   @"每"
                                   @"个"
                                   @"成员绑定的家电设备，使用家庭"
                                   @"应"
                                   @"用"
                                   @"进"
                                   @"行"
                                   @"食"
                                   @"材管理，并查看所有成员绑定的"
                                   @"设备数据("
                                   @"包"
                                   @"括"
                                   @"历史数据、健康相关的隐私数据)"
                                   @"。"
                                   @"被"
                                   @"邀"
                                   @"请"
                                   @"进"
                                   @"入的家庭成员也有权限邀请新的"
                                   @"家"
                                   @"庭"
                                   @"成"
                                   @"员"
                                   @"，"
                                   @"新加入的家庭成员和您具有一样"
                                   @"的"
                                   @"查"
                                   @"看"
                                   @"、"
                                   @"控"
                                   @"制权限，家庭中的每个成员都可"
                                   @"以"
                                   @"自"
                                   @"己"
                                   @"退"
                                   @"出，管理员可以删除成员。\n\n您"
                                   @"是否知悉并同意？"
                        preferredStyle:UIAlertControllerStyleAlert];
          UIAlertAction *cancelAction =
              [UIAlertAction actionWithTitle:@"拒绝"
                                       style:UIAlertActionStyleCancel
                                     handler:^(UIAlertAction *action){
                                     }];
          [alertControl addAction:cancelAction];
          UIAlertAction *okAction = [UIAlertAction
              actionWithTitle:@"同意"
                        style:UIAlertActionStyleDefault
                      handler:^(UIAlertAction *action) {
                        //同意协议后，继续执行后续操作，并在upstorage中将此用户的状态设为已同意
                        successAction();
                        [UPStorage putBooleanValue:YES name:isShowKey];
                      }];
          [alertControl addAction:okAction];

          //产品要求message左对齐
          UILabel *alertMessageLabel =
              [alertControl.view valueForKeyPath:@"_messageLabel"];
          alertMessageLabel.textAlignment = NSTextAlignmentLeft;

          [controller presentViewController:alertControl
                                   animated:YES
                                 completion:nil];
        });
}
/// 处理分享设备信息
- (void)handleDeviceShareMessage:(UPPushMessage *)message
{
    NSDictionary *params = message.messageBody.extraData.api.params;
    if (![params isKindOfClass:NSDictionary.class]) {
        UPLogInfo(@"UplusSpecial-SNSMhManager", @"无效消息体类型: %@", NSStringFromClass(params.class));
        return;
    }

    NSNumber *dynamicPopFlag = [self extractNumberFromObject:params[@"dynamicPopFlag"]];
    NSString *shareUuid = [self extractStringFromObject:params[@"shareUuid"]];
    //    os_log_t logger = os_log_create("com.haier.uhome.Uplus", "special");
    //    os_log(logger, "设备分享参数%{public}@", params);
    if (!dynamicPopFlag || shareUuid.length == 0) {
        UPLogInfo(@"UplusSpecial-SNSMhManager", @"设备分享参数缺失: popFlag=%@, uuid=%@",
                  dynamicPopFlag ?: @"nil", shareUuid ?: @"nil");
        //        os_log(logger, "设备分享参数缺失");
        return;
    }

    static NSString *const kDefaultTitle = @"设备共享";
    static NSString *const kDefaultContent = @"其他用户共享智能设备给你";

    NSString *content = [self isValidString:params[@"content"]] ? params[@"content"] : kDefaultContent;
    NSString *title = [self isValidString:params[@"title"]] ? params[@"title"] : kDefaultTitle;

    BOOL shouldAccept = (dynamicPopFlag.integerValue == 1);
    NSString *actionTitle = shouldAccept ? @"接受" : @"查看详情";

    __weak typeof(self) weakSelf = self;


    [AlertControllerTool showAlertController:title
        content:content
        cancelTitle:@"取消"
        confirmTitle:actionTitle
        falseHandler:^{
        }
        completeHandler:^{
          __strong typeof(weakSelf) strongSelf = weakSelf;
          if (!strongSelf) {
              //            os_log(logger, "弹框：self不存在");
              return;
          }

          if (shouldAccept) {
              //            os_log(logger, "弹框：点击接受");
              [strongSelf confirmDeviceSharingRelation:shareUuid];
          }
          else {
              //              os_log(logger, "弹框：点击跳转");
              [strongSelf gotoMessageCenter];
          }
        }];
}


// 请求接受 接口
- (void)confirmDeviceSharingRelation:(NSString *)shareUuid
{
    id user = [UpUserDomainHolder instance].userDomain.user;
    if (user) {
        [user confirmDeviceSharingRelation:shareUuid
            success:^(UserDomainSampleResult *_Nonnull result) {
              UPLogInfo(@"UplusSpecial-SNSMhManager", @"请求结果:请求%@\n %@",
                        result.success ? @"成功" : @"失败", result.retInfo);
              dispatch_async(dispatch_get_main_queue(), ^{
                if ([result.retCode isEqualToString:@"00000"]) {
                    [[UPToast shareManager] showWithText:@"接受成功"];
                }
                else if ([result.retCode isEqualToString:@"19016"]) {
                    [[UPToast shareManager]
                        showWithText:@"接受共享设备数量已达上限"];
                }
                else {
                    [[UPToast shareManager] showWithText:@"接受失败"];
                }
              });
            }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              UPLogError(@"UplusSpecial-SNSMhManager",
                         @"错误：retCode:%@\nretInfo:%@", result.retCode,
                         result.retInfo);
              dispatch_async(dispatch_get_main_queue(), ^{
                [[UPToast shareManager] showWithText:@"接受失败"];
              });
            }];
    }
    else {
        UPLogError(@"UplusSpecial-SNSMhManager",
                   @"无法获取到[UpUserDomainHolder "
                   @"instance].userDomain.user");
    }
}
// 前往 消息中心
- (void)gotoMessageCenter
{
    //    os_log_t logger = os_log_create("com.haier.uhome.Uplus", "special");
    //    os_log(logger, "弹框：前往 消息中心");
    dispatch_async(dispatch_get_main_queue(), ^{
      [UpVdn goToPage:kMessageCenterPageUrl
          flag:VdnPageFlagPush
          parameters:nil
          complete:^(NSDictionary *data) {
            UPLogInfo(@"UplusSpecial-SNSMhManager", @"跳转消息中心成功");
            //          os_log(logger, "弹框：跳转消息中心成功");
          }
          error:^(NSError *error) {
            UPLogError(@"UplusSpecial-SNSMhManager", @"跳转消息中心错误:%@", error);
            //          os_log(logger, "弹框：跳转消息中心错误:%{public}@", error);
          }];
    });
}
#pragma mark - Helper Methods

- (NSNumber *)extractNumberFromObject:(id)object
{
    if ([object isKindOfClass:NSNumber.class]) {
        return object;
    }
    if ([object isKindOfClass:NSString.class]) {
        NSString *str = (NSString *)object;
        NSScanner *scanner = [NSScanner scannerWithString:str];
        NSInteger intValue = 0;
        if ([scanner scanInteger:&intValue]) {
            return @(intValue);
        }
    }
    return nil;
}

- (NSString *)extractStringFromObject:(id)object
{
    if ([object isKindOfClass:NSString.class]) {
        return (NSString *)object;
    }
    if ([object isKindOfClass:NSNumber.class]) {
        return [(NSNumber *)object stringValue];
    }
    return nil;
}

- (BOOL)isValidString:(id)str
{
    return [str isKindOfClass:NSString.class] && ((NSString *)str).length > 0;
}
@end
