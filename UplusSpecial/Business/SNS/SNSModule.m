//
//  SNSModule.m
//  Uplus
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/5/22.
//  Copyright © 2017年 北京海尔广科数字技术有限公司. All rights reserved.
//

#import "SNSModule.h"
#import "SNSMhManager.h"
#import "UpVdnModuleServiceProtocol.h"

@interface SNSModule () <UpWorkflowTask>

@end

@implementation SNSModule
- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    return stage == UpLaunchStageBeforePrivacy ? self : nil;
}

- (void)run
{
    [[SNSMhManager instance] addObserver];
}

@end
