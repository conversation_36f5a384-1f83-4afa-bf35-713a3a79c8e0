//
//  FamilyInviteView.swift
//  UplusSpecial
//
//  Created by l<PERSON><PERSON> on 2025/4/23.
//

import UIKit
import UPPush
import SnapKit
import UHWebImage
import UPTools
import UPUserDomain

@objc(UpFamilyInviteView) public class FamilyInviteView: UIView {
  @objc public var resultHandler: ((UserDomainSampleResult, String) -> Void)?

  private let pushMessage: UPPushMessage

  private let safeBottom: CGFloat = UIApplication.shared.delegate?.window??.safeAreaInsets.bottom ?? 34

  // 弹窗宽度和边距常量
  private static let kMaxPopupWidth: CGFloat = 420
  private static let kPopupMargin: CGFloat = 12

  private let contentView: UIView = {
    let view = UIView()
    view.backgroundColor = .init(red: 245/255.0, green: 245/255.0, blue: 245/255.0, alpha: 1.0)
    view.layer.cornerRadius = 32
    view.layer.masksToBounds = true
    return view
  }()
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .init(red: 17/255.0, green: 17/255.0, blue: 17/255.0, alpha: 1.0)
    label.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
    label.text = "邀请家人";
    label.textAlignment = .center
    return label
  }()
  
  private let avatarView: UIImageView = {
    let imageView = UIImageView()
    imageView.layer.cornerRadius = 44
    imageView.layer.masksToBounds = true
    return imageView
  }()
  
  private let nameLabel: UILabel = {
    let label = UILabel()
    label.textColor = .init(red: 17/255.0, green: 17/255.0, blue: 17/255.0, alpha: 1.0)
    label.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
    label.textAlignment = .center
    return label
  }()
  
  private let messageLabel: UILabel = {
    let label = UILabel()
    label.textColor = .init(red: 102/255.0, green: 102/255.0, blue: 102/255.0, alpha: 1.0)
    label.font = UIFont.init(name: "PingFangSC-Regular", size: 14)
    label.textAlignment = .center
    label.numberOfLines = 0
    label.lineBreakMode = .byWordWrapping
    return label
  }()
  
  private lazy var errorContentView: UIView = {
    let view = UIView()
    view.backgroundColor = .init(red: 245/255.0, green: 245/255.0, blue: 245/255.0, alpha: 2.0)
    view.layer.cornerRadius = 32
    view.layer.masksToBounds = true
    return view
  }()
  
  @objc public init(withMessage message: UPPushMessage) {
    pushMessage = message
    super.init(frame: .zero)
    backgroundColor = .black.withAlphaComponent(0)
    setupSubviews()
  }

  
  @objc public func show() {
    let parentView = UIApplication.shared.delegate?.window as? UIView
    var layoutView: UIView? = self
    if superview == nil {
      layoutView = parentView
      parentView?.addSubview(self)
      snp.makeConstraints { make in
        make.edges.equalTo(UIEdgeInsets.zero)
      }
      
      contentView.snp.remakeConstraints { make in
        make.top.equalTo(snp.bottom)
        makePopupWidthConstraints(make)
        make.height.equalTo(372)
      }
      parentView?.setNeedsLayout()
      parentView?.layoutIfNeeded()
    }
    
    contentView.snp.remakeConstraints { make in
      make.bottom.equalTo(-safeBottom)
      makePopupWidthConstraints(make)
      make.height.equalTo(372)
    }
    layoutView?.setNeedsLayout()
    UIView.animate(withDuration: 0.35) {
      self.backgroundColor = .black.withAlphaComponent(0.5)
      layoutView?.layoutIfNeeded()
    }
  }
  
  @objc public func dismiss(_ view: UIView? = nil) {
    let animateView = view ?? contentView
    let height = animateView.frame.height
    animateView.snp.remakeConstraints { make in
      make.top.equalTo(snp.bottom)
      makePopupWidthConstraints(make)
      make.height.equalTo(height)
    }
    setNeedsLayout()

    let visibleHeight = frame.height - animateView.frame.minY
    let duration = 0.35 * visibleHeight / (safeBottom + height)
    UIView.animate(withDuration: duration) {
      self.backgroundColor = .black.withAlphaComponent(0)
      self.layoutIfNeeded()
    } completion: { _ in
      self.removeFromSuperview()
    }
  }
  
  @objc private func ignoreButtonTouched(_ sender: UIButton) {
    dismiss()
  }
  
  @objc private func joinButtonTouched(_ sender: UIButton) {
    let familyInfo = pushMessage.messageBody.extraData.api.params["familyInfo"] as? [String: Any]
    let familyId = familyInfo?["familyId"] as? String ?? ""
    let invitationCode = pushMessage.messageBody.extraData.api.params["invitationCode"] as? String ?? ""
    let user = UpUserDomainHolder.instance().userDomain.user()
    user.replyFamilyInvite(invitationCode, familyId: familyId, agree: true) { result in
      DispatchQueue.main.async {
        self.resultHandler?(result, familyId)
        self.dismiss()
      }
    } failure: { result in
      DispatchQueue.main.async {
        self.resultHandler?(result, familyId)
        self.showErrorView(result)
      }
    }
  }
  
  @objc private func knownButtonTouched(_ sender: UIButton) {
    dismiss(errorContentView)
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  // MARK: - 弹窗宽度约束辅助方法
  private func makePopupWidthConstraints(_ make: ConstraintMaker) {
      let containerWidth = UIScreen.main.bounds.width
    let maxWidthWithMargins = Self.kMaxPopupWidth + 2 * Self.kPopupMargin

    if containerWidth > maxWidthWithMargins {
      // 容器宽度大于420+12+12时，弹窗最大宽度为420，居中显示
      make.width.equalTo(Self.kMaxPopupWidth)
      make.centerX.equalToSuperview()
    } else {
      // 否则保持12pt边距
      make.leading.equalTo(Self.kPopupMargin)
      make.trailing.equalTo(-Self.kPopupMargin)
    }
  }

}

extension FamilyInviteView {
  private func setupSubviews() {
    addSubview(contentView)
    
    // 拉杆手势的响应范围
    let rodBgView = UIView()
    let gesture = UIPanGestureRecognizer(target: self, action: #selector(onPanGesture(_:)))
    rodBgView.addGestureRecognizer(gesture)
    contentView.addSubview(rodBgView)
    rodBgView.snp.makeConstraints { make in
      make.top.equalTo(0)
      make.leading.equalTo(32)
      make.trailing.equalTo(-32)
      make.height.equalTo(16)
    }
    
    // 拉杆
    let rodView = UIView()
    rodView.backgroundColor = .init(red: 229/255.0, green: 229/255.0, blue: 229/255.0, alpha: 1.0)
    rodView.layer.cornerRadius = 2
    rodView.layer.masksToBounds = true
    contentView.addSubview(rodView)
    rodView.snp.makeConstraints { make in
      make.top.equalTo(8)
      make.height.equalTo(4)
      make.centerX.equalToSuperview()
      make.width.equalTo(32)
    }
    
    contentView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.top.equalTo(rodBgView.snp.bottom).offset(4)
      make.leading.equalTo(32)
      make.trailing.equalTo(-32)
    }
    
    // 头像
    contentView.addSubview(avatarView)
    avatarView.snp.makeConstraints { make in
      make.top.equalTo(titleLabel.snp.bottom).offset(44)
      make.centerX.equalToSuperview()
      make.width.height.equalTo(88)
    }
    
    let image = UIImage(named: "UplusSpecial.bundle/invitor_default_avatar")
    let invitorInfo = pushMessage.messageBody.extraData.api.params["invitorInfo"] as? [String: Any]
    let avatarUrl = invitorInfo?["avatarUrl"] as? String
    let name = invitorInfo?["name"] as? String
    if let avatarUrl = avatarUrl, let url = URL(string: avatarUrl) {
      avatarView.uh_setImage(with: url, placeholderImage: image)
    } else {
      avatarView.image = image
    }

    // 名字
    nameLabel.text = name
    contentView.addSubview(nameLabel)
    nameLabel.snp.makeConstraints { make in
      make.top.equalTo(avatarView.snp.bottom).offset(14)
      make.leading.equalTo(52)
      make.trailing.equalTo(-52)
    }
    
    // 邀请文案
    let familyInfo = pushMessage.messageBody.extraData.api.params["familyInfo"] as? [String: Any]
    let familyName = familyInfo?["familyName"] as? String
    messageLabel.text = "邀请您加入“\(familyName ?? "")”，共同控制家电"
    contentView.addSubview(messageLabel)
    messageLabel.snp.makeConstraints { make in
      make.top.equalTo(nameLabel.snp.bottom).offset(12)
      make.leading.equalTo(16)
      make.trailing.equalTo(-16)
    }
    
    let btnWidth = (UIScreen.main.bounds.width - 12 * 2 - 16 * 3) / 2
    // 忽略按钮
    let ignoreButton = UIButton(type: .custom)
    ignoreButton.backgroundColor = .white
    ignoreButton.layer.cornerRadius = 16
    ignoreButton.layer.masksToBounds = true
    ignoreButton.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
    ignoreButton.setTitleColor(.init(red: 17/255.0, green: 17/255.0, blue: 17/255.0, alpha: 1.0), for: .normal)
    ignoreButton.setTitle("忽略", for: .normal)
    ignoreButton.addTarget(self, action: #selector(ignoreButtonTouched(_:)), for: .touchUpInside)
    contentView.addSubview(ignoreButton)
    ignoreButton.snp.makeConstraints { make in
      make.leading.equalTo(16)
      make.width.equalTo(btnWidth)
      make.height.equalTo(44)
      make.bottom.equalTo(-16)
    }
    
    // 加入按钮
    let joinButton = UIButton(type: .custom)
    joinButton.backgroundColor = .init(red: 0/255.0, green: 129/255.0, blue: 255/255.0, alpha: 1.0)
    joinButton.layer.cornerRadius = 16
    joinButton.layer.masksToBounds = true
    joinButton.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
    joinButton.setTitleColor(.white, for: .normal)
    joinButton.setTitle("加入", for: .normal)
    joinButton.addTarget(self, action: #selector(joinButtonTouched(_:)), for: .touchUpInside)
    contentView.addSubview(joinButton)
    joinButton.snp.makeConstraints { make in
      make.trailing.equalTo(-16)
      make.width.equalTo(ignoreButton)
      make.height.equalTo(ignoreButton)
      make.bottom.equalTo(ignoreButton)
    }
  }
  
  @objc private func onPanGesture(_ gesture: UIPanGestureRecognizer) {
    let minY = UIScreen.main.bounds.height - contentView.bounds.height - safeBottom
    switch gesture.state {
      case .changed:
        let translate = gesture.translation(in: gesture.view)
        var y = contentView.frame.minY
        y += translate.y
        if y < minY {
          y = minY
        } else if y > frame.maxY {
          y = frame.maxY
        }
        var frame = contentView.frame
        frame.origin.y = y
        contentView.frame = frame
        break
      case .ended, .cancelled, .failed:
        let velocity = gesture.velocity(in: gesture.view)
        if velocity.y > 1500 {
          dismiss()
          return
        }
        
        if contentView.frame.maxY > frame.maxY + contentView.frame.height / 3.0 {
          dismiss()
        } else {
          show()
        }
      default:
        break
    }
    gesture.setTranslation(.zero, in: gesture.view)
  }
  
  private func showErrorView(_ result: UpUserDomainResult) {
    switch result.retCode {
      case "E31138":
        showDetailErrorView("二维码失效", message: "请联系对方刷新二维码后重新扫描")
      case "E31108":
        showCommonErrorView("家庭已解散，无法加入该家庭")
      case "E31143":
        showCommonErrorView("邀请已撤销，无法加入该家庭")
      default:
        let msg = result.retInfo.isEmpty ? "操作失败，请稍后重试！" : result.retInfo
        UPToast.shareManager().show(withText: msg)
        dismiss()
    }
  }
  
  private func showDetailErrorView(_ title: String, message: String) {
    addSubview(errorContentView)
    errorContentView.snp.makeConstraints { make in
      make.top.equalTo(snp.bottom)
      make.height.equalTo(176)
      makePopupWidthConstraints(make)
    }
    
    let titleLabel = UILabel()
    titleLabel.textColor = .black
    titleLabel.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
    titleLabel.text = title
    errorContentView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.top.equalTo(28)
      make.centerX.equalToSuperview()
    }
    
    let messageLabel = UILabel()
    messageLabel.textColor = .init(red: 102/255.0, green: 102/255.0, blue: 102/255.0, alpha: 1.0)
    messageLabel.font = UIFont.init(name: "PingFangSC-Regular", size: 14)
    messageLabel.textAlignment = .center
    messageLabel.numberOfLines = 0
    messageLabel.lineBreakMode = .byWordWrapping
    messageLabel.text = message
    errorContentView.addSubview(messageLabel)
    messageLabel.snp.makeConstraints { make in
      make.top.equalTo(titleLabel.snp.bottom).offset(12)
      make.leading.equalTo(16)
      make.trailing.equalTo(-16)
    }
    
    let button = UIButton(type: .custom)
    button.backgroundColor = .init(red: 0/255.0, green: 129/255.0, blue: 255/255.0, alpha: 1.0)
    button.layer.cornerRadius = 16
    button.layer.masksToBounds = true
    button.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
    button.setTitleColor(.white, for: .normal)
    button.setTitle("我知道了", for: .normal)
    button.addTarget(self, action: #selector(knownButtonTouched(_:)), for: .touchUpInside)
    errorContentView.addSubview(button)
    button.snp.makeConstraints { make in
      make.trailing.bottom.equalTo(-16)
      make.leading.equalTo(16)
      make.height.equalTo(44)
    }
    
    contentView.removeFromSuperview()
    setNeedsLayout()
    layoutIfNeeded()
    
    errorContentView.snp.makeConstraints { make in
      make.bottom.equalTo(-safeBottom)
      makePopupWidthConstraints(make)
      make.height.equalTo(176)
    }
    
    setNeedsLayout()
    UIView.animate(withDuration: 0.35) {
      self.layoutIfNeeded()
    }
  }
  
  private func showCommonErrorView(_ message: String) {
    addSubview(errorContentView)
    errorContentView.snp.makeConstraints { make in
      make.top.equalTo(snp.bottom)
      make.height.equalTo(144)
      makePopupWidthConstraints(make)
    }
    
    let titleLabel = UILabel()
    titleLabel.textColor = .black
    titleLabel.font = UIFont.init(name: "PingFangSC-Medium", size: 17)
    titleLabel.textAlignment = .center
    titleLabel.text = message
    errorContentView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.top.equalTo(28)
      make.leading.equalTo(16)
      make.trailing.equalTo(-16)
    }
    
    let button = UIButton(type: .custom)
    button.backgroundColor = .init(red: 0/255.0, green: 129/255.0, blue: 255/255.0, alpha: 1.0)
    button.layer.cornerRadius = 16
    button.layer.masksToBounds = true
    button.titleLabel?.font = UIFont.init(name: "PingFangSC-Medium", size: 16)
    button.setTitleColor(.white, for: .normal)
    button.setTitle("我知道了", for: .normal)
    button.addTarget(self, action: #selector(knownButtonTouched(_:)), for: .touchUpInside)
    errorContentView.addSubview(button)
    button.snp.makeConstraints { make in
      make.trailing.bottom.equalTo(-16)
      make.leading.equalTo(16)
      make.height.equalTo(44)
    }
    
    contentView.removeFromSuperview()
    setNeedsLayout()
    layoutIfNeeded()
    errorContentView.snp.makeConstraints { make in
      make.bottom.equalTo(-safeBottom)
      makePopupWidthConstraints(make)
      make.height.equalTo(144)
    }
    setNeedsLayout()
    UIView.animate(withDuration: 0.35) {
      self.layoutIfNeeded()
    }
  }
}
