//
//  UP3DTouchModule.m
//  UplusSpecial
//
//  Created by 郑连乐 on 2025/3/21.
//

#import "UP3DTouchModule.h"
#import <UpVdnModule/UpVdnModuleServiceProtocol.h>
#import <UpTrace/UPEventTrace.h>

@interface UP3DTouchModule () <UpWorkflowTask>

@property (nonatomic) UpLaunchStage currentStage;

@end

// 添加设备
static NSString *const UIApplicationShortcutItemTypeAddDevice = @"com.haier.uhome.Uplus.addDevice";
// 消息中心
static NSString *const UIApplicationShortcutItemTypeMessageCenter = @"com.haier.uhome.Uplus.messageCenter";
// 报装报修
static NSString *const UIApplicationShortcutItemTypeInstallationRepair = @"com.haier.uhome.Uplus.installationRepair";

@implementation UP3DTouchModule

// 模块返回不同启动阶段的初始化任务
- (id<UpWorkflowTask>)initializeTaskForStage:(enum UpLaunchStage)stage
{
    self.currentStage = stage;
    switch (stage) {
        case UpLaunchStageAfterPrivacy:
            return self;
        default:
            return nil;
    }
}

- (void)run
{
    switch (self.currentStage) {
        case UpLaunchStageAfterPrivacy:
            [self init3DShortcutItems];
            break;
        default:
            break;
    }
}

// 根据shortcutItem对象的唯一标识跳转到对应的添加设备、消息中心、报装报修页面，并且对相应事件进行埋点
- (enum UpChainHandledResult)onPerformActionFor:(UIApplicationShortcutItem *)shortcutItem completionHandler:(void (^)(BOOL))completionHandler
{
    if ([shortcutItem.type isEqualToString:UIApplicationShortcutItemTypeAddDevice]) {
        // 添加设备
        [[UPEventTrace getInstance] trace:@"MB56789"];

        [UpVdn goToPage:@"http://uplus.haier.com/uplusapp/main/qrcodescan.html?needAuthLogin=1"
                   flag:VdnPageFlagPush
             parameters:nil
               complete:nil
                  error:nil];
    }
    else if ([shortcutItem.type isEqualToString:UIApplicationShortcutItemTypeMessageCenter]) {
        // 消息中心
        [[UPEventTrace getInstance] trace:@"MB66789"];

        [UpVdn goToPage:@"mpaas://messageCenter?needAuthLogin=1"
                   flag:VdnPageFlagPush
             parameters:nil
               complete:nil
                  error:nil];
    }
    else if ([shortcutItem.type isEqualToString:UIApplicationShortcutItemTypeInstallationRepair]) {
        // 报装报修
        [[UPEventTrace getInstance] trace:@"MB76789"];

        [UpVdn goToPage:@"https://service.haiersmarthomes.com/app/index.html?type=T02&code=303&push=outSide&needAuthLogin=1#/userServices"
                   flag:VdnPageFlagPush
             parameters:nil
               complete:nil
                  error:nil];
    }

    if (completionHandler)
        completionHandler(YES);

    return UpChainHandledResultTrue;
}

// 配置需要当3DTouch按压显示的快捷shortcutItem
- (void)init3DShortcutItems
{
    UIApplicationShortcutItem *addDeviceItem = [[UIApplicationShortcutItem alloc] initWithType:UIApplicationShortcutItemTypeAddDevice localizedTitle:@"添加设备" localizedSubtitle:nil icon:[UIApplicationShortcutIcon iconWithTemplateImageName:@"UplusSpecial.bundle/touch_addDevice"] userInfo:nil];

    UIApplicationShortcutItem *messageCenterItem = [[UIApplicationShortcutItem alloc] initWithType:UIApplicationShortcutItemTypeMessageCenter localizedTitle:@"消息中心" localizedSubtitle:nil icon:[UIApplicationShortcutIcon iconWithTemplateImageName:@"UplusSpecial.bundle/touch_messageCenter"] userInfo:nil];

    UIApplicationShortcutItem *installationRepairItem = [[UIApplicationShortcutItem alloc] initWithType:UIApplicationShortcutItemTypeInstallationRepair localizedTitle:@"报装报修" localizedSubtitle:nil icon:[UIApplicationShortcutIcon iconWithTemplateImageName:@"UplusSpecial.bundle/touch_installationRepair"] userInfo:nil];

    [UIApplication sharedApplication].shortcutItems = @[ addDeviceItem, messageCenterItem, installationRepairItem ];
}

@end
