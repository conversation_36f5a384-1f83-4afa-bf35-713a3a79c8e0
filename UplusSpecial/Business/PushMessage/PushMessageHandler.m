//
//  PushMessageHandler.m
//  UPInitBaseKit
//
//  Created by gump on 31/3/2021.
//

#import "PushMessageHandler.h"
#import <upuserdomain/UpUserDomainHolder.h>
#import <UPLog/UPLog.h>
#import <UPPush/UPPushMessage.h>
#import <UPPush/UPPushAlertManager.h>
#import <UPPush/UPPushStringTools.h>
#import <UPPush/UPPushManager.h>
#import <UPVDN/UPVDN.h>
#import <UPVDN/UPVDNManager.h>
#import <upnetwork/UPNetwork.h>
#import <UpTrace/UPEventTrace.h>
#import <UPPush/UPPushAlertController.h>

static NSString *const ADD_MODEL_SUCCEED = @"com.haier.uhome.uplus.bind.ADD_MODEL_SUCCEED"; //设备名称补全通知

@interface PushMessageHandler ()

@property (nonatomic, assign) BOOL isAlerting;

@end

@implementation PushMessageHandler

+ (instancetype)sharedHandler
{
    static PushMessageHandler *handler = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      handler = [[PushMessageHandler alloc] init];
    });
    return handler;
}

#pragma mark - public
/**
 *  订阅通知
 */
- (void)observeNotification
{
    NSDictionary *messageDic = [self pushMessageDic];
    NSArray *keys = messageDic.allKeys;
    for (NSString *key in keys) {
        NSString *value = messageDic[key];
        SEL sel = NSSelectorFromString(value);
        [[NSNotificationCenter defaultCenter] addObserver:self selector:sel name:key object:nil];
    }
}

#pragma mark - private
/**
 *  推送消息字典
 */
- (NSDictionary *)pushMessageDic
{
    return @{ @"DEV_WASHING_MACHINE_CLEAN" : @"boxClean:", //筒自洁
              @"MODIFY_DEVNAME" : @"refreshDeviceListDataWithCheck:", //设备改名
              @"MOVE_POSITIO" : @"refreshUserDomainData:", //设备位置移动
              @"MOVE_FAMILY" : @"refreshUserDomainData:", //设备转移家庭
              @"FAMILY_DATA_CHANGE" : @"refreshUserDomainData:", //家庭数据变化
              @"MOVEOUT" : @"refreshUserDomainData:", //设备移出家庭
              @"UNBIND" : @"refreshUserDomainData:", //设备解绑
              ADD_MODEL_SUCCEED : @"refreshDeviceListData", //设备名称补全通知
              @"UPLUS_USER_LOG_OUT_OF_DEVICE" : @"logOutOfDevice:", // 退出登录
    };
}

/**
 *  筒自洁
 */
- (void)boxClean:(NSNotification *)notification
{
    UPPushMessage *message = [notification object];
    if (!message) {
        return;
    }
    [UPPushAlertManager.instance showAlertWithMessage:message
                                         actionHandle:^(NSInteger callID) {
                                           if (!message.messageBody.extraData) {
                                               return;
                                           }
                                           if (message.messageBody.extraData.api && message.messageBody.extraData.api.callID == callID) {
                                               NSDictionary *dic = message.messageBody.extraData.api.params;
                                               NSString *mac = [dic objectForKey:@"mac"];
                                               [self gotoDeviceDetailPage:mac];
                                           }
                                           else if (message.messageBody.extraData.pages) {
                                               NSString __block *url;
                                               [message.messageBody.extraData.pages enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                                                 if (obj.callID == callID) {
                                                     url = obj.url;
                                                     *stop = YES;
                                                 }
                                               }];
                                               if (url) {
                                                   [[UPVDNManager shareManager]
                                                           .vdnDomain goToPage:url
                                                                          flag:VdnPageFlagPush
                                                                    parameters:@{ @"hidesBottomBarWhenPushed" : @"1" }
                                                                      complete:nil
                                                                         error:nil];
                                               }
                                           }
                                         }];
}

/**
 *  刷新userdomain数据
 */
- (void)refreshUserDomainData:(NSNotification *)notification
{
    if ([self isMessageNeedRefreshFamily:notification]) {
        [self refreshFamilyList];
        return;
    }

    if (![self isMessageNeedProcess:notification]) {
        return;
    }
    UpUserDomain *userDomain = [UpUserDomainHolder instance].userDomain;
    [userDomain refreshUser:^(UserDomainSampleResult *_Nonnull result) {
      if (result.success) {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh userDomain success", __PRETTY_FUNCTION__, __LINE__);
      }
      else {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh userDomain fail", __PRETTY_FUNCTION__, __LINE__);
      }
    }];
}

/**
 *  跳转设备详情页
 */
- (void)gotoDeviceDetailPage:(NSString *)mac
{
    if (!mac) {
        return;
    }
    [[UPVDNManager shareManager]
            .vdnDomain goToPage:@"http://uplus.haier.com/uplusapp/DeviceList/DetailView.html"
        flag:VdnPageFlagPush
        parameters:@{ @"deviceId" : mac }
        complete:^(NSDictionary *data) {
        }
        error:^(NSError *error){
        }];
}

/**
 *  刷新devicelist数据并检测message中的apiType
 */
- (void)refreshDeviceListDataWithCheck:(NSNotification *)notification
{
    if (![self isMessageNeedProcess:notification]) {
        return;
    }
    [self refreshDeviceListData];
}

/**
 *  刷新devicelist数据
 */
- (void)refreshDeviceListData
{
    UpUserDomain *userDomain = [UpUserDomainHolder instance].userDomain;
    [userDomain.user refreshDeviceList:^(UserDomainSampleResult *_Nonnull result) {
      if (result.success) {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh devicelist success", __PRETTY_FUNCTION__, __LINE__);
      }
      else {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh devicelist fail", __PRETTY_FUNCTION__, __LINE__);
      }
    }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh devicelist fail", __PRETTY_FUNCTION__, __LINE__);
        }];
}

- (void)refreshFamilyList
{
    UpUserDomain *userDomain = [UpUserDomainHolder instance].userDomain;
    [userDomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull result) {
      if (result.success) {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh familyList success", __PRETTY_FUNCTION__, __LINE__);
      }
      else {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh familyList fail", __PRETTY_FUNCTION__, __LINE__);
      }
    }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UplusSpecial", @"%s[%d]pushmessage refresh familyList fail", __PRETTY_FUNCTION__, __LINE__);
        }];
}

- (BOOL)isMessageNeedRefreshFamily:(NSNotification *)notification
{
    UPPushMessage *message = [notification object];
    if (![message isKindOfClass:[UPPushMessage class]]) {
        return NO;
    }

    NSString *apiType = message.messageBody.extraData.api.apiType;
    NSArray *needRefreshFamilyType = @[
        @"FAMILY_ROOM_CHANGE", // 家庭房间变更
        @"FAMILY_FLOOR_CHANGE", // 家庭楼层变更
        @"IOT_FAMILY_MEMBER_TYPE_UPDATE_FOR_CREATOR", // 家庭成员类型变更
        @"IOT_FAMILY_MEMBER_TYPE_UPDATE_FOR_CURRENT", // 家庭成员类型变更
        @"IOT_FAMILY_MEMBER_LEAVE", //成员退出家庭
        @"IOT_REMOVE_FAMILY_MEMBER" //管理员移除成员
    ];

    if ([needRefreshFamilyType containsObject:apiType]) {
        return YES;
    }

    return NO;
}

/**
 *  判断message中的apiType是否需要处理
 */
- (BOOL)isMessageNeedProcess:(NSNotification *)notification
{
    UPPushMessage *message = [notification object];
    if (![message isKindOfClass:[UPPushMessage class]]) {
        return NO;
    }

    NSString *apiType = message.messageBody.extraData.api.apiType;
    NSArray *needProcessType = @[
        @"FAMILY_DEVICE_MODIFY_DEVNAME", //设备修改名称
        @"FAMILY_DEVICE_MOVE_POSITION", //设备移动位置
        @"IOT_TRANSFER_DEVICE_TO_FAMILY", //设备转移家庭
        @"FAMILY_DEVICE_MOVEOUT", //设备移出家庭
        @"FAMILY_DEVICE_UNBIND", //设备解除绑定
        @"SHARE_DEVICE_TO_FAMILY", //分享设备到家庭
        @"ADD_FAMILY_MEMBER", //新成员加入家庭
        @"DELETE_FAMILY_SHARE_DEVICE", //取消设备分享,例如灯组设备解散后，会收到
    ];
    if ([needProcessType containsObject:apiType]) {
        return YES;
    }

    UPLogInfo(@"UplusSpecial", @"%s[%d]no need process apitype:%@", __PRETTY_FUNCTION__, __LINE__, apiType);
    return NO;
}

// 退出登录
- (void)logOutOfDevice:(NSNotification *)notification
{
    UPPushMessage *message = notification.object;
    if (!message) {
        return;
    }

    if (UPPush_isEmptyString(message.messageName) || ![message.messageName isEqualToString:@"UPLUS_USER_LOG_OUT_OF_DEVICE"]) {
        return;
    }

    if (!message.messageBody.extraData.api.params || message.messageBody.extraData.api.params.count == 0) {
        return;
    }

    [[UPPushManager instance]
            .push unregisterPush:^(BOOL result) {
      [[UpUserDomainHolder instance]
              .userDomain logOut:^(UserDomainSampleResult *_Nonnull result){
      }];
    }];

    NSString *content =
        [message.messageBody.extraData.api.params objectForKey:@"content"];
    NSString *title =
        [message.messageBody.extraData.api.params objectForKey:@"title"];
    if (![self canAlertForContent:content title:title]) {
        return;
    }

    UPPushAlertModel *alertModel = [[UPPushAlertModel alloc] init];
    alertModel.title = title;
    alertModel.message = content;
    UPPushAlertActionModel *actionModel = [[UPPushAlertActionModel alloc] init];
    actionModel.title = @"重新登录";
    __weak typeof(self) weakSelf = self;
    actionModel.actionHandler = ^{
      weakSelf.isAlerting = NO;
      [weakSelf gotoLoginPage];
    };
    NSMutableArray<UPPushAlertActionModel *> *actionModelArray = [NSMutableArray array];
    [actionModelArray addObject:actionModel];
    alertModel.otherActions = actionModelArray;
    self.isAlerting = YES;
    dispatch_async(dispatch_get_main_queue(), ^{
      UPPushAlertController *alertController = [[UPPushAlertController alloc] initWithModel:alertModel];
      dispatch_after(
          dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)),
          dispatch_get_main_queue(), ^{
            if (alertController) {
                [alertController show:^{
                  [[UPEventTrace getInstance] trace:@"MB38829"];
                }];
            }
          });
    });
}

- (BOOL)canAlertForContent:(NSString *)content title:(NSString *)title
{
    if (self.isAlerting) {
        return NO;
    }
    if (UPPush_isEmptyString(content) || UPPush_isEmptyString(title)) {
        return NO;
    }
    return YES;
}

- (void)gotoLoginPage
{
    [[UPEventTrace getInstance] trace:@"MB37120"];
    [[UPVDNManager shareManager]
            .vdnDomain goToPage:@"mpaas://usercenter?page_anim=anim_pop"
        flag:VdnPageFlagPresent
        parameters:@{
            @"hidesBottomBarWhenPushed" : @"1"
        }
        complete:^(NSDictionary *data) {
        }
        error:^(NSError *error){

        }];
}

@end
