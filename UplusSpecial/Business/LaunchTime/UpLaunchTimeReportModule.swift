//
//  UpLaunchTimeReportModule.swift
//  UplusSpecial
//
//  Created by whenwe on 2023/11/15.
//

import Foundation
import UPCore
import UpTrace
import uplog
import LaunchKitCommon

let NSNotificationFlutterPackageDidLaunch = NSNotification.Name("flutter_package_did_launch")
let UPFunctionToggleLaunchTimeReportKey = "LaunchTime.launchTimeReport"
let UpLaunchTimeReportGIOKey = "MB35485"
let UpLaunchTimeCurrentVersion = "UpLaunchTimeCurrentVersion"
let UpAppForegroundChangeGIOKey = "MB35846" //切前后台埋点 0-后台，1-前台
///flutter 设备卡片显示
let NSNotificationFlutterPackageDeviceDidLaunch = NSNotification.Name("flutter_package_device_did_launch")


@objc public class UpLaunchTimeReportModule: NSObject, ModuleProtocol, WorkflowTask  {
    
    // 更新版本后第一次启动
    var isFirstLaunch = false
  
    public func initializeTask(for stage: LaunchStage) -> (any WorkflowTask)? {
      return stage == .system ? self : nil
    }
    
    public func run() {
      // 先判断是否是首次启动
      self.isFirstLaunchInCurrentVersion()
      // 再处理监听逻辑
      NotificationCenter.default.addObserver(self, selector: #selector(flutterPackageDidRender), name: NSNotificationFlutterPackageDidLaunch, object: nil)
      NotificationCenter.default.addObserver(self, selector: #selector(flutterPackageDeviceDidRender), name: NSNotificationFlutterPackageDeviceDidLaunch, object: nil)

    }
  
    public func onAppBackground() {
      UPEventTrace.getInstance().trace(UpAppForegroundChangeGIOKey, withVariable: ["value":"0"])
    }
    
    public func onAppForeground() {
      UPEventTrace.getInstance().trace(UpAppForegroundChangeGIOKey, withVariable: ["value":"1"])
    }
}

fileprivate extension UpLaunchTimeReportModule {
    
    /**
     收到通知后延迟3秒再执行上报逻辑
     - Discussion:
        1. 收到通知后，根据配置文件的配置，可能会有移除启动页的操作；因此立即上报的话会导致获取不到启动页移除时的时间戳
        2. 延迟3秒后，启动页早已经被移除（启动页被创建后无论如何在2秒内都会被移除），因此可以上报
     */
    @objc func flutterPackageDidRender() {
        
        //移除监听，防止多次上报（渲染广告页、渲染底部tab都会接到此通知；若等上报完再移除，可能会导致数据被上报两次）
        NotificationCenter.default.removeObserver(self, name: NSNotificationFlutterPackageDidLaunch, object: nil)
        
        let launchTimeAnalyze = UpLaunchTimeAnalyze.shareInstance()
        //上报冷启动结束时机
        let sectionNameAndPositions = [
            UpLaunchTimeAnalyzeSectionSplashScreen: false,
            UpLaunchTimeAnalyzeSectionSplashScreenRemove: false,
            UpLaunchTimeAnalyzeSectionFlutterTabDidRendered: false,
            UpLaunchTimeAnalyzeSectionFlutterDeviceDidRendered:true
        ]
        launchTimeAnalyze.cacheTimestamp(withSectionNameAndPositions: sectionNameAndPositions)
        
    }
    
    @objc func flutterPackageDeviceDidRender() {
        
        //移除监听，防止多次上报
        NotificationCenter.default.removeObserver(self, name: NSNotificationFlutterPackageDeviceDidLaunch, object: nil)
        
        let launchTimeAnalyze = UpLaunchTimeAnalyze.shareInstance()
        
        // 读取配置文件属性，是否需要上报
        let toggle = UPFunctionToggle.shareInstance().toggleValue(forKey: UPFunctionToggleLaunchTimeReportKey)
        let isReport = toggle as? Bool ?? false
        //当配置开关为true且标志位shouldAnalyze为true时，才会进行上报；否则不上报，移除监听
        if !(isReport && launchTimeAnalyze.shouldAnalyze) {
            return
        }
        
        //启动结束时间
        let sectionNameAndPositions = [
            UpLaunchTimeAnalyzeSectionAll: false,
            UpLaunchTimeAnalyzeSectionFlutterDeviceDidRendered: false
        ]
        
        launchTimeAnalyze.cacheTimestamp(withSectionNameAndPositions: sectionNameAndPositions)
        
        // 延迟3秒再上报
        DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
            
            #if DEBUG
            #else
            // 上报到GIO
            self.launchTimeGioReport()
            #endif
            
            //上报完成，将标志位shouldAnalyze设为false
            launchTimeAnalyze.shouldAnalyze = false
        }
        
    }
    
    
    
    //处理数据并上报到GIO
    func launchTimeGioReport() {
        let launchTimeAnalyze = UpLaunchTimeAnalyze.shareInstance()
        if !launchTimeAnalyze.shouldReportWithNoAbnormalData() {
            return
        }
        let detailJsons: Array = launchTimeAnalyze.detailJsons
        var gioValue: Dictionary = Dictionary<String, Any>()
        //组装埋点详细数据分段参数data0~data9
        for (index, dataComponent) in detailJsons.enumerated() {
            let key = "data_\(index)"
            gioValue[key] = dataComponent
        }

        //组装其它埋点参数
        let launchTime = launchTimeAnalyze.launchTime
        gioValue["launch_time_num"] = Int(launchTime)
        gioValue["launch_time"] = launchTime
        gioValue["launch_interval"] = launchTimeAnalyze.timeInterval
        gioValue["launch_preview"] = launchTimeAnalyze.previewJson
        gioValue["devicelist_time"] = Int(launchTimeAnalyze.deviceDidRenderedTime)

        // 是否开启了日志
        let logLevel = UPLogInjection.getInstance().upLog.getLevel()
        let logEnable = logLevel == UPLogLevelVerbose || logLevel == UPLogLevelDebug
        // 是否开启了全量日志
        let logFullEnable = UPLogInjection.getInstance().upLog.getFullLogsStatus()
        // 日志和全量日志只要开启了一个，就将LogSwtich设为yes（和安卓同步）
        gioValue["LogSwtich"] = (logFullEnable || logEnable) ? "yes" : "no"
        //首次启动标识
        gioValue["isFirst"] = isFirstLaunch ? "yes" : "no"
        
        UPEventTrace.getInstance().trace(UpLaunchTimeReportGIOKey, withVariable: gioValue)
    }
    
    // 判断是否是首次启动
    func isFirstLaunchInCurrentVersion() {
        // 获取当前版本
        let curShortVer: String = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
        let curVer: String = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? ""
        let curVersion: String = curShortVer + "-" + curVer
        
        // 获取存储的版本
        let lastVersinon: String = UserDefaults.standard.string(forKey: UpLaunchTimeCurrentVersion) ?? ""
        // 判断值是否相等，若不相等，认为是首次启动，修改isFirstLaunch为true，并将新的版本号存储到userDefault中
        if curVersion != lastVersinon {
            isFirstLaunch = true
            UserDefaults.standard.set(curVersion, forKey: UpLaunchTimeCurrentVersion)
        }
    }
}
