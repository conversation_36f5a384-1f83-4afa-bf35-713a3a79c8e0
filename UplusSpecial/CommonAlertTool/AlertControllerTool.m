//
//  AlertControllerTool.m
//  UplusSpecial
//
//  Created by 飛 on 2025/6/17.
//

#import "AlertControllerTool.h"
#import "UPUpgradeAlertController.h"

static NSTimeInterval _lastAlertTimestamp = 0;

@interface AlertControllerTool ()
@end

@implementation AlertControllerTool

+ (void)showAlertController:(NSString *)title
                    content:(NSString *)content
                cancelTitle:(NSString *)cancelTitle
               confirmTitle:(NSString *)confirmTitle
               falseHandler:(void (^)(void))falseHandler
            completeHandler:(void (^)(void))completeHandler
{
    // 防抖：5秒内不允许重复弹窗
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - _lastAlertTimestamp < 5) {
        return;
    }
    _lastAlertTimestamp = currentTime;

    UIColor *blackColor =
        [UIColor colorWithRed:0.067
                        green:0.067
                         blue:0.067
                        alpha:1.0];
    UIColor *grayColor = [UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
    UPUpgradeAlertController *alertController =
        [UPUpgradeAlertController alertControllerWithTitle:title
                                             remindMessage:@""
                                                   message:content];
    alertController.titleLabel.textColor = blackColor;
    alertController.titleLabel.font =
        [UIFont fontWithName:@"PingFangSC-Medium"
                        size:17.0];
    alertController.contentLabel.textColor = grayColor;
    alertController.contentLabel.font =
        [UIFont fontWithName:@"PingFangSC-Regular"
                        size:14];

    __weak typeof(self) weakSelf = self;
    UPUpgradeAlertAction *cancel = [UPUpgradeAlertAction
        actionWithTitle:cancelTitle
                  style:UPUpgradeAlertActionStyleDefault
        completeHandler:^(UPUpgradeAlertAction *_Nonnull action) {
          __strong typeof(self) strongSelf = weakSelf;
          if (falseHandler) {
              falseHandler();
          }

          [strongSelf removeAlertControllerAnimated:YES alert:alertController.view];
        }];
    [cancel.actionButton setTitleColor:blackColor forState:UIControlStateNormal];
    cancel.actionButton.titleLabel.font =
        [UIFont fontWithName:@"PingFangSC-Medium"
                        size:16];

    UPUpgradeAlertAction *confirm = [UPUpgradeAlertAction
        actionWithTitle:confirmTitle
                  style:UPUpgradeAlertActionStyleDefault
        completeHandler:^(UPUpgradeAlertAction *_Nonnull action) {
          __strong typeof(self) strongSelf = weakSelf;
          if (completeHandler) {
              completeHandler();
          }
          [strongSelf removeAlertControllerAnimated:YES alert:alertController.view];
        }];
    [confirm.actionButton
        setTitleColor:[UIColor colorWithRed:1 green:1 blue:1 alpha:1.0]
             forState:UIControlStateNormal];
    confirm.actionButton.titleLabel.font =
        [UIFont fontWithName:@"PingFangSC-Medium"
                        size:16];
    [alertController addAction:cancel];
    [alertController addAction:confirm];

    alertController.view.frame = [UIScreen mainScreen].bounds;
    alertController.view.alpha = 0;
    alertController.view.tag = 9999;


    dispatch_async(dispatch_get_main_queue(), ^{
      // 获取当前 alert 个数（tag == 9999）
      NSInteger count = [[[self getApplicationKeyWindow]
                              .subviews filteredArrayUsingPredicate:
                                            [NSPredicate predicateWithFormat:@"tag == 9999"]] count];
      // 只有第一个 alert 有背景遮罩
      alertController.view.backgroundColor = (count == 0) ? [UIColor colorWithWhite:0 alpha:0.25] : [UIColor colorWithWhite:0 alpha:0];

      [[self getApplicationKeyWindow] addSubview:alertController.view];
      [UIView animateWithDuration:0.25
                       animations:^{
                         alertController.view.alpha = 1;
                       }];
    });
}

+ (void)didTapBackgroundToDismiss:(UIControl *)sender
{
    UIView *alertView = sender.superview;
    [self removeAlertControllerAnimated:YES alert:alertView];
}

/// 移除弹窗控制器
+ (void)removeAlertControllerAnimated:(BOOL)animated alert:(UIView *)alert
{
    if (alert == nil) {
        return;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
      if (animated) {
          [UIView animateWithDuration:0.25
              animations:^{
                alert.alpha = 0;
              }
              completion:^(BOOL finished) {
                [alert removeFromSuperview];
              }];
      }
      else {
          [alert removeFromSuperview];
      }
    });
}
+ (UIWindow *)getApplicationKeyWindow
{
    __block UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (keyWindow == nil) {
        [[UIApplication sharedApplication]
                .windows
            enumerateObjectsUsingBlock:^(__kindof UIWindow *_Nonnull obj,
                                         NSUInteger idx, BOOL *_Nonnull stop) {
              if (obj.isKeyWindow) {
                  keyWindow = obj;
                  *stop = YES;
              }
            }];
    }
    return keyWindow;
}
@end
