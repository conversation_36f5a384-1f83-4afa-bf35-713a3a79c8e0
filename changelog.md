# 0.4.3
## TASK:
> -x_haieruplus-10848 iOS_H5容器自研-iOS版本<br /> 
> -x_haieruplus-10962 iOS_智家APP开启全局module<br /> 
## BUG:
> -x_haieruplus-11831 【验收环境】【iOS】绑定二级页，有自发现设备，APP崩溃<br /> 
> -x_haieruplus-12521 【小优-ios】附近设备页面扫描不到设备时，小优冒泡背景色与安卓不一致<br /> 

# 0.4.0
## TASK:
> -x_haieruplus-5548 iOS_YH_220922_000832_智家APP中增加：游客模式<br /> 
> -x_haieruplus-7080 iOS_mainbox库平台化整改<br /> 
> -x_haieruplus-7124 iOS_一站式接入平台能力可选<br /> 
> -x_haieruplus-7267 iOS_平台化剩余差异抹除<br /> 
> -x_haieruplus-7067 iOS_未实现方法运行时Crash防护<br /> 
> -x_haieruplus-7092 iOS_Base库平台化整改<br /> 
> -x_haieruplus-7088 iOS_iOS移除mPaaS框架<br /> 
> -x_haieruplus-7142 iOS_XX_221230_000869端上在推送库中开放pushid字段的API接口<br /> 
> -x_haieruplus-8896 iOS_SH_20230201_000887 服务页面下线小优助手<br /> 
## BUG:
> -x_haieruplus-8797 ios启动App未同意隐私前无法连接GIO<br /> 
> -x_haieruplus-8803 [iOS移除mPaaS框架] 日志页不同手机系统显示页面不一致<br /> 

# 0.3.0
## TASK:
> -x_haieruplus-4706 iOS_7.18.0-埋点平台化<br /> 
> -x_haieruplus-4772 iOS_推送平台化<br /> 
> -x_haieruplus-4730 iOS_关于我们页面进行版本检测上增加上传userID<br /> 

# 0.2.0
## TASK:
> -x_haieruplus-3208 iOS_基础组件融合App修改固化<br /> 
> -x_haieruplus-3313 iOS_iOS主线融合App修改固化<br /> 

# 0.1.0
## TASK:
> -x_haieruplus-1336 iOS_极光一键登录平台化开发<br /> 
